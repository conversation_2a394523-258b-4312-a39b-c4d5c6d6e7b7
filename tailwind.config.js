import defaultTheme from 'tailwindcss/defaultTheme';
import typography from '@tailwindcss/typography';
import { addDynamicIconSelectors } from '@iconify/tailwind';
import daisyui from 'daisyui';

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: 'class',
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.{vue,js,ts,jsx,tsx}',
    ],
    theme: {
        extend: {
            fontFamily: {
                sans: ['Instrument Sans', ...defaultTheme.fontFamily.sans],
            },
            borderRadius: {
                lg: 'var(--radius)',
                md: 'calc(var(--radius) - 2px)',
                sm: 'calc(var(--radius) - 4px)',
            },
            colors: {
                'text': '#030712',
                'text-light': '#1f2937',
                'text-lighter': '#6b7280',
                'background': '#ffffff',
                'primary': '#4f46e5',
                'primary-hover': '#4338ca',
                'primary-text': '#ffffff',
                'secondary': '#18181b',
                'secondary-hover': '#1b1b21',
                'secondary-text': '#ffffff',
                'accent': '#2dd4bf',
                'accent-hover': '#14b8a6',
                'accent-text': '#ffffff',
                'destructive': '#ef4444',
                'destructive-hover': '#dc2626',
                'destructive-text': '#ffffff',
                'border': '#e5e7eb',
                'input': '#e5e7eb',
                'ring': '#e5e7eb',
                'sidebar-border': 'rgba(226, 232, 240, 0.1)',
                dark: {
                    'text': '#f9fafb',
                    'text-light': '#f3f4f6',
                    'text-lighter': '#d1d5db',
                    'background': '#0f172a',
                    'primary': '#4f46e5',
                    'primary-hover': '#4338ca',
                    'primary-text': '#ffffff',
                    'secondary': '#27272a',
                    'secondary-hover': '#323238',
                    'secondary-text': '#ffffff',
                    'accent': '#2dd4bf',
                    'accent-hover': '#14b8a6',
                    'accent-text': '#ffffff',
                    'destructive': '#ef4444',
                    'destructive-hover': '#dc2626',
                    'destructive-text': '#ffffff',
                    'border': '#1f2937',
                    'input': '#374151',
                    'ring': '#1f2937',
                    'sidebar-border': 'rgba(51, 65, 85, 0.5)',
                },
                card: {
                    DEFAULT: 'hsl(var(--card))',
                    foreground: 'hsl(var(--card-foreground))',
                },
                popover: {
                    DEFAULT: 'hsl(var(--popover))',
                    foreground: 'hsl(var(--popover-foreground))',
                },
                muted: {
                    DEFAULT: 'hsl(var(--muted))',
                    foreground: 'hsl(var(--muted-foreground))',
                },
                chart: {
                    1: 'hsl(var(--chart-1))',
                    2: 'hsl(var(--chart-2))',
                    3: 'hsl(var(--chart-3))',
                    4: 'hsl(var(--chart-4))',
                    5: 'hsl(var(--chart-5))',
                },
                sidebar: {
                    DEFAULT: 'hsl(var(--sidebar-background))',
                    foreground: 'hsl(var(--sidebar-foreground))',
                    primary: 'hsl(var(--sidebar-primary))',
                    'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
                    accent: 'hsl(var(--sidebar-accent))',
                    'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
                    border: 'hsl(var(--sidebar-border))',
                    ring: 'hsl(var(--sidebar-ring))',
                },
            },
            borderColor: {
                DEFAULT: '#e5e7eb',
            },
            boxShadow: {
                DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
            },
            width: {
                sidebar: '16rem',
                'sidebar-sm': '4rem',
            },
            height: {
                header: '4rem',
            },
            animation: {
                'accordion-down': 'accordion-down 0.2s ease-out',
                'accordion-up': 'accordion-up 0.2s ease-out',
            },
            keyframes: {
                'accordion-down': {
                    from: { height: 0 },
                    to: { height: 'var(--radix-accordion-content-height)' },
                },
                'accordion-up': {
                    from: { height: 'var(--radix-accordion-content-height)' },
                    to: { height: 0 },
                },
            },
        },
    },
    plugins: [
        typography,
        addDynamicIconSelectors(),
        daisyui,
    ],
    daisyui: {
        themes: [
            {
                light: {
                    "primary": "#4f46e5",
                    "primary-content": "#ffffff",
                    "secondary": "#18181b",
                    "secondary-content": "#ffffff",
                    "accent": "#2dd4bf",
                    "accent-content": "#ffffff",
                    "neutral": "#f3f4f6",
                    "neutral-content": "#1f2937",
                    "base-100": "#ffffff",
                    "base-200": "#f9fafb",
                    "base-300": "#f3f4f6",
                    "base-content": "#1f2937",
                    "info": "#3abff8",
                    "success": "#22c55e",
                    "warning": "#f59e0b",
                    "error": "#ef4444",
                    "--rounded-box": "0.5rem",
                    "--rounded-btn": "0.5rem",
                    "--rounded-badge": "0.5rem",
                    "--btn-text-case": "none",
                }
            },
            {
                dark: {
                    "primary": "#4f46e5",
                    "primary-content": "#ffffff",
                    "secondary": "#27272a",
                    "secondary-content": "#ffffff",
                    "accent": "#2dd4bf",
                    "accent-content": "#ffffff",
                    "neutral": "#1f2937",
                    "neutral-content": "#f3f4f6",
                    "base-100": "#0f172a",
                    "base-200": "#1e293b",
                    "base-300": "#334155",
                    "base-content": "#f3f4f6",
                    "info": "#3abff8",
                    "success": "#22c55e",
                    "warning": "#f59e0b",
                    "error": "#ef4444",
                    "--rounded-box": "0.5rem",
                    "--rounded-btn": "0.5rem",
                    "--rounded-badge": "0.5rem",
                    "--btn-text-case": "none",
                }
            },
        ],
        darkTheme: "dark",
    },
};
