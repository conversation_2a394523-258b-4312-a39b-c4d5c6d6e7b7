<?php

namespace Tests\Feature\Commands;

use App\Models\ServiceReminder;
use App\Models\Vehicle;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Tests\TestCase;

class CreateRecurringServiceRemindersTest extends TestCase
{
    use RefreshDatabase;

    public function test_command_creates_recurring_reminders(): void
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();
        
        // Erstelle ein Fahrzeug
        $vehicle = Vehicle::factory()->create([
            'user_id' => $user->id,
            'mileage' => 50000,
        ]);
        
        // Erstelle eine abgeschlossene Erinnerung mit Wiederholungseinstellungen
        $reminder = ServiceReminder::create([
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Ölwechsel',
            'description' => 'Regelmäßiger Ölwechsel',
            'due_date' => Carbon::now()->subDays(15),
            'status' => 'completed',
            'priority' => 'medium',
            'repeat_interval' => 6,
            'repeat_unit' => 'months',
        ]);
        
        // Führe den Befehl aus
        $this->artisan('service-reminders:create-recurring')
             ->expectsOutput('Prüfe auf abgeschlossene Serviceerinnerungen mit Wiederholungseinstellungen...')
             ->assertSuccessful();
        
        // Überprüfe, ob eine neue Erinnerung erstellt wurde
        $this->assertDatabaseHas('service_reminders', [
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Ölwechsel',
            'status' => 'pending',
        ]);
        
        // Überprüfe, ob die ursprüngliche Erinnerung als verarbeitet markiert wurde
        $this->assertNotNull(ServiceReminder::find($reminder->id)->processed_at);
    }
    
    public function test_command_ignores_already_processed_reminders(): void
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();
        
        // Erstelle ein Fahrzeug
        $vehicle = Vehicle::factory()->create([
            'user_id' => $user->id,
        ]);
        
        // Erstelle eine bereits verarbeitete Erinnerung
        $reminder = ServiceReminder::create([
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Ölwechsel',
            'description' => 'Regelmäßiger Ölwechsel',
            'due_date' => Carbon::now()->subDays(15),
            'status' => 'completed',
            'priority' => 'medium',
            'repeat_interval' => 6,
            'repeat_unit' => 'months',
            'processed_at' => Carbon::now(),
        ]);
        
        // Anzahl der Erinnerungen vor der Ausführung
        $remindersCountBefore = ServiceReminder::count();
        
        // Führe den Befehl aus
        $this->artisan('service-reminders:create-recurring')
             ->expectsOutput('Prüfe auf abgeschlossene Serviceerinnerungen mit Wiederholungseinstellungen...')
             ->assertSuccessful();
        
        // Überprüfe, ob keine neue Erinnerung erstellt wurde
        $this->assertEquals($remindersCountBefore, ServiceReminder::count());
    }
    
    public function test_command_creates_reminders_with_kilometer_intervals(): void
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();
        
        // Erstelle ein Fahrzeug
        $vehicle = Vehicle::factory()->create([
            'user_id' => $user->id,
            'mileage' => 50000,
        ]);
        
        // Erstelle eine abgeschlossene Erinnerung mit Kilometerwert-basierter Wiederholung
        $reminder = ServiceReminder::create([
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Ölwechsel',
            'description' => 'Regelmäßiger Ölwechsel',
            'due_mileage' => 50000,
            'status' => 'completed',
            'priority' => 'medium',
            'repeat_interval' => 10000,
            'repeat_unit' => 'km',
        ]);
        
        // Führe den Befehl aus
        $this->artisan('service-reminders:create-recurring')
             ->expectsOutput('Prüfe auf abgeschlossene Serviceerinnerungen mit Wiederholungseinstellungen...')
             ->assertSuccessful();
        
        // Überprüfe, ob eine neue Erinnerung mit erhöhtem Kilometerstand erstellt wurde
        $this->assertDatabaseHas('service_reminders', [
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Ölwechsel',
            'status' => 'pending',
            'due_mileage' => 60000, // 50000 + 10000
        ]);
    }
} 