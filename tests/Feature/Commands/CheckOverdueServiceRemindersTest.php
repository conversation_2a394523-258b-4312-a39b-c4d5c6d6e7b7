<?php

namespace Tests\Feature\Commands;

use App\Models\ServiceReminder;
use App\Models\Vehicle;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Tests\TestCase;

class CheckOverdueServiceRemindersTest extends TestCase
{
    use RefreshDatabase;

    public function test_command_marks_overdue_reminders_by_date(): void
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();
        
        // Erstelle ein Fahrzeug
        $vehicle = Vehicle::factory()->create([
            'user_id' => $user->id,
            'mileage' => 50000,
        ]);
        
        // Erstelle eine ausstehende Erinnerung mit vergangenem Datum
        $reminder = ServiceReminder::create([
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Test Service',
            'description' => 'Test Beschreibung',
            'due_date' => Carbon::now()->subDays(5),
            'status' => 'pending',
            'priority' => 'medium',
        ]);
        
        // <PERSON>ühre den Befehl aus
        $this->artisan('service-reminders:check-overdue')
             ->expectsOutput('Prüfe auf überfällige Serviceerinnerungen...')
             ->assertSuccessful();
        
        // Überprüfe, ob der Status aktualisiert wurde
        $this->assertDatabaseHas('service_reminders', [
            'id' => $reminder->id,
            'status' => 'overdue',
        ]);
    }

    public function test_command_marks_overdue_reminders_by_mileage(): void
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();
        
        // Erstelle ein Fahrzeug mit einem Kilometerstand, der über dem Grenzwert liegt
        $vehicle = Vehicle::factory()->create([
            'user_id' => $user->id,
            'mileage' => 55000,
        ]);
        
        // Erstelle eine ausstehende Erinnerung mit einem Kilometerstand-Grenzwert
        $reminder = ServiceReminder::create([
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Test Service',
            'description' => 'Test Beschreibung',
            'due_mileage' => 50000,
            'status' => 'pending',
            'priority' => 'medium',
        ]);
        
        // Führe den Befehl aus
        $this->artisan('service-reminders:check-overdue')
             ->expectsOutput('Prüfe auf überfällige Serviceerinnerungen...')
             ->assertSuccessful();
        
        // Überprüfe, ob der Status aktualisiert wurde
        $this->assertDatabaseHas('service_reminders', [
            'id' => $reminder->id,
            'status' => 'overdue',
        ]);
    }

    public function test_command_does_not_mark_non_overdue_reminders(): void
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();
        
        // Erstelle ein Fahrzeug
        $vehicle = Vehicle::factory()->create([
            'user_id' => $user->id,
            'mileage' => 45000,
        ]);
        
        // Erstelle eine ausstehende Erinnerung mit zukünftigem Datum und nicht erreichtem Kilometerstand
        $reminder = ServiceReminder::create([
            'vehicle_id' => $vehicle->id,
            'user_id' => $user->id,
            'title' => 'Test Service',
            'description' => 'Test Beschreibung',
            'due_date' => Carbon::now()->addDays(10),
            'due_mileage' => 50000,
            'status' => 'pending',
            'priority' => 'medium',
        ]);
        
        // Führe den Befehl aus
        $this->artisan('service-reminders:check-overdue')
             ->expectsOutput('Prüfe auf überfällige Serviceerinnerungen...')
             ->assertSuccessful();
        
        // Überprüfe, ob der Status unverändert ist
        $this->assertDatabaseHas('service_reminders', [
            'id' => $reminder->id,
            'status' => 'pending',
        ]);
    }
} 