<?php

namespace Tests\Feature;

use App\Models\Trip;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class FahrtenbuchTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @test */
    public function user_can_view_fahrtenbuch_page()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)
            ->get(route('fahrtenbuch.index'));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Fahrtenbuch/Index'));
    }
    
    /** @test */
    public function user_can_create_trip()
    {
        $user = User::factory()->create();
        $vehicle = Vehicle::factory()->create(['user_id' => $user->id]);
        
        $tripData = [
            'date' => now()->format('Y-m-d'),
            'vehicleId' => $vehicle->id,
            'startOdometer' => 10000,
            'endOdometer' => 10050,
            'startLocation' => 'Lonsee, Büro',
            'endLocation' => 'Ulm, Kunde XYZ',
            'purpose' => 'Kundentermin',
            'type' => 'business',
            'notes' => 'Besprechung mit Herrn Schmidt',
        ];
        
        $response = $this->actingAs($user)
            ->post(route('fahrtenbuch.store'), $tripData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('trips', [
            'user_id' => $user->id,
            'vehicle_id' => $vehicle->id,
            'start_location' => 'Lonsee, Büro',
            'end_location' => 'Ulm, Kunde XYZ',
            'purpose' => 'Kundentermin',
            'type' => 'business',
        ]);
    }
    
    /** @test */
    public function user_cannot_create_trip_with_invalid_data()
    {
        $user = User::factory()->create();
        $vehicle = Vehicle::factory()->create(['user_id' => $user->id]);
        
        // End odometer lower than start odometer (invalid)
        $tripData = [
            'date' => now()->format('Y-m-d'),
            'vehicleId' => $vehicle->id,
            'startOdometer' => 10000,
            'endOdometer' => 9950, // Invalid - lower than start
            'startLocation' => 'Lonsee, Büro',
            'endLocation' => 'Ulm, Kunde XYZ',
            'purpose' => 'Kundentermin',
            'type' => 'business',
        ];
        
        $response = $this->actingAs($user)
            ->post(route('fahrtenbuch.store'), $tripData);
        
        $response->assertSessionHasErrors('endOdometer');
        $this->assertDatabaseMissing('trips', [
            'user_id' => $user->id,
            'vehicle_id' => $vehicle->id,
            'start_odometer' => 10000,
        ]);
    }
    
    /** @test */
    public function user_can_export_fahrtenbuch_as_pdf()
    {
        $user = User::factory()->create();
        $vehicle = Vehicle::factory()->create(['user_id' => $user->id]);
        
        // Create some trips
        Trip::factory()->count(5)->create([
            'user_id' => $user->id,
            'vehicle_id' => $vehicle->id,
        ]);
        
        $response = $this->actingAs($user)
            ->get(route('fahrtenbuch.export', ['format' => 'pdf']));
        
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/pdf');
    }
    
    /** @test */
    public function user_can_export_fahrtenbuch_as_csv()
    {
        $user = User::factory()->create();
        $vehicle = Vehicle::factory()->create(['user_id' => $user->id]);
        
        // Create some trips
        Trip::factory()->count(5)->create([
            'user_id' => $user->id,
            'vehicle_id' => $vehicle->id,
        ]);
        
        $response = $this->actingAs($user)
            ->get(route('fahrtenbuch.export', ['format' => 'csv']));
        
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv');
    }
    
    /** @test */
    public function user_cannot_access_another_users_trips()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $vehicle1 = Vehicle::factory()->create(['user_id' => $user1->id]);
        
        // Create a trip for user1
        $trip = Trip::factory()->create([
            'user_id' => $user1->id,
            'vehicle_id' => $vehicle1->id,
        ]);
        
        // User2 tries to access user1's data
        $response = $this->actingAs($user2)
            ->get(route('fahrtenbuch.index'));
        
        $response->assertStatus(200);
        
        // Make sure user2 doesn't see user1's trips
        $response->assertInertia(function ($page) {
            return $page->component('Fahrtenbuch/Index')
                ->has('trips', 0);
        });
    }
} 