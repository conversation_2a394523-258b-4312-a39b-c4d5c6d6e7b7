<?php

namespace Database\Seeders;

use App\Models\Trip;
use App\Models\User;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class TripSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users who have vehicles
        $users = User::whereHas('vehicles')->get();
        
        foreach ($users as $user) {
            $vehicles = $user->vehicles;
            
            foreach ($vehicles as $vehicle) {
                $this->seedTripsByVehicle($user, $vehicle);
            }
        }
    }
    
    /**
     * Seed trips for a specific vehicle
     */
    private function seedTripsByVehicle(User $user, Vehicle $vehicle): void
    {
        // Common locations for this user
        $homeLocation = 'München, Zuhause';
        $workLocation = 'München, Büro';
        $commonLocations = [
            'München, Supermarkt',
            'München, Fitnessstudio', 
            'Starnberg, Kunde XYZ',
            'München, Arztpraxis',
            'München, Restaurant Bella Italia',
            'Augsburg, Niederlassung Nord',
        ];
        
        // Common purposes
        $businessPurposes = [
            '<PERSON>nden<PERSON><PERSON>', 
            '<PERSON><PERSON><PERSON><PERSON>ftstreffe<PERSON>', 
            '<PERSON><PERSON><PERSON> Niederlassung', 
            'Außenter<PERSON>',
            'Liefer<PERSON>',
            'Konferenz',
        ];
        
        $privatePurposes = [
            'Einkaufen', 
            'Freizeit', 
            'Arztbesuch', 
            'Sport',
            'Familienbesuch',
            'Urlaub',
        ];
        
        // Start with current odometer reading
        $currentOdometer = $vehicle->mileage ?? 10000;
        
        // Generate 30 trips in the past 3 months
        $date = Carbon::now()->subMonths(3);
        $endDate = Carbon::now();
        
        while ($date->lte($endDate)) {
            // Skip some random days
            if (rand(0, 2) === 0) {
                $date->addDay();
                continue;
            }
            
            // Commute trip to work
            if ($date->isWeekday() && rand(0, 10) > 2) {
                $distance = rand(10, 20);
                
                // Morning commute
                Trip::create([
                    'user_id' => $user->id,
                    'vehicle_id' => $vehicle->id,
                    'date' => $date->format('Y-m-d'),
                    'start_odometer' => $currentOdometer,
                    'end_odometer' => $currentOdometer + $distance,
                    'start_location' => $homeLocation,
                    'end_location' => $workLocation,
                    'purpose' => 'Arbeitsweg',
                    'type' => 'commute',
                    'notes' => null,
                ]);
                
                $currentOdometer += $distance;
                
                // Evening commute back home
                $distance = rand(10, 20);
                
                Trip::create([
                    'user_id' => $user->id,
                    'vehicle_id' => $vehicle->id,
                    'date' => $date->format('Y-m-d'),
                    'start_odometer' => $currentOdometer,
                    'end_odometer' => $currentOdometer + $distance,
                    'start_location' => $workLocation,
                    'end_location' => $homeLocation,
                    'purpose' => 'Rückweg vom Büro',
                    'type' => 'commute',
                    'notes' => null,
                ]);
                
                $currentOdometer += $distance;
            }
            
            // Add business trip sometimes
            if ($date->isWeekday() && rand(0, 10) > 6) {
                $randomLocation = $commonLocations[array_rand($commonLocations)];
                $randomPurpose = $businessPurposes[array_rand($businessPurposes)];
                $distance = rand(15, 50);
                
                Trip::create([
                    'user_id' => $user->id,
                    'vehicle_id' => $vehicle->id,
                    'date' => $date->format('Y-m-d'),
                    'start_odometer' => $currentOdometer,
                    'end_odometer' => $currentOdometer + $distance,
                    'start_location' => $workLocation,
                    'end_location' => $randomLocation,
                    'purpose' => $randomPurpose,
                    'type' => 'business',
                    'notes' => rand(0, 1) ? 'Gespräch mit Kunden über Projektfortschritt' : null,
                ]);
                
                $currentOdometer += $distance;
                
                // Return trip
                Trip::create([
                    'user_id' => $user->id,
                    'vehicle_id' => $vehicle->id,
                    'date' => $date->format('Y-m-d'),
                    'start_odometer' => $currentOdometer,
                    'end_odometer' => $currentOdometer + $distance,
                    'start_location' => $randomLocation,
                    'end_location' => $workLocation,
                    'purpose' => "Rückkehr von $randomPurpose",
                    'type' => 'business',
                    'notes' => null,
                ]);
                
                $currentOdometer += $distance;
            }
            
            // Add private trip sometimes
            if (rand(0, 10) > 7) {
                $randomLocation = $commonLocations[array_rand($commonLocations)];
                $randomPurpose = $privatePurposes[array_rand($privatePurposes)];
                $distance = rand(5, 30);
                
                Trip::create([
                    'user_id' => $user->id,
                    'vehicle_id' => $vehicle->id,
                    'date' => $date->format('Y-m-d'),
                    'start_odometer' => $currentOdometer,
                    'end_odometer' => $currentOdometer + $distance,
                    'start_location' => $homeLocation,
                    'end_location' => $randomLocation,
                    'purpose' => $randomPurpose,
                    'type' => 'private',
                    'notes' => null,
                ]);
                
                $currentOdometer += $distance;
                
                // Return trip
                Trip::create([
                    'user_id' => $user->id,
                    'vehicle_id' => $vehicle->id,
                    'date' => $date->format('Y-m-d'),
                    'start_odometer' => $currentOdometer,
                    'end_odometer' => $currentOdometer + $distance,
                    'start_location' => $randomLocation,
                    'end_location' => $homeLocation,
                    'purpose' => "Rückkehr von $randomPurpose",
                    'type' => 'private',
                    'notes' => null,
                ]);
                
                $currentOdometer += $distance;
            }
            
            // Update the vehicle's mileage with the current odometer
            $vehicle->update([
                'mileage' => $currentOdometer,
                'last_mileage_update' => $date->format('Y-m-d'),
            ]);
            
            $date->addDay();
        }
    }
} 