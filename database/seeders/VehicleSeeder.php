<?php

namespace Database\Seeders;

use App\Models\Vehicle;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class VehicleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // <PERSON><PERSON><PERSON><PERSON>, ob es <PERSON><PERSON> in der Datenbank gibt
        $users = DB::select('SELECT id FROM users LIMIT 1');
        
        if (empty($users)) {
            $this->command->info('Keine Benutzer gefunden. Erstelle einen Testbenutzer...');
            
            // Erstelle einen Testbenutzer, falls keiner existiert
            $userId = DB::table('users')->insertGetId([
                'name' => 'Test User',
                'email' => 'test' . time() . '@example.com',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $userId = $users[0]->id;
        }
        
        // Fahrzeuge erstellen
        $vehicles = [
            [
                'user_id' => $userId,
                'make' => 'BMW',
                'model' => '320d',
                'year' => 2020,
                'license_plate' => 'M-AB 123',
                'vin' => 'WBAPK73559A123456',
                'color' => 'Schwarz',
            ],
            [
                'user_id' => $userId,
                'make' => 'VW',
                'model' => 'Golf',
                'year' => 2019,
                'license_plate' => 'M-CD 456',
                'vin' => 'WVWZZZ1KZ9M123456',
                'color' => 'Silber',
            ],
            [
                'user_id' => $userId,
                'make' => 'Audi',
                'model' => 'A4',
                'year' => 2021,
                'license_plate' => 'M-EF 789',
                'vin' => 'WAUZZZ8K9NA123456',
                'color' => 'Blau',
            ],
        ];
        
        foreach ($vehicles as $vehicle) {
            Vehicle::updateOrCreate(
                ['license_plate' => $vehicle['license_plate']],
                $vehicle
            );
        }
        
        $this->command->info('Fahrzeuge wurden erfolgreich erstellt.');
    }
}
