<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_reminders', function (Blueprint $table) {
            $table->integer('repeat_interval')->nullable()->after('priority');
            $table->string('repeat_unit')->nullable()->after('repeat_interval');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_reminders', function (Blueprint $table) {
            $table->dropColumn(['repeat_interval', 'repeat_unit']);
        });
    }
};
