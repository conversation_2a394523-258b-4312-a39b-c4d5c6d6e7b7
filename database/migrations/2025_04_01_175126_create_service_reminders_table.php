<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_reminders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained(); // Wer hat die Erinnerung erstellt
            $table->string('title'); // Titel/Bezeichnung
            $table->text('description')->nullable(); // Beschreibung
            $table->date('due_date')->nullable(); // Fälligkeitsdatum
            $table->integer('due_mileage')->nullable(); // Fälligkeitskilometerstand
            $table->enum('status', ['pending', 'completed', 'overdue'])->default('pending');
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
            $table->boolean('is_recurring')->default(false); // Wiederkehrend
            $table->integer('recurring_interval')->nullable(); // Wiederholungsintervall in Tagen
            $table->integer('recurring_mileage')->nullable(); // Wiederholungsintervall in km
            $table->foreignId('maintenance_log_id')->nullable()->constrained()->nullOnDelete(); // Verknüpfung zur Wartung bei Erledigung
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_reminders');
    }
};
