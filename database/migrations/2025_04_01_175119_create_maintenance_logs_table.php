<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('maintenance_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained(); // Wer hat den Eintrag erstellt
            $table->string('title'); // Titel/Zusammenfassung
            $table->enum('type', [
                'service', // Regelmäßige Wartung
                'repair', // Reparatur
                'modification', // Modifikation/Umbau
                'purchase', // Kauf
                'sale', // Verkauf
                'insurance', // Versicherungsfall
                'inspection', // TÜV/HU
                'other' // Sonstiges
            ])->default('service');
            $table->date('date'); // Datum der Wartung
            $table->integer('mileage')->nullable(); // Kilometerstand
            $table->decimal('cost', 10, 2)->nullable(); // Kosten
            $table->text('description')->nullable(); // Detaillierte Beschreibung
            $table->string('location')->nullable(); // Ort/Werkstatt
            $table->string('invoice_number')->nullable(); // Rechnungsnummer
            $table->boolean('is_recurring')->default(false); // Wiederkehrende Wartung
            $table->integer('recurring_interval')->nullable(); // Wiederholungsintervall in Tagen
            $table->integer('recurring_mileage')->nullable(); // Wiederholungsintervall in km
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('maintenance_logs');
    }
};
