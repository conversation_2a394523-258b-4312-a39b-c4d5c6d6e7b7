<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attachments', function (Blueprint $table) {
            $table->id();
            $table->morphs('attachable'); // Polymorpher Beziehungstyp (kann an Vehicle oder MaintenanceLog angehängt werden)
            $table->foreignId('user_id')->constrained(); // Wer hat den Anhang hochgeladen
            $table->string('filename'); // Dateiname
            $table->string('original_filename'); // Originaldateiname
            $table->string('file_path'); // Dateipfad
            $table->string('mime_type'); // MIME-Typ
            $table->integer('file_size'); // Dateigröße in Bytes
            $table->string('description')->nullable(); // Beschreibung
            $table->enum('type', [
                'invoice', // Rechnung
                'receipt', // Quittung
                'inspection_report', // Prüfbericht
                'manual', // Handbuch
                'insurance', // Versicherungsdokument
                'registration', // Zulassungsdokument
                'image', // Bild
                'other' // Sonstiges
            ])->default('other');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attachments');
    }
};
