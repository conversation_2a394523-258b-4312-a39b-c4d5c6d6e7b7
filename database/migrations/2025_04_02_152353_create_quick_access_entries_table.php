<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quick_access_entries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained()->onDelete('cascade');
            $table->string('type'); // 'refueling', 'car_wash', 'parking_fee'
            $table->date('date');
            $table->integer('mileage')->nullable();
            $table->decimal('amount', 10, 2); // The amount paid
            $table->decimal('quantity', 10, 2)->nullable(); // e.g., liters for refueling
            $table->decimal('price_per_unit', 10, 2)->nullable(); // e.g., price per liter
            $table->string('location')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quick_access_entries');
    }
};
