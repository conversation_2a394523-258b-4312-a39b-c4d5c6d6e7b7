<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('make')->nullable(); // Hersteller
            $table->string('model')->nullable(); // Modell
            $table->string('type')->nullable(); // Fahrzeugtyp
            $table->string('hsn')->nullable(); // Herstellerschlüsselnummer
            $table->string('tsn')->nullable(); // Typschlüsselnummer
            $table->string('vin')->nullable()->unique(); // Fahrzeug-Identifikationsnummer
            $table->string('license_plate')->nullable(); // Kennzeichen
            $table->integer('year')->nullable(); // Baujahr
            $table->integer('mileage')->default(0); // Kilometerstand
            $table->date('purchase_date')->nullable(); // Kaufdatum
            $table->decimal('purchase_price', 10, 2)->nullable(); // Kaufpreis
            $table->text('notes')->nullable(); // Notizen
            $table->string('color')->nullable(); // Farbe
            $table->string('fuel_type')->nullable(); // Kraftstoffart
            $table->integer('power')->nullable(); // Leistung in kW
            $table->string('engine_size')->nullable(); // Hubraum
            $table->string('transmission')->nullable(); // Getriebe
            $table->string('image')->nullable(); // Hauptbild
            $table->boolean('is_active')->default(true); // Aktiv/Inaktiv
            $table->timestamps();
            $table->softDeletes(); // Für "gelöschte" Fahrzeuge
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
