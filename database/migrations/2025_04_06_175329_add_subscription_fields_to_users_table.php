<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('consecutive_subscriptions')->default(0)->comment('Anzahl aufeinanderfolgender Abonnements vom gleichen Typ');
            $table->boolean('restricted_mode')->default(false)->comment('Ob der Benutzer im eingeschränkten Modus ist (nur Lesezugriff)');
            $table->timestamp('last_subscription_end')->nullable()->comment('Datum des letzten Abo-Endes für Überprüfung aufeinanderfolgender Abos');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('consecutive_subscriptions');
            $table->dropColumn('restricted_mode');
            $table->dropColumn('last_subscription_end');
        });
    }
};
