<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('parts', function (Blueprint $table) {
            $table->foreignId('maintenance_log_id')->nullable()->after('id')->constrained()->onDelete('cascade');

            // Überprüfe, ob die Spalten bereits existieren
            if (!Schema::hasColumn('parts', 'quantity')) {
                $table->integer('quantity')->default(1)->after('description');
            }

            if (!Schema::hasColumn('parts', 'cost')) {
                $table->decimal('cost', 10, 2)->nullable()->after('quantity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('parts', function (Blueprint $table) {
            $table->dropForeign(['maintenance_log_id']);
            $table->dropColumn('maintenance_log_id');

            // Wenn die Spalten durch diese Migration hinzugefügt wurden, entfernen
            if (Schema::hasColumn('parts', 'quantity') && Schema::hasColumn('parts', 'cost')) {
                $table->dropColumn(['quantity', 'cost']);
            }
        });
    }
};
