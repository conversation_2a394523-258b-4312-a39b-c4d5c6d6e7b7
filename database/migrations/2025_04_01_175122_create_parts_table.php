<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parts', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Teilename
            $table->string('part_number')->nullable(); // Teilenummer
            $table->text('description')->nullable(); // Beschreibung
            $table->decimal('price', 10, 2)->nullable(); // Preis
            $table->string('manufacturer')->nullable(); // Hersteller
            $table->string('supplier')->nullable(); // Lieferant
            $table->string('supplier_url')->nullable(); // Link zum Lieferanten
            $table->string('image')->nullable(); // Bild
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parts');
    }
};
