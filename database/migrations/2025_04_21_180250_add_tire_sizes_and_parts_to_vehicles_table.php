<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('vehicles', 'tire_sizes')) {
                $table->json('tire_sizes')->nullable()->after('is_active');
            }
            
            if (!Schema::hasColumn('vehicles', 'parts')) {
                $table->json('parts')->nullable()->after('tire_sizes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            if (Schema::hasColumn('vehicles', 'tire_sizes')) {
                $table->dropColumn('tire_sizes');
            }
            
            if (Schema::hasColumn('vehicles', 'parts')) {
                $table->dropColumn('parts');
            }
        });
    }
};
