<?php

namespace Database\Factories;

use App\Models\Trip;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Trip>
 */
class TripFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Trip::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startOdometer = $this->faker->numberBetween(1000, 100000);
        $endOdometer = $startOdometer + $this->faker->numberBetween(5, 100);
        $type = $this->faker->randomElement(['business', 'private', 'commute']);
        
        // Business locations tend to be different from private ones
        $startLocations = [
            'business' => [
                'München, Büro',
                'München, Firmensitz',
                'München, Niederlassung',
            ],
            'private' => [
                'München, Zuhause',
                'München, Wohnung',
            ],
            'commute' => [
                'München, Zuhause',
                'München, Wohnung',
            ],
        ];
        
        $endLocations = [
            'business' => [
                'Starnberg, Kunde XYZ',
                'Augsburg, Niederlassung Nord',
                'München, Konferenzzentrum',
                'Dachau, Partnerunternehmen',
            ],
            'private' => [
                'München, Supermarkt',
                'München, Fitnessstudio',
                'München, Restaurant',
                'München, Kino',
            ],
            'commute' => [
                'München, Büro',
                'München, Arbeitsstelle',
            ],
        ];
        
        $purposes = [
            'business' => [
                'Kundentermin',
                'Geschäftstreffen',
                'Besprechung',
                'Fortbildung',
                'Konferenz',
            ],
            'private' => [
                'Einkaufen',
                'Freizeit',
                'Sport',
                'Arztbesuch',
                'Restaurant',
            ],
            'commute' => [
                'Arbeitsweg',
                'Heimweg',
                'Pendeln zur Arbeit',
                'Pendeln nach Hause',
            ],
        ];
        
        $startLocation = $this->faker->randomElement($startLocations[$type]);
        $endLocation = $this->faker->randomElement($endLocations[$type]);
        $purpose = $this->faker->randomElement($purposes[$type]);
        
        return [
            'user_id' => User::factory(),
            'vehicle_id' => Vehicle::factory(),
            'date' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'start_odometer' => $startOdometer,
            'end_odometer' => $endOdometer,
            'start_location' => $startLocation,
            'end_location' => $endLocation,
            'purpose' => $purpose,
            'type' => $type,
            'notes' => $this->faker->optional(0.3)->sentence(),
        ];
    }

    /**
     * Configure the factory to create a business trip.
     *
     * @return static
     */
    public function business(): static
    {
        return $this->state(function (array $attributes) {
            $startOdometer = $attributes['start_odometer'] ?? $this->faker->numberBetween(1000, 100000);
            $endOdometer = $startOdometer + $this->faker->numberBetween(10, 100);
            
            return [
                'start_odometer' => $startOdometer,
                'end_odometer' => $endOdometer,
                'start_location' => $this->faker->randomElement([
                    'München, Büro',
                    'München, Firmensitz',
                    'München, Niederlassung',
                ]),
                'end_location' => $this->faker->randomElement([
                    'Starnberg, Kunde XYZ',
                    'Augsburg, Niederlassung Nord',
                    'München, Konferenzzentrum',
                    'Dachau, Partnerunternehmen',
                ]),
                'purpose' => $this->faker->randomElement([
                    'Kundentermin',
                    'Geschäftstreffen',
                    'Besprechung',
                    'Fortbildung',
                    'Konferenz',
                ]),
                'type' => 'business',
            ];
        });
    }

    /**
     * Configure the factory to create a private trip.
     *
     * @return static
     */
    public function private(): static
    {
        return $this->state(function (array $attributes) {
            $startOdometer = $attributes['start_odometer'] ?? $this->faker->numberBetween(1000, 100000);
            $endOdometer = $startOdometer + $this->faker->numberBetween(5, 50);
            
            return [
                'start_odometer' => $startOdometer,
                'end_odometer' => $endOdometer,
                'start_location' => $this->faker->randomElement([
                    'München, Zuhause',
                    'München, Wohnung',
                ]),
                'end_location' => $this->faker->randomElement([
                    'München, Supermarkt',
                    'München, Fitnessstudio',
                    'München, Restaurant',
                    'München, Kino',
                ]),
                'purpose' => $this->faker->randomElement([
                    'Einkaufen',
                    'Freizeit',
                    'Sport',
                    'Arztbesuch',
                    'Restaurant',
                ]),
                'type' => 'private',
            ];
        });
    }

    /**
     * Configure the factory to create a commute trip.
     *
     * @return static
     */
    public function commute(): static
    {
        return $this->state(function (array $attributes) {
            $startOdometer = $attributes['start_odometer'] ?? $this->faker->numberBetween(1000, 100000);
            $endOdometer = $startOdometer + $this->faker->numberBetween(10, 30);
            
            return [
                'start_odometer' => $startOdometer,
                'end_odometer' => $endOdometer,
                'start_location' => $this->faker->randomElement([
                    'München, Zuhause',
                    'München, Wohnung',
                ]),
                'end_location' => $this->faker->randomElement([
                    'München, Büro',
                    'München, Arbeitsstelle',
                ]),
                'purpose' => $this->faker->randomElement([
                    'Arbeitsweg',
                    'Pendeln zur Arbeit',
                ]),
                'type' => 'commute',
            ];
        });
    }
} 