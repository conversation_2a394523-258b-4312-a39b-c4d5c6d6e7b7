
  GET|HEAD        / ................................................................................. home
  GET|HEAD        api-fallback/vehicle-data api.vehicle-data.hsn-tsn › VehicleDataController@getVehicleBy…
  GET|HEAD        api-fallback/vehicle-data/vin api.vehicle-data.vin › VehicleDataController@getVehicleBy…
  GET|HEAD        calendar ...................................................................... calendar
  GET|HEAD        confirm-password ............ password.confirm › Auth\ConfirmablePasswordController@show
  POST            confirm-password generated::j8YCChAlyOsaGr79 › Auth\ConfirmablePasswordController@store
  GET|HEAD        dashboard ........................................ dashboard › DashboardController@index
  POST            email/verification-notification verification.send › Auth\EmailVerificationNotificationC…
  GET|HEAD        forgot-password ............. password.request › Auth\PasswordResetLinkController@create
  POST            forgot-password ................ password.email › Auth\PasswordResetLinkController@store
  GET|HEAD        livewire/livewire.js generated::8xy4hTvNbCbdAizK › Livewire\Mechanisms › FrontendAssets…
  GET|HEAD        livewire/livewire.min.js.map generated::cMiz7GlkMHVJEHai › Livewire\Mechanisms › Fronte…
  GET|HEAD        livewire/preview-file/{filename} livewire.preview-file › Livewire\Features › FilePrevie…
  POST            livewire/update .... livewire.update › Livewire\Mechanisms › HandleRequests@handleUpdate
  POST            livewire/upload-file livewire.upload-file › Livewire\Features › FileUploadController@ha…
  GET|HEAD        login ............................... login › Auth\AuthenticatedSessionController@create
  POST            login .......... generated::sXokPphHopM4zh7u › Auth\AuthenticatedSessionController@store
  POST            logout ............................ logout › Auth\AuthenticatedSessionController@destroy
  POST            maintenance-logs/{maintenanceLog}/attachments maintenance-logs.attachments.store › Atta…
  GET|HEAD        maintenance-logs/{maintenance_log} maintenance-logs.show › MaintenanceLogController@show
  PUT|PATCH       maintenance-logs/{maintenance_log} maintenance-logs.update › MaintenanceLogController@u…
  DELETE          maintenance-logs/{maintenance_log} maintenance-logs.destroy › MaintenanceLogController@…
  GET|HEAD        maintenance-logs/{maintenance_log}/edit maintenance-logs.edit › MaintenanceLogControlle…
  GET|HEAD        maintenance-types ............ maintenance-types.index › MaintenanceTypeController@index
  GET|HEAD        maintenance-types/form-options maintenance-types.form-options › MaintenanceTypeControll…
  GET|HEAD        maintenance-types/stats/{vehicleId?} maintenance-types.stats › MaintenanceTypeControlle…
  GET|HEAD        parts ............................................... parts.index › PartController@index
  POST            parts ............................................... parts.store › PartController@store
  GET|HEAD        parts/create ...................................... parts.create › PartController@create
  GET|HEAD        parts/search ...................................... parts.search › PartController@search
  GET|HEAD        parts/{part} .......................................... parts.show › PartController@show
  PUT|PATCH       parts/{part} ...................................... parts.update › PartController@update
  DELETE          parts/{part} .................................... parts.destroy › PartController@destroy
  GET|HEAD        parts/{part}/edit ..................................... parts.edit › PartController@edit
  GET|HEAD        register ............................... register › Auth\RegisteredUserController@create
  POST            register ............. generated::rdf8HMEB4GgPSZC5 › Auth\RegisteredUserController@store
  POST            reset-password ....................... password.store › Auth\NewPasswordController@store
  GET|HEAD        reset-password/{token} .............. password.reset › Auth\NewPasswordController@create
  GET|HEAD        service-reminders/{service_reminder} service-reminders.show › ServiceReminderController…
  PUT|PATCH       service-reminders/{service_reminder} service-reminders.update › ServiceReminderControll…
  DELETE          service-reminders/{service_reminder} service-reminders.destroy › ServiceReminderControl…
  GET|HEAD        service-reminders/{service_reminder}/edit service-reminders.edit › ServiceReminderContr…
  ANY             settings ......... generated::QjhWK2Fmy6n6YkvO › Illuminate\Routing › RedirectController
  GET|HEAD        settings/appearance ......................................................... appearance
  GET|HEAD        settings/password ..................... password.edit › Settings\PasswordController@edit
  PUT             settings/password ................. password.update › Settings\PasswordController@update
  GET|HEAD        settings/profile ........................ profile.edit › Settings\ProfileController@edit
  PATCH           settings/profile .................... profile.update › Settings\ProfileController@update
  DELETE          settings/profile .................. profile.destroy › Settings\ProfileController@destroy
  GET|HEAD        storage/{path} ........................................................... storage.local
  GET|HEAD        up ......................................................... generated::uuEpRs9rComMDBdo
  GET|HEAD        vehicles ...................................... vehicles.index › VehicleController@index
  POST            vehicles ...................................... vehicles.store › VehicleController@store
  GET|HEAD        vehicles/create ............................. vehicles.create › VehicleController@create
  GET|HEAD        vehicles/{vehicle} .............................. vehicles.show › VehicleController@show
  PUT|PATCH       vehicles/{vehicle} .......................... vehicles.update › VehicleController@update
  DELETE          vehicles/{vehicle} ........................ vehicles.destroy › VehicleController@destroy
  POST            vehicles/{vehicle}/attachments vehicles.attachments.store › AttachmentController@storeF…
  DELETE          vehicles/{vehicle}/attachments/{attachment} vehicles.attachments.destroy › AttachmentCo…
  GET|HEAD        vehicles/{vehicle}/edit ......................... vehicles.edit › VehicleController@edit
  GET|HEAD        vehicles/{vehicle}/maintenance-logs vehicles.maintenance-logs.index › MaintenanceLogCon…
  POST            vehicles/{vehicle}/maintenance-logs vehicles.maintenance-logs.store › MaintenanceLogCon…
  GET|HEAD        vehicles/{vehicle}/maintenance-logs/create vehicles.maintenance-logs.create › Maintenan…
  GET|HEAD        vehicles/{vehicle}/report ........... vehicles.report › VehicleController@generateReport
  GET|HEAD        vehicles/{vehicle}/service-reminders vehicles.service-reminders.index › ServiceReminder…
  POST            vehicles/{vehicle}/service-reminders vehicles.service-reminders.store › ServiceReminder…
  GET|HEAD        vehicles/{vehicle}/service-reminders/create vehicles.service-reminders.create › Service…
  GET|HEAD        verify-email .............. verification.notice › Auth\EmailVerificationPromptController
  GET|HEAD        verify-email/{id}/{hash} .............. verification.verify › Auth\VerifyEmailController

                                                                                       Showing [69] routes

