# Serviceerinnerungen

Diese Dokumentation erklärt, wie Serviceerinnerungen in der Fahrzeug-Lebensakte-Anwendung funktionieren und durch geplante Aufgaben verwaltet werden.

## Überblick

Serviceerinnerungen ermöglichen es Benutzern, an anstehende Fahrzeugwartungen erinnert zu werden. Erinnerungen können basierend auf:

1. Einem bestimmten Datum (z.B. TÜV fällig am 01.06.2025)
2. <PERSON><PERSON>lo<PERSON> (z.B. Ölwechsel bei 60.000 km)
3. Oder beidem

Die Anwendung überprüft automatisch, ob Erinnerungen überfällig sind, und kann wiederkehrende Erinnerungen erstellen, nachdem eine Wartung abgeschlossen wurde.

## Datenbankstruktur

Serviceerinnerungen werden in der Tabelle `service_reminders` gespeichert und haben folgende wichtige Felder:

- `vehicle_id` - <PERSON><PERSON><PERSON><PERSON><PERSON>, für das die Erinnerung gilt
- `user_id` - <PERSON><PERSON><PERSON>, der die Erinnerung erstellt hat
- `title` - Titel der Erinnerung
- `description` - Optionale Beschreibung
- `due_date` - Fälligkeitsdatum (optional)
- `due_mileage` - Fälligkeits-Kilometerstand (optional)
- `status` - Status der Erinnerung ('pending', 'overdue', 'completed')
- `priority` - Priorität ('low', 'medium', 'high')
- `repeat_interval` - Wiederholungsintervall (z.B. 6)
- `repeat_unit` - Wiederholungseinheit ('days', 'weeks', 'months', 'years', 'km')
- `processed_at` - Datum, an dem die Folgeerinnerung erstellt wurde

## Status von Serviceerinnerungen

Erinnerungen durchlaufen verschiedene Status:

1. **Ausstehend (pending)** - Die Erinnerung wurde erstellt und ist noch nicht fällig
2. **Überfällig (overdue)** - Das Fälligkeitsdatum ist überschritten oder der Kilometerstand wurde erreicht
3. **Abgeschlossen (completed)** - Die Wartung wurde durchgeführt

## Geplante Aufgaben (Scheduler)

Die Anwendung führt automatisch folgende tägliche Aufgaben aus:

### 1. Aktualisierung der Kilometerstände (`vehicles:update-mileage`)

Aktualisiert den Kilometerstand der Fahrzeuge basierend auf der angegebenen jährlichen Fahrleistung.

```php
$schedule->command('vehicles:update-mileage')->dailyAt('00:00');
```

### 2. Prüfung auf überfällige Serviceerinnerungen (`service-reminders:check-overdue`)

Überprüft alle ausstehenden Erinnerungen auf Überfälligkeit (Datum oder Kilometerstand) und aktualisiert ihren Status.

```php
$schedule->command('service-reminders:check-overdue')->dailyAt('00:00');
```

### 3. Erstellung wiederkehrender Serviceerinnerungen (`service-reminders:create-recurring`)

Erstellt neue Folgeerinnerungen für abgeschlossene Erinnerungen mit Wiederholungseinstellungen.

```php
$schedule->command('service-reminders:create-recurring')->dailyAt('00:00');
```

## Manuelle Ausführung der Befehle

Die Befehle können auch manuell ausgeführt werden:

```bash
php artisan vehicles:update-mileage
php artisan service-reminders:check-overdue
php artisan service-reminders:create-recurring
```

## Implementation der Wiederholungslogik

Die Wiederholung von Serviceerinnerungen wird über die `createNextReminder()` Methode in der `ServiceReminder`-Klasse implementiert. Diese Methode:

1. Prüft, ob die Erinnerung eine Wiederholung haben soll
2. Berechnet das nächste Fälligkeitsdatum basierend auf der Wiederholungseinheit
3. Berechnet den nächsten Fälligkeits-Kilometerstand bei kilometerbasierter Wiederholung
4. Erstellt eine neue Erinnerung mit den berechneten Werten

## Anzeige in der Benutzeroberfläche

Überfällige Erinnerungen werden in der Benutzeroberfläche hervorgehoben:

1. Im Dashboard wird die Anzahl der überfälligen Erinnerungen angezeigt
2. Im Kalender werden überfällige Erinnerungen rot markiert
3. In der Detailansicht einer Erinnerung werden überfällige Werte rot hervorgehoben

## Benutzerdefinierte Funktionen

Das `ServiceReminder`-Model bietet verschiedene Funktionen:

1. `scopePending()`, `scopeOverdue()`, `scopeCompleted()` - Filtern nach Status
2. `isDue()` - Prüft, ob eine Erinnerung überfällig ist
3. `createNextReminder()` - Erstellt eine Folgeerinnerung 