---
description: 
globs: 
alwaysApply: true
---
# Coding Standards and Best Practices

## PHP Standards
- Strict typing enabled: `declare(strict_types=1);`
- PSR-12 coding standards
- PHP 8.3+ features utilized
- Descriptive variable and method names
- Type hints and return types required

## Laravel Conventions
- Controllers in [app/Http/Controllers](mdc:app/Http/Controllers)
- Models in [app/Models](mdc:app/Models)
- Livewire components in [app/Http/Livewire](mdc:app/Http/Livewire)
- Form requests in [app/Http/Requests](mdc:app/Http/Requests)
- Services in [app/Services](mdc:app/Services)
- Repositories in [app/Repositories](mdc:app/Repositories)

## Frontend Guidelines
- Livewire for dynamic components
- Alpine.js for lightweight JavaScript
- TailwindCSS utility-first styling
- DaisyUI components
- Responsive design required
- Dark mode support
- High-contrast badges for improved accessibility
- Consistent color-coding for data categorization (e.g., trip types in Fahrtenbuch)

## Database Conventions
- Migrations in [database/migrations](mdc:database/migrations)
- Seeders in [database/seeders](mdc:database/seeders)
- Factories in [database/factories](mdc:database/factories)
- Snake case for table and column names
- Proper indexing required
- Foreign key constraints enforced