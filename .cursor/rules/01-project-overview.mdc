---
description: 
globs: 
alwaysApply: true
---
# Vehicle Life Record Application Overview

This Laravel-based application serves as a comprehensive digital vehicle management system. The application follows modern Laravel practices with a focus on maintainable, scalable code.

## Core Features
- Vehicle management with automatic data retrieval (HSN/TSN, VIN)
- Complete vehicle history tracking
- Service planning and calculations
- Tax-compliant vehicle logbook (Fahrtenbuch) with PDF/CSV exports
- Parts search integration
- PDF report generation

## Key Technologies
- Laravel 12
- PHP 8.3+
- Livewire 3.5+
- Alpine.js
- TailwindCSS
- DaisyUI
- MySQL/PostgreSQL

## Main Entry Points
- [routes/web.php](mdc:routes/web.php) - Web routes
- [routes/api.php](mdc:routes/api.php) - API endpoints
- [app/Http/Kernel.php](mdc:app/Http/Kernel.php) - HTTP kernel configuration