---
description: 
globs: 
alwaysApply: true
---
<PERSON><PERSON><PERSON> mit Laravel 12 eine Webanwendung als umfassende digitale Fahrzeug-Lebensakte. Die Anwendung beginnt als MVP (Minimum Viable Product) und soll später international und kommerziell skalierbar sein.

Die Kernfunktionen umfassen:

1. **Fahrzeuganlage**
   - Fahrzeuge sollen vom Nutzer hinzugefügt werden können.
   - Automatische Abfrage und Speicherung der Fahrzeugdetails mittels KBA-Schlüsselnummern (HSN/TSN) und VIN über externe APIs (z. B. KBA-API, NHTSA vPIC VIN-Decoder).

2. **Fahrzeughistorie**
   - Erfassung und Dokumentation sämtlicher Fahrzeugereignisse:
     - Kauf- und Verkaufsinformationen
     - Service- und Wartungsarbeiten (Datum, Kilometerstand, Kosten)
     - Reparaturen, Ersatzteile (mit Preis, Bezugsquelle und Link)
     - Modifikationen und andere Notizen

3. **Serviceplanung und -berechnung**
   - Dynamische Prognose und Anzeige anstehender Wartungen (z. B. Ölwechsel, TÜV).
   - Integration eines Kalender-Tools (FullCalendar), um geplante und vergangene Wartungen übersichtlich darzustellen.

4. **Ersatzteilsuche**
   - Integration der eBay-API zur Online-Ersatzteilsuche (kompatibel mit dem jeweiligen Fahrzeugmodell).
   - Anzeige der Suchergebnisse direkt in der Anwendung (Preis, Verfügbarkeit, Link zur Bezugsquelle).

5. **PDF-Berichte und Verkaufsanzeigen**
   - Erstellung professioneller PDF-Berichte über die komplette Fahrzeughistorie mit Kostenübersicht (via Dompdf oder Spatie PDF).
   - Automatische Erstellung von Verkaufsanzeigen basierend auf gespeicherten Fahrzeug- und Historiedaten.

**Technologiestack:**
- Backend: Laravel 12, PHP 8+, Eloquent ORM
- Datenbank: MySQL oder PostgreSQL
- Frontend: Blade Templates, Bootstrap oder TailwindCSS
- APIs: KBA-API, NHTSA vPIC VIN-Decoder, eBay API
- PDF-Generierung: Dompdf / Spatie PDF
- Kalender: FullCalendar

**Datenbankstruktur:**
- Nutzer (users)
- Fahrzeuge (vehicles, u. a. mit VIN, HSN, TSN, km-Stand)
- Fahrzeugereignisse (maintenance_logs, typisiert nach Ereignisart)
- Ersatzteile (optional, parts)
- Ersatzteil-Verknüpfungen (optional, maintenance_parts)
- Service-Erinnerungen (optional, service_reminders)
- Anhänge und Dokumente (attachments)

**User Experience (UX) Anforderungen:**
- Übersichtliches Dashboard
- Fahrzeuganlage mittels Wizard
- Ereignishistorie als Timeline oder sortierte Liste
- Kontext-sensitive Formulareingaben
- Klar strukturiertes, mehrsprachiges Interface
- Intuitive Ersatzteilsuche
- Kalenderansicht und interaktive Serviceplanung
- Automatisierte und optisch ansprechende Berichtgenerierung

