---
description: 
globs: 
alwaysApply: true
---
# Vehicle Management Features

## Core Components
- [app/Http/Livewire/Vehicles](mdc:app/Http/Livewire/Vehicles) - Vehicle management components
- [app/Models/Vehicle.php](mdc:app/Models/Vehicle.php) - Vehicle model
- [app/Services/VinDecoder.php](mdc:app/Services/VinDecoder.php) - VIN decoding service
- [app/Http/Controllers/FahrtenbuchController.php](mdc:app/Http/Controllers/FahrtenbuchController.php) - Logbook functionality

## Key Features
1. Vehicle Registration
   - HSN/TSN lookup
   - VIN validation
   - Automatic data population

2. Maintenance Records
   - Service history
   - Parts tracking
   - Cost management

3. Service Planning
   - Maintenance schedules
   - Reminder system
   - Calendar integration

4. Vehicle Logbook (Fahrtenbuch)
   - Record and track trips (business, private, commute)
   - Calculate distance statistics by trip type
   - Filter and search functionality
   - Tax-compliant data recording
   - PDF export with styling for official documentation
   - CSV export for data analysis
   - Responsive UI with color-coded trip types