<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Täglich um Mitternacht die Kilometerstände aktualisieren
        $schedule->command('vehicles:update-mileage')->dailyAt('00:00');
        
        // Täglich um Mitternacht die Serviceerinnerungen auf Überfälligkeit prüfen
        $schedule->command('service-reminders:check-overdue')->dailyAt('00:00');
        
        // Täglich um Mitternacht wiederkehrende Serviceerinnerungen erstellen
        $schedule->command('service-reminders:create-recurring')->dailyAt('00:00');
        
        // Täglich endgültig Löschen von Fahrzeugen, die länger als 30 Tage im Papierkorb sind
        $schedule->command('vehicles:delete-expired')->dailyAt('01:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
} 