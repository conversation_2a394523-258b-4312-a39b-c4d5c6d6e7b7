<?php

namespace App\Console\Commands;

use App\Models\ServiceReminder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CreateRecurringServiceReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'service-reminders:create-recurring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Erstellt wiederkehrende Serviceerinnerungen für abgeschlossene Erinnerungen';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Prüfe auf abgeschlossene Serviceerinnerungen mit Wiederholungseinstellungen...');

        // Hole alle abgeschlossenen Erinnerungen mit Wiederholungseinstellungen, die noch keine Folgeerinnerung haben
        $reminders = ServiceReminder::where('status', 'completed')
            ->whereNotNull('repeat_interval')
            ->whereNotNull('repeat_unit')
            ->whereNull('processed_at')
            ->get();

        $this->info("Gefundene abgeschlossene Erinnerungen mit Wiederholungseinstellungen: {$reminders->count()}");

        $createdCount = 0;

        foreach ($reminders as $reminder) {
            $this->info("Verarbeite Erinnerung: '{$reminder->title}' für Fahrzeug ID {$reminder->vehicle_id}");
            
            // Erstelle die nächste Erinnerung
            $nextReminder = $reminder->createNextReminder();
            
            if ($nextReminder) {
                $createdCount++;
                $this->info("-> Neue wiederkehrende Erinnerung erstellt mit ID {$nextReminder->id}");
                
                // Markiere die ursprüngliche Erinnerung als verarbeitet
                $reminder->processed_at = now();
                $reminder->save();
            } else {
                $this->info("-> Konnte keine neue Erinnerung erstellen");
            }
        }

        $this->info("{$createdCount} neue wiederkehrende Erinnerungen erstellt.");
        
        if ($createdCount > 0) {
            Log::info("Erstellung wiederkehrender Serviceerinnerungen abgeschlossen. {$createdCount} neue Erinnerungen erstellt.");
        }

        return Command::SUCCESS;
    }
} 