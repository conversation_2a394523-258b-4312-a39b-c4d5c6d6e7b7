<?php

namespace App\Console\Commands;

use App\Models\Vehicle;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateVehicleMileage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vehicles:update-mileage';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Aktualisiert den Kilometerstand der Fahrzeuge basierend auf der jährlichen Fahrleistung';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starte die Aktualisierung der Kilometerstände...');

        // Nur Fahrzeuge mit einer jährlichen Fahrleistung > 0 berücksichtigen
        $vehicles = Vehicle::whereNotNull('annual_mileage')
            ->where('annual_mileage', '>', 0)
            ->where('is_active', true)
            ->get();

        $this->info("Gefundene Fahrzeuge mit jährlicher Fahrleistung: {$vehicles->count()}");

        $updatedCount = 0;

        foreach ($vehicles as $vehicle) {
            $this->info("Verarbeite Fahrzeug: {$vehicle->make} {$vehicle->model} - Aktueller Kilometerstand: {$vehicle->mileage} km");
            
            if ($vehicle->updateMileageFromAnnualRate()) {
                $updatedCount++;
                $this->info("-> Kilometerstand aktualisiert auf: {$vehicle->mileage} km");
            } else {
                $this->info("-> Keine Aktualisierung notwendig");
            }
        }

        $this->info("Fahrzeug-Kilometerstände aktualisiert: {$updatedCount} von {$vehicles->count()}");
        
        Log::info("Tägliche Aktualisierung der Fahrzeug-Kilometerstände abgeschlossen. {$updatedCount} Fahrzeuge aktualisiert.");

        return Command::SUCCESS;
    }
} 