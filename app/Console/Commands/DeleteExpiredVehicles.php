<?php

namespace App\Console\Commands;

use App\Models\Vehicle;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DeleteExpiredVehicles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vehicles:delete-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Löscht Fahrzeuge endgültig, die vor mehr als 30 Tagen in den Papierkorb verschoben wurden';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Suche nach Fahrzeugen zur endgültigen Löschung...');
        
        $date = Carbon::now()->subDays(30);
        $vehicles = Vehicle::onlyTrashed()
            ->where('deleted_at', '<=', $date)
            ->get();
        
        $count = $vehicles->count();
        
        if ($count === 0) {
            $this->info('Keine Fahrzeuge zur endgültigen Löschung gefunden.');
            return Command::SUCCESS;
        }
        
        $this->info("Es wurden {$count} Fahrzeuge gefunden, die älter als 30 Tage sind und endgültig gelöscht werden.");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $deletedCount = 0;
        $errorCount = 0;
        
        foreach ($vehicles as $vehicle) {
            try {
                // Lösche das Fahrzeugbild, falls vorhanden
                if ($vehicle->image) {
                    Storage::disk('public')->delete($vehicle->image);
                }
                
                // Lösche das Fahrzeug endgültig
                $vehicle->forceDelete();
                $deletedCount++;
                
                Log::info('Fahrzeug endgültig gelöscht', [
                    'vehicle_id' => $vehicle->id,
                    'make' => $vehicle->make,
                    'model' => $vehicle->model,
                    'deleted_at' => $vehicle->deleted_at
                ]);
            } catch (\Exception $e) {
                $errorCount++;
                Log::error('Fehler beim endgültigen Löschen eines Fahrzeugs', [
                    'vehicle_id' => $vehicle->id,
                    'error' => $e->getMessage()
                ]);
                $this->error("Fehler beim Löschen von Fahrzeug ID {$vehicle->id}: {$e->getMessage()}");
            }
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine(2);
        
        $this->info("Vorgang abgeschlossen: {$deletedCount} Fahrzeuge wurden erfolgreich gelöscht.");
        
        if ($errorCount > 0) {
            $this->warn("Bei {$errorCount} Fahrzeugen sind Fehler aufgetreten. Details finden Sie im Log.");
        }
        
        return Command::SUCCESS;
    }
}
