<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Carbon;

class SetupTrialPeriodForNewUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-trial-period {--all : Aktiviere die Testphase für alle Benutzer ohne aktives Abonnement}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Richtet die kostenlose 6-monatige Testphase für neue Benutzer ein';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('all')) {
            // Für alle Benutzer ohne Testphase und ohne aktives Abonnement
            $users = User::whereNull('trial_ends_at')
                ->get();
            
            $count = 0;
            foreach ($users as $user) {
                if (!$user->subscription('default') || !$user->subscription('default')->active()) {
                    $user->setTrialPeriod();
                    $count++;
                }
            }
            
            $this->info("Kostenlose Testphase für {$count} Benutzer aktiviert.");
        } else {
            // Nur für Benutzer, die in den letzten 24 Stunden erstellt wurden und noch keine Testphase haben
            $yesterday = Carbon::now()->subDay();
            
            $users = User::whereNull('trial_ends_at')
                ->where('created_at', '>=', $yesterday)
                ->get();
            
            $count = 0;
            foreach ($users as $user) {
                $user->setTrialPeriod();
                $count++;
            }
            
            $this->info("Kostenlose Testphase für {$count} neue Benutzer aktiviert.");
        }
        
        return Command::SUCCESS;
    }
}
