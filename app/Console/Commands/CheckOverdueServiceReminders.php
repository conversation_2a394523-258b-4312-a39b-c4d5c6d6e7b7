<?php

namespace App\Console\Commands;

use App\Models\ServiceReminder;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CheckOverdueServiceReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'service-reminders:check-overdue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Prüft auf überfällige Serviceerinnerungen und aktualisiert ihren Status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Prüfe auf überfällige Serviceerinnerungen...');

        // Hole alle ausstehenden Erinnerungen
        $reminders = ServiceReminder::with('vehicle')
            ->where('status', 'pending')
            ->get();

        $this->info("Gefundene ausstehende Erinnerungen: {$reminders->count()}");

        $updatedCount = 0;

        foreach ($reminders as $reminder) {
            $isDueByDate = $reminder->due_date && Carbon::parse($reminder->due_date)->isPast();
            $isDueByMileage = $reminder->due_mileage && $reminder->vehicle && $reminder->vehicle->mileage >= $reminder->due_mileage;

            if ($isDueByDate || $isDueByMileage) {
                $reason = $isDueByDate ? 'Datum überschritten' : 'Kilometerstand erreicht';
                $this->info("Überfällige Erinnerung gefunden: '{$reminder->title}' für {$reminder->vehicle->make} {$reminder->vehicle->model} - Grund: {$reason}");
                
                // Status auf 'overdue' aktualisieren
                $reminder->status = 'overdue';
                $reminder->save();
                
                $updatedCount++;
            }
        }

        $this->info("Status von {$updatedCount} Erinnerungen auf 'überfällig' gesetzt.");
        
        if ($updatedCount > 0) {
            Log::info("Überprüfung der Serviceerinnerungen abgeschlossen. {$updatedCount} Erinnerungen als überfällig markiert.");
        }

        return Command::SUCCESS;
    }
} 