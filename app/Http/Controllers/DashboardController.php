<?php

namespace App\Http\Controllers;

use App\Models\MaintenanceLog;
use App\Models\ServiceReminder;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        // Alle Fahrzeuge des aktuellen Benutzers
        $vehicles = Vehicle::where('user_id', Auth::id())
            ->with('maintenanceLogs', 'serviceReminders')
            ->withCount('maintenanceLogs')
            ->orderBy('is_active', 'desc')
            ->get();

        // Aktive Fahrzeuge
        $activeVehicles = $vehicles->where('is_active', true);

        // Kürzlich durchgeführte Wartungen
        $recentMaintenanceLogs = MaintenanceLog::whereIn('vehicle_id', $vehicles->pluck('id'))
            ->with('vehicle')
            ->orderBy('date', 'desc')
            ->limit(5)
            ->get();

        // Anstehende Servicetermine
        $upcomingServices = ServiceReminder::whereIn('vehicle_id', $vehicles->pluck('id'))
            ->with('vehicle')
            ->where('status', 'pending')
            ->orderBy('due_date')
            ->limit(5)
            ->get();

        // Überfällige Servicetermine
        $overdueServices = ServiceReminder::whereIn('vehicle_id', $vehicles->pluck('id'))
            ->with('vehicle')
            ->where('status', 'overdue')
            ->orderBy('due_date')
            ->get();

        // Gesamtkosten nach Fahrzeug
        $vehicleCosts = [];
        foreach ($vehicles as $vehicle) {
            // Verwende die total_cost Eigenschaft, die auch die Kosten für Teile einschließt
            $vehicleCosts[$vehicle->id] = $vehicle->maintenanceLogs->sum(function($log) {
                return $log->getTotalCostAttribute();
            });
        }

        // Gesamtkosten
        $totalCosts = array_sum($vehicleCosts);

        return Inertia::render('Dashboard', [
            'vehicles' => $activeVehicles,
            'totalVehicles' => $vehicles->count(),
            'activeVehicles' => $activeVehicles->count(),
            'recentMaintenanceLogs' => $recentMaintenanceLogs,
            'upcomingServices' => $upcomingServices,
            'overdueServices' => $overdueServices,
            'vehicleCosts' => $vehicleCosts,
            'totalCosts' => $totalCosts,
        ]);
    }
}
