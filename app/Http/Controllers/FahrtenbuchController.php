<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Trip;
use App\Models\Vehicle;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;
use League\Csv\Writer;
use Symfony\Component\HttpFoundation\StreamedResponse;

class FahrtenbuchController extends Controller
{
    /**
     * Display the Fahrtenbuch index page
     */
    public function index(): InertiaResponse
    {
        $trips = Trip::with('vehicle')
            ->where('user_id', Auth::id())
            ->orderBy('date', 'desc')
            ->get();
            
        $vehicles = Vehicle::where('user_id', Auth::id())
            ->select('id', 'make', 'model', 'license_plate')
            ->get()
            ->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'name' => "{$vehicle->make} {$vehicle->model}",
                    'licensePlate' => $vehicle->license_plate,
                ];
            });
            
        // Group trips by month for the month dropdown
        $months = $trips->groupBy(function ($trip) {
            return $trip->date->format('Y-m');
        })->map(function ($group, $month) {
            $date = \Carbon\Carbon::createFromFormat('Y-m', $month);
            return [
                'value' => $month,
                'label' => $date->isoFormat('MMMM YYYY'),
            ];
        })->values();

        return Inertia::render('Fahrtenbuch/Index', [
            'trips' => $trips->map(function ($trip) {
                return [
                    'id' => $trip->id,
                    'date' => $trip->date->format('Y-m-d'),
                    'vehicleId' => $trip->vehicle_id,
                    'startOdometer' => $trip->start_odometer,
                    'endOdometer' => $trip->end_odometer,
                    'startLocation' => $trip->start_location,
                    'endLocation' => $trip->end_location,
                    'purpose' => $trip->purpose,
                    'type' => $trip->type,
                    'notes' => $trip->notes,
                    'isRecurring' => $trip->is_recurring,
                    'recurrenceType' => $trip->recurrence_type,
                    'recurrenceEndDate' => $trip->recurrence_end_date ? $trip->recurrence_end_date->format('Y-m-d') : null,
                    'weekdays' => $trip->weekdays,
                ];
            }),
            'vehicles' => $vehicles,
            'months' => $months,
        ]);
    }

    /**
     * Store a new trip entry
     */
    public function storeTrip(Request $request): RedirectResponse
    {
        \Log::info('Fahrtenbuch Eintrag erhalten', $request->all());
        
        $validated = $request->validate([
            'date' => 'required|date',
            'vehicleId' => 'required|exists:vehicles,id',
            'startOdometer' => 'required|integer',
            'endOdometer' => 'required|integer|gt:startOdometer',
            'startLocation' => 'required|string|max:255',
            'endLocation' => 'required|string|max:255',
            'purpose' => 'required|string|max:255',
            'type' => 'required|in:business,private,commute',
            'notes' => 'nullable|string|max:1000',
            'isRecurring' => 'boolean',
            'recurrenceType' => 'required_if:isRecurring,true|in:daily,weekly',
            'recurrenceEndDate' => 'required_if:isRecurring,true|date|after:date',
            'weekdays' => 'nullable|required_if:recurrenceType,weekly',
        ]);

        try {
            // Create base trip data
            $tripData = [
                'user_id' => Auth::id(),
                'vehicle_id' => $validated['vehicleId'],
                'date' => $validated['date'],
                'start_odometer' => $validated['startOdometer'],
                'end_odometer' => $validated['endOdometer'],
                'start_location' => $validated['startLocation'],
                'end_location' => $validated['endLocation'],
                'purpose' => $validated['purpose'],
                'type' => $validated['type'],
                'notes' => $validated['notes'] ?? null,
                'is_recurring' => $validated['isRecurring'] ?? false,
                'recurrence_type' => $validated['isRecurring'] ? $validated['recurrenceType'] : null,
                'recurrence_end_date' => $validated['isRecurring'] ? $validated['recurrenceEndDate'] : null,
                'weekdays' => ($validated['isRecurring'] && $validated['recurrenceType'] === 'weekly') ? $validated['weekdays'] : null,
            ];

            // Create the initial trip
            $trip = Trip::create($tripData);

            // If this is a recurring trip, create additional trips
            if ($validated['isRecurring']) {
                $startDate = Carbon::parse($validated['date']);
                $endDate = Carbon::parse($validated['recurrenceEndDate']);
                $dailyKmIncrease = $validated['endOdometer'] - $validated['startOdometer'];

                // Änderung hier: Für wöchentliche Wiederholungen erst ab der nächsten Woche starten
                if ($validated['recurrenceType'] === 'daily') {
                    $currentDate = $startDate->copy()->addDay(); // Start from next day
                } else { // weekly
                    // Bei wöchentlicher Wiederholung prüfen wir ab dem nächsten Tag
                    $currentDate = $startDate->copy()->addDay();
                }

                while ($currentDate->lte($endDate)) {
                    // Für wöchentliche Wiederholungen: Überprüfe, ob der aktuelle Wochentag ausgewählt ist
                    $createTrip = false;
                    
                    if ($validated['recurrenceType'] === 'daily') {
                        $createTrip = true;
                    } elseif ($validated['recurrenceType'] === 'weekly') {
                        // Bestimme Wochentag (0=Sonntag, 1=Montag, ..., 6=Samstag)
                        $dayOfWeek = $currentDate->dayOfWeek;
                        $weekdayMap = [
                            0 => 'sunday',
                            1 => 'monday',
                            2 => 'tuesday',
                            3 => 'wednesday',
                            4 => 'thursday',
                            5 => 'friday',
                            6 => 'saturday'
                        ];
                        
                        // Überprüfe, ob der aktuelle Wochentag ausgewählt ist
                        $weekday = $weekdayMap[$dayOfWeek];
                        $createTrip = isset($validated['weekdays'][$weekday]) && $validated['weekdays'][$weekday];
                    }
                    
                    if ($createTrip) {
                        // Calculate new odometer readings
                        $newStartOdometer = $tripData['end_odometer'] + 1;
                        $newEndOdometer = $newStartOdometer + $dailyKmIncrease;
                        
                        Trip::create([
                            ...$tripData,
                            'date' => $currentDate->format('Y-m-d'),
                            'start_odometer' => $newStartOdometer,
                            'end_odometer' => $newEndOdometer,
                        ]);
                        
                        // Update the last end odometer for the next iteration
                        $tripData['end_odometer'] = $newEndOdometer;
                    }
                    
                    // Das Datum für den nächsten Durchlauf erhöhen
                    if ($validated['recurrenceType'] === 'daily') {
                        $currentDate->addDay();
                    } else { // weekly
                        // Bei wöchentlichen Wiederholungen jeden Tag prüfen, da verschiedene Wochentage ausgewählt sein können
                        $currentDate->addDay();
                    }
                }
            }

            \Log::info('Fahrtenbuch Eintrag gespeichert', ['trip_id' => $trip->id]);
            
            return redirect()->back()->with('success', 'Fahrt wurde erfolgreich gespeichert');
        } catch (\Exception $e) {
            \Log::error('Fehler beim Speichern des Fahrtenbuch-Eintrags', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Fehler beim Speichern der Fahrt: ' . $e->getMessage());
        }
    }

    /**
     * Export the Fahrtenbuch as PDF or CSV
     */
    public function export(Request $request, string $format)
    {
        $validated = $request->validate([
            'vehicleId' => 'nullable|exists:vehicles,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'exportAll' => 'nullable|string'
        ]);

        // Wenn exportAll=true, verwende ein sehr breites Datumsintervall oder gar keins
        if (!empty($validated['exportAll']) && $validated['exportAll'] === 'true') {
            // Setze einen sehr großen Zeitraum, um alle Fahrten zu umfassen
            $startDate = Carbon::create(2000, 1, 1);
            $endDate = Carbon::create(2100, 12, 31);
        } else {
            $startDate = !empty($validated['startDate']) 
                ? Carbon::parse($validated['startDate']) 
                : Carbon::now()->startOfMonth();
                
            $endDate = !empty($validated['endDate']) 
                ? Carbon::parse($validated['endDate']) 
                : Carbon::now()->endOfMonth();
        }
        
        // Prepare vehicle data
        if (!empty($validated['vehicleId'])) {
            // If a specific vehicle is selected
            $vehicles = Vehicle::with('user')
                ->where('id', $validated['vehicleId'])
                ->where('user_id', Auth::id())
                ->get();
        } else {
            // Get all user's vehicles
            $vehicles = Vehicle::with('user')
                ->where('user_id', Auth::id())
                ->get();
        }
        
        // Prepare data structure organized by vehicles
        $vehiclesData = [];
        
        foreach ($vehicles as $vehicle) {
            $trips = Trip::where('vehicle_id', $vehicle->id)
                ->where('user_id', Auth::id())
                ->whereBetween('date', [$startDate, $endDate])
                ->orderBy('date', 'asc')
                ->get();
            
            // Skip vehicles with no trips in the date range
            if ($trips->isEmpty()) {
                continue;
            }
            
            // Calculate statistics
            $businessTrips = $trips->where('type', 'business');
            $privateTrips = $trips->where('type', 'private');
            $commuteTrips = $trips->where('type', 'commute');
            
            $businessKm = $businessTrips->sum(function ($trip) {
                return $trip->end_odometer - $trip->start_odometer;
            });
            
            $privateKm = $privateTrips->sum(function ($trip) {
                return $trip->end_odometer - $trip->start_odometer;
            });
            
            $commuteKm = $commuteTrips->sum(function ($trip) {
                return $trip->end_odometer - $trip->start_odometer;
            });
            
            $totalKm = $businessKm + $privateKm + $commuteKm;
            
            $statistics = [
                'totalTrips' => $trips->count(),
                'businessTrips' => $businessTrips->count(),
                'privateTrips' => $privateTrips->count(),
                'commuteTrips' => $commuteTrips->count(),
                'totalKm' => $totalKm,
                'businessKm' => $businessKm,
                'privateKm' => $privateKm,
                'commuteKm' => $commuteKm,
                'businessPercent' => $totalKm > 0 ? ($businessKm / $totalKm * 100) : 0,
                'privatePercent' => $totalKm > 0 ? ($privateKm / $totalKm * 100) : 0,
                'commutePercent' => $totalKm > 0 ? ($commuteKm / $totalKm * 100) : 0,
            ];
            
            $vehiclesData[$vehicle->id] = [
                'vehicle' => $vehicle,
                'trips' => $trips,
                'statistics' => $statistics
            ];
        }
        
        // Return appropriate format
        if ($format === 'pdf') {
            // Generate PDF using dompdf
            $pdf = PDF::loadView('pdf.fahrtenbuch-export', [
                'vehiclesData' => $vehiclesData,
                'startDate' => $startDate,
                'endDate' => $endDate,
            ]);
            
            return $pdf->download('Fahrtenbuch.pdf');
        } elseif ($format === 'csv') {
            $filename = 'Fahrtenbuch.csv';
            
            $headers = [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => "attachment; filename=\"$filename\"",
                'Pragma' => 'no-cache',
                'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
                'Expires' => '0',
            ];
            
            // Generate CSV directly instead of using a Blade template
            $callback = function() use ($vehiclesData) {
                $output = fopen('php://output', 'w');
                
                // Add BOM for UTF-8
                fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
                
                // CSV header
                fputcsv($output, [
                    'Fahrzeug', 'Kennzeichen', 'Datum', 'Startort', 'Zielort', 
                    'Zweck', 'Art', 'KM Stand Beginn', 'KM Stand Ende', 'Strecke (km)', 'Notizen'
                ], ';');
                
                // CSV content
                foreach ($vehiclesData as $vehicleId => $data) {
                    foreach ($data['trips'] as $trip) {
                        $type = match($trip->type) {
                            'business' => 'Geschäftlich',
                            'private' => 'Privat',
                            default => 'Arbeitsweg'
                        };
                        
                        fputcsv($output, [
                            $data['vehicle']->make . ' ' . $data['vehicle']->model,
                            $data['vehicle']->license_plate,
                            $trip->date->format('d.m.Y'),
                            $trip->start_location,
                            $trip->end_location,
                            $trip->purpose,
                            $type,
                            $trip->start_odometer,
                            $trip->end_odometer,
                            $trip->distance,
                            $trip->notes ?? ''
                        ], ';');
                    }
                }
                
                fclose($output);
            };
            
            return new StreamedResponse($callback, 200, $headers);
        }
        
        return redirect()->back()->with('error', 'Ungültiges Exportformat');
    }
    /**
     * Entfernt einen Trip anhand der ID.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyTrip($id): RedirectResponse
    {
        $trip = Trip::where('user_id', Auth::id())->find($id);
        if (!$trip) {
            return redirect()->back()->with('error', 'Fahrt nicht gefunden oder keine Berechtigung.');
        }
        $trip->delete();
        return redirect()->back()->with('success', 'Fahrt erfolgreich gelöscht.');
    }

    /**
     * Update the specified trip in database
     */
    public function update(Request $request, $trip): RedirectResponse
    {
        \Log::info('Fahrtenbuch Eintrag aktualisieren', $request->all());
        
        $validated = $request->validate([
            'date' => 'required|date',
            'vehicleId' => 'required|exists:vehicles,id',
            'startOdometer' => 'required|integer',
            'endOdometer' => 'required|integer|gt:startOdometer',
            'startLocation' => 'required|string|max:255',
            'endLocation' => 'required|string|max:255',
            'purpose' => 'required|string|max:255',
            'type' => 'required|in:business,private,commute',
            'notes' => 'nullable|string|max:1000',
            'isRecurring' => 'boolean',
            'recurrenceType' => 'required_if:isRecurring,true|in:daily,weekly',
            'recurrenceEndDate' => 'required_if:isRecurring,true|date|after:date',
            'weekdays' => 'nullable|required_if:recurrenceType,weekly',
        ]);

        try {
            // Find the trip
            $tripModel = Trip::where('id', $trip)
                ->where('user_id', Auth::id())
                ->firstOrFail();
            
            // Update trip
            $tripModel->update([
                'vehicle_id' => $validated['vehicleId'],
                'date' => $validated['date'],
                'start_odometer' => $validated['startOdometer'],
                'end_odometer' => $validated['endOdometer'],
                'start_location' => $validated['startLocation'],
                'end_location' => $validated['endLocation'],
                'purpose' => $validated['purpose'],
                'type' => $validated['type'],
                'notes' => $validated['notes'] ?? null,
                'is_recurring' => $validated['isRecurring'] ?? false,
                'recurrence_type' => $validated['isRecurring'] ? $validated['recurrenceType'] : null,
                'recurrence_end_date' => $validated['isRecurring'] ? $validated['recurrenceEndDate'] : null,
                'weekdays' => ($validated['isRecurring'] && $validated['recurrenceType'] === 'weekly') ? $validated['weekdays'] : null,
            ]);

            return redirect()->back()->with('success', 'Fahrt wurde erfolgreich aktualisiert');
        } catch (\Exception $e) {
            \Log::error('Fehler beim Aktualisieren der Fahrt', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            
            return redirect()->back()->with('error', 'Fehler beim Aktualisieren der Fahrt: ' . $e->getMessage());
        }
    }

    /**
     * Entfernt einen Trip und optional alle zugehörigen wiederholten Fahrten.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyRecurringTrip($id): RedirectResponse
    {
        try {
            $originalTrip = Trip::where('user_id', Auth::id())->find($id);
            
            if (!$originalTrip) {
                return redirect()->back()->with('error', 'Fahrt nicht gefunden oder keine Berechtigung.');
            }
            
            if (!$originalTrip->is_recurring) {
                return redirect()->back()->with('error', 'Diese Fahrt ist keine regelmäßige Fahrt.');
            }
            
            // Finde die früheste Fahrt in dieser Serie, um den korrekten Datumsbereich zu ermitteln
            $earliestTripDate = Trip::where('user_id', Auth::id())
                ->where('vehicle_id', $originalTrip->vehicle_id)
                ->where('start_location', $originalTrip->start_location)
                ->where('end_location', $originalTrip->end_location)
                ->where('purpose', $originalTrip->purpose)
                ->where('type', $originalTrip->type)
                ->where('is_recurring', true)
                ->where('recurrence_type', $originalTrip->recurrence_type)
                ->min('date');
                
            // Wenn kein frühestes Datum gefunden wurde, verwende das Datum der ausgewählten Fahrt
            $startDate = $earliestTripDate ? Carbon::parse($earliestTripDate) : $originalTrip->date;
            
            // Lösche alle Fahrten mit gleichen Eigenschaften im Wiederholungszeitraum
            Trip::where('user_id', Auth::id())
                ->where('vehicle_id', $originalTrip->vehicle_id)
                ->where('start_location', $originalTrip->start_location)
                ->where('end_location', $originalTrip->end_location)
                ->where('purpose', $originalTrip->purpose)
                ->where('type', $originalTrip->type)
                ->where('is_recurring', true)
                ->where('recurrence_type', $originalTrip->recurrence_type)
                ->whereBetween('date', [
                    $startDate,
                    $originalTrip->recurrence_end_date
                ])
                ->delete();
                
            return redirect()->back()->with('success', 'Alle wiederholten Fahrten wurden erfolgreich gelöscht.');
        } catch (\Exception $e) {
            \Log::error('Fehler beim Löschen der wiederholten Fahrten', [
                'error' => $e->getMessage(),
                'trip_id' => $id
            ]);
            return redirect()->back()->with('error', 'Fehler beim Löschen der Fahrten: ' . $e->getMessage());
        }
    }
}