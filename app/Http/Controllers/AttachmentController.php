<?php

namespace App\Http\Controllers;

use App\Models\Attachment;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class AttachmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Store a new attachment for a vehicle.
     */
    public function storeForVehicle(Request $request, Vehicle $vehicle)
    {
        $request->validate([
            'attachments' => 'required|array',
            'attachments.*' => 'required|file|max:10240', // 10MB max
            'types' => 'required|array',
            'types.*' => 'required|string',
            'descriptions' => 'nullable|array',
            'descriptions.*' => 'nullable|string',
            'file_sizes' => 'required|array',
            'file_sizes.*' => 'required|numeric',
            'user_id' => 'required|exists:users,id'
        ]);

        $attachments = $request->file('attachments');
        $types = $request->input('types');
        $descriptions = $request->input('descriptions');
        $fileSizes = $request->input('file_sizes');
        $userId = $request->input('user_id');

        $uploadedFiles = [];

        foreach ($attachments as $index => $file) {
            $originalFilename = $file->getClientOriginalName();
            $mimeType = $file->getMimeType();

            // Generiere einen eindeutigen Dateinamen
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();

            // Speichere die Datei
            $path = $file->storeAs('attachments', $filename, 'public');

            // Erstelle den Attachment-Datensatz
            $attachment = new Attachment([
                'filename' => $filename,
                'original_filename' => $originalFilename,
                'mime_type' => $mimeType,
                'type' => $types[$index] ?? 'other',
                'description' => $descriptions[$index] ?? null,
                'file_path' => $path,
                'file_size' => $fileSizes[$index] ?? 0,
                'user_id' => $userId,
            ]);

            // Verknüpfe mit dem Fahrzeug
            $vehicle->attachments()->save($attachment);
            $uploadedFiles[] = $originalFilename;
        }

        return response()->json([
            'success' => true,
            'message' => count($uploadedFiles) . ' Dokumente wurden erfolgreich hochgeladen',
            'files' => $uploadedFiles
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vehicle $vehicle, Attachment $attachment)
    {
        // Check if the attachment belongs to this vehicle
        if ($attachment->attachable_id != $vehicle->id || $attachment->attachable_type != get_class($vehicle)) {
            return response()->json([
                'success' => false,
                'message' => 'Das Dokument gehört nicht zu diesem Fahrzeug'
            ], 403);
        }

        // Delete the file from storage
        if (Storage::disk('public')->exists($attachment->file_path)) {
            Storage::disk('public')->delete($attachment->file_path);
        }

        // Delete the record from database
        $attachment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Dokument wurde erfolgreich gelöscht'
        ]);
    }
}
