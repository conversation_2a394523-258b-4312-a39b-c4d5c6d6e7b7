<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\MaintenanceLog;
use App\Models\Part;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class MaintenanceLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Vehicle $vehicle)
    {
        $maintenanceLogs = $vehicle->maintenanceLogs()
            ->orderBy('date', 'desc')
            ->get();

        return Inertia::render('MaintenanceLogs/Index', [
            'vehicle' => $vehicle,
            'maintenanceLogs' => $maintenanceLogs
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Vehicle $vehicle)
    {
        return Inertia::render('MaintenanceLogs/Create', [
            'vehicle' => $vehicle
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Vehicle $vehicle)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|string',
            'date' => 'required|date',
            'mileage' => 'required|integer|min:0',
            'cost' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'next_inspection_date' => 'nullable|date|required_if:type,inspection',
            'parts' => 'nullable|array',
            'parts.*.name' => 'required|string|max:255',
            'parts.*.part_number' => 'nullable|string|max:255',
            'parts.*.quantity' => 'required|integer|min:1',
            'parts.*.cost' => 'nullable|numeric|min:0',
            'parts.*.purchase_url' => 'nullable|string|max:1000',
        ]);

        // Füge den aktuellen Benutzer als Ersteller hinzu
        $validated['user_id'] = Auth::id();

        // Verwende eine Transaktion, um Datenkonsistenz zu gewährleisten
        DB::beginTransaction();

        try {
            // Erstelle den Wartungseintrag
            $maintenanceLog = $vehicle->maintenanceLogs()->create($validated);

            // Verarbeite die Teile, wenn vorhanden
            if (isset($validated['parts']) && is_array($validated['parts'])) {
                foreach ($validated['parts'] as $partData) {
                    $maintenanceLog->parts()->create($partData);
                }
            }

            DB::commit();

            return redirect()->route('vehicles.show', $vehicle->id)
                ->with('success', 'Wartungseintrag erfolgreich erstellt.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Ein Fehler ist aufgetreten: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MaintenanceLog $maintenanceLog)
    {
        // Ensure parts are properly loaded with all fields needed for calculation
        $maintenanceLog->load([
            'vehicle',
            'parts' => function($query) {
                $query->select('id', 'maintenance_log_id', 'name', 'part_number', 'quantity', 'cost', 'purchase_url');
            }
        ]);

        return Inertia::render('MaintenanceLogs/Show', [
            'maintenanceLog' => $maintenanceLog,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MaintenanceLog $maintenanceLog)
    {
        return Inertia::render('MaintenanceLogs/Edit', [
            'maintenanceLog' => $maintenanceLog->load('parts'),
            'vehicle' => $maintenanceLog->vehicle
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MaintenanceLog $maintenanceLog)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'type' => 'required|string',
            'date' => 'required|date',
            'mileage' => 'nullable|integer|min:0',
            'cost' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'next_inspection_date' => 'nullable|date|required_if:type,inspection',
            'parts' => 'nullable|array',
            'parts.*.id' => 'nullable|integer',
            'parts.*.name' => 'required|string|max:255',
            'parts.*.part_number' => 'nullable|string|max:255',
            'parts.*.quantity' => 'required|integer|min:1',
            'parts.*.cost' => 'nullable|numeric|min:0',
            'parts.*.purchase_url' => 'nullable|string|max:1000',
        ]);

        // Verwende eine Transaktion, um Datenkonsistenz zu gewährleisten
        DB::beginTransaction();

        try {
            // Aktualisiere den Wartungseintrag (ohne Teile)
            $maintenanceLogData = collect($validated)->except('parts')->toArray();
            $maintenanceLog->update($maintenanceLogData);

            // Verarbeite die Teile, wenn vorhanden
            if (isset($validated['parts']) && is_array($validated['parts'])) {
                // Aktualisierter Ansatz: Sammle zuerst die IDs der eingesendeten Teile
                $partIds = collect($validated['parts'])
                    ->pluck('id')
                    ->filter()
                    ->toArray();

                // Lösche alle Teile, die nicht mehr in der aktualisierten Liste sind
                $maintenanceLog->parts()
                    ->whereNotIn('id', $partIds)
                    ->delete();

                // Aktualisiere oder erstelle jedes Teil in der Liste
                foreach ($validated['parts'] as $partData) {
                    if (isset($partData['id']) && $partData['id']) {
                        // Teil aktualisieren
                        $maintenanceLog->parts()
                            ->where('id', $partData['id'])
                            ->update([
                                'name' => $partData['name'],
                                'part_number' => $partData['part_number'],
                                'quantity' => $partData['quantity'],
                                'cost' => $partData['cost'],
                                'purchase_url' => $partData['purchase_url'] ?? null,
                            ]);
                    } else {
                        // Neues Teil erstellen
                        $maintenanceLog->parts()->create([
                            'name' => $partData['name'],
                            'part_number' => $partData['part_number'],
                            'quantity' => $partData['quantity'],
                            'cost' => $partData['cost'],
                            'purchase_url' => $partData['purchase_url'] ?? null,
                        ]);
                    }
                }
            } else {
                // Wenn keine Teile gesendet wurden, alle vorhandenen löschen
                $maintenanceLog->parts()->delete();
            }

            DB::commit();

            return redirect()->route('maintenance-logs.show', $maintenanceLog->id)
                ->with('success', 'Wartungseintrag erfolgreich aktualisiert.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Ein Fehler ist aufgetreten: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MaintenanceLog $maintenanceLog)
    {
        $vehicleId = $maintenanceLog->vehicle_id;

        // Verwende eine Transaktion, um Datenkonsistenz zu gewährleisten
        DB::beginTransaction();

        try {
            // Lösche zunächst die zugehörigen Teile
            $maintenanceLog->parts()->delete();

            // Dann den Wartungseintrag löschen
            $maintenanceLog->delete();

            DB::commit();

            return redirect()->route('vehicles.show', $vehicleId)
                ->with('success', 'Wartungseintrag erfolgreich gelöscht.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Ein Fehler ist aufgetreten: ' . $e->getMessage()]);
        }
    }
}
