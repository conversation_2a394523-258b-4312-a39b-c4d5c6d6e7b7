<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\ServiceReminder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ServiceReminderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Vehicle $vehicle)
    {
        $reminders = $vehicle->serviceReminders()->orderBy('due_date')->get();

        return Inertia::render('ServiceReminders/Index', [
            'vehicle' => $vehicle,
            'reminders' => $reminders
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Vehicle $vehicle)
    {
        return Inertia::render('ServiceReminders/Create', [
            'vehicle' => $vehicle
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Vehicle $vehicle)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date',
            'due_mileage' => 'nullable|integer|min:0',
            'status' => 'required|string|in:pending,completed,overdue',
            'priority' => 'required|string|in:high,medium,low',
            'repeat_interval' => 'nullable|integer|min:1',
            'repeat_unit' => 'nullable|string|in:days,weeks,months,years,km',
        ]);

        // Stelle sicher, dass mindestens due_date oder due_mileage angegeben ist
        if (empty($validated['due_date']) && empty($validated['due_mileage'])) {
            return back()->withErrors([
                'due_date' => 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.',
                'due_mileage' => 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.'
            ])->withInput();
        }

        // Füge den aktuellen Benutzer als Ersteller hinzu
        $validated['user_id'] = Auth::id();

        // Erstelle die Servicerinnerung
        $serviceReminder = $vehicle->serviceReminders()->create($validated);

        return redirect()->route('vehicles.show', $vehicle->id)
            ->with('success', 'Servicerinnerung erfolgreich erstellt.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ServiceReminder $serviceReminder)
    {
        return Inertia::render('ServiceReminders/Show', [
            'serviceReminder' => $serviceReminder->load('vehicle'),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ServiceReminder $serviceReminder)
    {
        return Inertia::render('ServiceReminders/Edit', [
            'serviceReminder' => $serviceReminder,
            'vehicle' => $serviceReminder->vehicle
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ServiceReminder $serviceReminder)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date',
            'due_mileage' => 'nullable|integer|min:0',
            'status' => 'required|string|in:pending,completed,overdue',
            'priority' => 'required|string|in:high,medium,low',
            'repeat_interval' => 'nullable|integer|min:1',
            'repeat_unit' => 'nullable|string|in:days,weeks,months,years,km',
        ]);

        // Stelle sicher, dass mindestens due_date oder due_mileage angegeben ist
        if (empty($validated['due_date']) && empty($validated['due_mileage'])) {
            return back()->withErrors([
                'due_date' => 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.',
                'due_mileage' => 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.'
            ])->withInput();
        }

        $serviceReminder->update($validated);

        return redirect()->route('service-reminders.show', $serviceReminder->id)
            ->with('success', 'Servicerinnerung erfolgreich aktualisiert.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ServiceReminder $serviceReminder)
    {
        $vehicle_id = $serviceReminder->vehicle_id;
        $serviceReminder->delete();

        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Servicerinnerung erfolgreich gelöscht.'
            ]);
        }

        return redirect()->route('vehicles.show', $vehicle_id)
            ->with('success', 'Servicerinnerung erfolgreich gelöscht.');
    }
}
