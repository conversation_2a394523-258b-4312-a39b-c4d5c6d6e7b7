<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\ServerException;
use Illuminate\Http\Client\RequestException;

class VehicleDataController extends Controller
{
    /**
     * Ruft Fahrzeugdaten basierend auf HSN/TSN ab
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVehicleByHsnTsn(Request $request)
    {
        $request->validate([
            'hsn' => 'required|string|size:4',
            'tsn' => 'required|string|size:3',
        ]);

        $hsn = $request->hsn;
        $tsn = $request->tsn;

        // KBA-Nummer im Format HSN/TSN
        $kbaNumber = $hsn . '/' . $tsn;

        // Debug: Direkte API-Anfrage testen und vollständige Antwort zeigen
        if ($request->has('debug') && config('app.debug')) {
            try {
                $apiUrl = rtrim(config('services.kba.base_url'), '/') . '/CheckGermany';
                $username = config('services.kba.username') ?: 'calhan'; // Fallback für Tests

                $params = [
                    'KBANumber' => $kbaNumber,
                    'username' => $username
                ];

                $response = Http::timeout(15)->get($apiUrl, $params);

                return response()->json([
                    'status' => $response->status(),
                    'api_response' => $response->json(),
                    'api_url' => $apiUrl,
                    'params' => $params,
                    'raw_response' => $response->body(),
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ], 500);
            }
        }

        // Cache-Schlüssel für HSN/TSN-Kombination
        $cacheKey = "vehicle_data_{$hsn}_{$tsn}";

        // Prüfen, ob Daten im Cache sind (7 Tage Gültigkeit)
        if (Cache::has($cacheKey)) {
            return response()->json(Cache::get($cacheKey));
        }

        try {
            // KBA API aufrufen
            if (config('services.kba.username')) {
                $kbaData = $this->fetchFromKbaApi($kbaNumber);

                if (!empty($kbaData)) {
                    // Cache für 7 Tage
                    Cache::put($cacheKey, $kbaData, now()->addDays(7));
                    return response()->json($kbaData);
                }
            }

            // Fallback auf DAT Group API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('services.dat.api_key'),
                'Content-Type' => 'application/json',
            ])->get(config('services.dat.base_url') . '/vehicles', [
                'hsn' => $hsn,
                'tsn' => $tsn,
                'country' => 'DE', // Deutschland als Standard
            ]);

            if ($response->successful()) {
                $vehicleData = $this->transformApiResponse($response->json());

                // Cache für 7 Tage
                Cache::put($cacheKey, $vehicleData, now()->addDays(7));

                return response()->json($vehicleData);
            }

            // Bei Fehlern Fallback auf Mock-Daten (nur für Entwicklungszwecke)
            if (config('app.env') === 'local' || config('app.env') === 'development') {
                $mockData = $this->getMockVehicleData($hsn, $tsn);
                return response()->json($mockData);
            }

            return response()->json([
                'message' => 'Keine Fahrzeugdaten gefunden',
            ], 404);

        } catch (\Exception $e) {
            Log::error('Fehler beim Abrufen der Fahrzeugdaten: ' . $e->getMessage(), [
                'hsn' => $hsn,
                'tsn' => $tsn,
            ]);

            // Bei Fehlern im Entwicklungsmodus Mock-Daten zurückgeben
            if (config('app.env') === 'local' || config('app.env') === 'development') {
                $mockData = $this->getMockVehicleData($hsn, $tsn);
                return response()->json($mockData);
            }

            return response()->json([
                'message' => 'Fehler beim Abrufen der Fahrzeugdaten',
            ], 500);
        }
    }

    /**
     * Ruft Fahrzeugdaten basierend auf der VIN (Fahrgestellnummer) ab
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVehicleByVin(Request $request)
    {
        $request->validate([
            'vin' => 'required|string|min:17|max:17',
        ]);

        $vin = $request->vin;

        // Debug: Direkte API-Anfrage testen und vollständige Antwort zeigen
        if ($request->has('debug') && config('app.debug')) {
            try {
                // Die NHTSA API ist kostenlos und unterstützt VIN-Dekodierung
                $apiUrl = "https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVinValues/{$vin}";

                $params = [
                    'format' => 'json'
                ];

                $response = Http::timeout(15)->get($apiUrl, $params);

                return response()->json([
                    'status' => $response->status(),
                    'api_response' => $response->json(),
                    'api_url' => $apiUrl,
                    'params' => $params,
                    'raw_response' => $response->body(),
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ], 500);
            }
        }

        // Zusätzliche VIN-Validierung
        if (!$this->isValidVin($vin)) {
            return response()->json([
                'message' => 'Ungültige VIN. Bitte überprüfen Sie die Eingabe.',
            ], 400);
        }

        // Cache-Schlüssel für VIN
        $cacheKey = "vehicle_data_vin_{$vin}";

        // Prüfen, ob Daten im Cache sind (7 Tage Gültigkeit)
        if (Cache::has($cacheKey)) {
            return response()->json(Cache::get($cacheKey));
        }

        try {
            // Die NHTSA API (kostenlos, aber primär US-Fahrzeuge)
            $vehicleData = $this->fetchFromNHTSA($vin);

            if (!empty($vehicleData)) {
                // Cache für 7 Tage
                Cache::put($cacheKey, $vehicleData, now()->addDays(7));
                return response()->json($vehicleData);
            }

            // Falls NHTSA fehlschlägt, versuchen wir es mit der DAT API für europäische Fahrzeuge
            if (!empty(config('services.dat.api_key'))) {
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . config('services.dat.api_key'),
                    'Content-Type' => 'application/json',
                ])->get(config('services.dat.base_url') . '/vin-decoder', [
                    'vin' => $vin,
                ]);

                if ($response->successful()) {
                    $vehicleData = $this->transformVinApiResponse($response->json());

                    // Cache für 7 Tage
                    Cache::put($cacheKey, $vehicleData, now()->addDays(7));

                    return response()->json($vehicleData);
                }
            }

            // Bei Fehlern Fallback auf Mock-Daten (nur für Entwicklungszwecke)
            if (config('app.env') === 'local' || config('app.env') === 'development') {
                $mockData = $this->getMockVinData($vin);
                return response()->json($mockData);
            }

            return response()->json([
                'message' => 'Keine Fahrzeugdaten für diese VIN gefunden',
            ], 404);

        } catch (\Exception $e) {
            Log::error('Fehler beim Abrufen der VIN-Daten: ' . $e->getMessage(), [
                'vin' => $vin,
            ]);

            // Bei Fehlern im Entwicklungsmodus Mock-Daten zurückgeben
            if (config('app.env') === 'local' || config('app.env') === 'development') {
                $mockData = $this->getMockVinData($vin);
                return response()->json($mockData);
            }

            return response()->json([
                'message' => 'Fehler beim Abrufen der Fahrzeugdaten',
            ], 500);
        }
    }

    /**
     * Ruft Daten von der NHTSA API ab
     *
     * @param string $vin
     * @return array
     */
    private function fetchFromNHTSA(string $vin): array
    {
        try {
            $response = Http::get("https://vpic.nhtsa.dot.gov/api/vehicles/DecodeVinValues/{$vin}?format=json");

            if ($response->successful()) {
                $data = $response->json();

                // Prüfen, ob Daten vorhanden sind
                if (isset($data['Results']) && count($data['Results']) > 0) {
                    $result = $data['Results'][0];

                    // Erweiterte Erkennung: Auch nur Make (Hersteller) akzeptieren, wenn Model fehlt
                    if (!empty($result['Make'])) {
                        // Fahrzeugdaten basierend auf verfügbaren Informationen erstellen
                        $vehicleInfo = [
                            'vin' => $vin,
                            'make' => $result['Make'],
                            'model' => $result['Model'] ?? 'Unbekanntes Modell',
                        ];

                        // Optionale Felder hinzufügen, wenn verfügbar
                        if (!empty($result['ModelYear'])) {
                            $vehicleInfo['year'] = (int)$result['ModelYear'];
                        }

                        if (!empty($result['BodyClass'])) {
                            $vehicleInfo['type'] = $result['BodyClass'];
                            $vehicleInfo['body_type'] = $result['BodyClass'];
                        }

                        if (!empty($result['DisplacementL'])) {
                            $vehicleInfo['engine_size'] = (float)$result['DisplacementL'] * 1000;
                        }

                        if (!empty($result['FuelTypePrimary'])) {
                            $vehicleInfo['fuel_type'] = $this->mapFuelType($result['FuelTypePrimary']);
                        }

                        if (!empty($result['Doors'])) {
                            $vehicleInfo['doors'] = (int)$result['Doors'];
                        }

                        if (!empty($result['TransmissionStyle'])) {
                            $vehicleInfo['transmission'] = $result['TransmissionStyle'];
                        }

                        if (!empty($result['DriveType'])) {
                            $vehicleInfo['drive_type'] = $result['DriveType'];
                        }

                        // Hersteller-Info hinzufügen, wenn verfügbar
                        if (!empty($result['Manufacturer'])) {
                            $vehicleInfo['manufacturer'] = $result['Manufacturer'];
                        }

                        // Fahrzeugart hinzufügen, wenn verfügbar
                        if (!empty($result['VehicleType'])) {
                            $vehicleInfo['vehicle_type'] = $result['VehicleType'];
                        }

                        // Produktionsort hinzufügen, wenn verfügbar
                        if (!empty($result['PlantCity']) && !empty($result['PlantCountry'])) {
                            $vehicleInfo['production_plant'] = $result['PlantCity'] . ', ' . $result['PlantCountry'];
                        }

                        // Leistung in PS hinzufügen, wenn verfügbar
                        if (!empty($result['EngineHP'])) {
                            $vehicleInfo['power_hp'] = (int)$result['EngineHP'];
                            // Ungefähre Umrechnung in kW
                            $vehicleInfo['power'] = round((int)$result['EngineHP'] * 0.7457);
                        }

                        // Anmerkungen zu Fehlern in der VIN hinzufügen
                        if (!empty($result['ErrorText'])) {
                            $vehicleInfo['note'] = 'Hinweis: Mögliche VIN-Ungenauigkeiten erkannt.';
                        }

                        return [$vehicleInfo];
                    }
                }
            }

            return [];
        } catch (\Exception $e) {
            Log::warning('Fehler bei NHTSA API: ' . $e->getMessage(), ['vin' => $vin]);
            return [];
        }
    }

    /**
     * Konvertiert NHTSA Kraftstofftypen ins interne Format
     */
    private function mapFuelType(string $fuelType): string
    {
        $mapping = [
            'Gasoline' => 'petrol',
            'Diesel' => 'diesel',
            'Electric' => 'electric',
            'Hybrid' => 'hybrid',
            'Plug-in Hybrid' => 'plugin_hybrid',
            'Natural Gas' => 'cng',
            'Propane' => 'lpg',
            'Hydrogen' => 'hydrogen',
            'Flexible Fuel Vehicle' => 'flex_fuel',
        ];

        return $mapping[trim($fuelType)] ?? 'other';
    }

    /**
     * Validiert eine VIN nach grundlegenden Regeln
     */
    private function isValidVin(string $vin): bool
    {
        // Grundlegende VIN-Validierung (17 Zeichen, keine I, O, Q)
        return (bool) preg_match('/^[A-HJ-NPR-Z0-9]{17}$/i', $vin);
    }

    /**
     * Ruft Mock-Daten für Entwicklungszwecke ab
     */
    private function getMockVehicleData(string $hsn, string $tsn): array
    {
        // Vereinfachte Mock-Daten für Entwicklungszwecke
        $mockDatabase = [
            '0005/119' => [
                [
                    'hsn' => '0005',
                    'tsn' => '119',
                    'make' => 'BMW',
                    'model' => '3er',
                    'type' => '320d',
                    'fuel_type' => 'diesel',
                    'power' => 110,
                    'engine_size' => 1995,
                    'year_from' => 2005,
                    'year_to' => 2011,
                ]
            ],
            '0005/AII' => [
                [
                    'hsn' => '0005',
                    'tsn' => 'AII',
                    'make' => 'BMW',
                    'model' => '525 D TOURING',
                    'type' => 'Touring',
                    'fuel_type' => 'diesel',
                    'power' => 145,
                    'power_hp' => 197,
                    'engine_size' => 2993,
                    'year_from' => 2004,
                    'year_to' => 2010,
                    'image_url' => 'http://www.kbaapi.de/image.aspx/@Ym13IDUyNSBEIFRPVVJJTkc=',
                ]
            ],
            '0603/AGI' => [
                [
                    'hsn' => '0603',
                    'tsn' => 'AGI',
                    'make' => 'Audi',
                    'model' => 'A4',
                    'type' => '2.0 TFSI',
                    'fuel_type' => 'petrol',
                    'power' => 147,
                    'engine_size' => 1984,
                    'year_from' => 2008,
                    'year_to' => 2015,
                ]
            ],
            '7089/ACK' => [
                [
                    'hsn' => '7089',
                    'tsn' => 'ACK',
                    'make' => 'Volkswagen',
                    'model' => 'Golf',
                    'type' => '1.4 TSI',
                    'fuel_type' => 'petrol',
                    'power' => 90,
                    'engine_size' => 1390,
                    'year_from' => 2012,
                    'year_to' => 2019,
                ]
            ],
            '4000/305' => [
                [
                    'hsn' => '4000',
                    'tsn' => '305',
                    'make' => 'Alfa Romeo',
                    'model' => 'Giulietta Spider',
                    'type' => '1.3',
                    'fuel_type' => 'petrol',
                    'power' => 59,
                    'engine_size' => 1281,
                    'year_from' => 1961,
                    'year_to' => 1962,
                ]
            ],
        ];

        $key = $hsn . '/' . $tsn;

        // Wenn HSN/TSN-Kombination in der Mock-Datenbank existiert, gebe die Daten zurück
        if (isset($mockDatabase[$key])) {
            return $mockDatabase[$key];
        }

        // Sonst generiere zufällige Daten
        return [[
            'hsn' => $hsn,
            'tsn' => $tsn,
            'make' => 'Unbekannter Hersteller',
            'model' => 'Unbekanntes Modell',
            'type' => 'Unbekannter Typ',
            'fuel_type' => array_rand(array_flip(['petrol', 'diesel', 'electric', 'hybrid'])),
            'power' => rand(50, 250),
            'engine_size' => rand(1000, 3000),
            'year_from' => rand(1990, 2020),
            'year_to' => rand(2021, 2023),
        ]];
    }

    /**
     * Ruft Mock-Daten für eine VIN ab
     */
    private function getMockVinData(string $vin): array
    {
        // Vereinfachte Mock-Daten für Entwicklungszwecke
        $mockDatabase = [
            'WVWZZZ1KZAW123456' => [
                [
                    'vin' => 'WVWZZZ1KZAW123456',
                    'make' => 'Volkswagen',
                    'model' => 'Golf',
                    'type' => '1.4 TSI',
                    'year' => 2010,
                    'fuel_type' => 'petrol',
                    'power' => 90,
                    'engine_size' => 1390,
                    'transmission' => 'manual',
                    'body_type' => 'hatchback',
                    'doors' => 5,
                ]
            ],
            'WAUZZZ8K9DA123456' => [
                [
                    'vin' => 'WAUZZZ8K9DA123456',
                    'make' => 'Audi',
                    'model' => 'A4',
                    'type' => '2.0 TDI',
                    'year' => 2013,
                    'fuel_type' => 'diesel',
                    'power' => 105,
                    'engine_size' => 1968,
                    'transmission' => 'automatic',
                    'body_type' => 'sedan',
                    'doors' => 4,
                ]
            ],
            'WB10404C1WZC38937' => [
                [
                    'vin' => 'WB10404C1WZC38937',
                    'make' => 'BMW',
                    'model' => 'R 1100 S',
                    'type' => 'Motorrad',
                    'year' => 1998,
                    'fuel_type' => 'petrol',
                    'power' => 72,
                    'engine_size' => 1085,
                    'transmission' => 'manual',
                    'body_type' => 'motorcycle',
                    'doors' => null,
                ]
            ],
            'WMWRC31090TB93763' => [
                [
                    'vin' => 'WMWRC31090TB93763',
                    'make' => 'MINI',
                    'model' => 'Cooper S',
                    'type' => 'Hatchback',
                    'year' => 2009,
                    'fuel_type' => 'petrol',
                    'power' => 128,
                    'power_hp' => 172,
                    'engine_size' => 1598,
                    'transmission' => 'manual',
                    'body_type' => 'hatchback',
                    'doors' => 3,
                    'manufacturer' => 'BMW AG',
                    'production_plant' => 'OXFORD, UNITED KINGDOM',
                    'vehicle_type' => 'PASSENGER CAR',
                    'note' => 'Turbocharged R56 Cooper S Model',
                ]
            ],
        ];

        // Wenn VIN in der Mock-Datenbank existiert, gebe die Daten zurück
        if (isset($mockDatabase[$vin])) {
            return $mockDatabase[$vin];
        }

        // Sonst generiere zufällige Daten
        return [[
            'vin' => $vin,
            'make' => 'Unbekannter Hersteller',
            'model' => 'Unbekanntes Modell',
            'type' => 'Unbekannter Typ',
            'year' => rand(1990, 2023),
            'fuel_type' => array_rand(array_flip(['petrol', 'diesel', 'electric', 'hybrid'])),
            'power' => rand(50, 250),
            'engine_size' => rand(1000, 3000),
            'transmission' => array_rand(array_flip(['manual', 'automatic'])),
            'body_type' => array_rand(array_flip(['sedan', 'hatchback', 'suv', 'coupe'])),
            'doors' => rand(2, 5),
        ]];
    }

    /**
     * Transformiert API-Antwort ins interne Format
     */
    private function transformApiResponse(array $data): array
    {
        // Implementierung je nach API-Antwortformat anpassen
        // Dies ist ein Beispiel basierend auf einer hypothetischen API
        return $data;
    }

    /**
     * Transformiert VIN-API-Antwort ins interne Format
     */
    private function transformVinApiResponse(array $data): array
    {
        // Implementierung je nach API-Antwortformat anpassen
        // Dies ist ein Beispiel basierend auf einer hypothetischen API
        return $data;
    }

    /**
     * Ruft Fahrzeugdaten von der KBA API ab (deutsche Fahrzeugdaten)
     *
     * @param string $kbaNumber HSN/TSN-Kombination im Format "XXXX/XXX"
     * @return array
     */
    private function fetchFromKbaApi(string $kbaNumber): array
    {
        try {
            // Konfigurationen
            $apiUrl = rtrim(config('services.kba.base_url'), '/') . '/CheckGermany';
            $username = config('services.kba.username');
            $password = config('services.kba.password');
            $apiKey = config('services.kba.api_key');

            // Debug-Info
            Log::debug('KBA API Anfrage:', [
                'url' => $apiUrl,
                'kba_number' => $kbaNumber,
                'username' => $username,
                'has_api_key' => !empty($apiKey)
            ]);

            // API-Anfrage Parameter
            $params = [
                'KBANumber' => $kbaNumber,
            ];

            // Authentifizierung je nach Konfiguration
            if (!empty($apiKey)) {
                // API-Key Authentifizierung (bevorzugt)
                $params['apikey'] = $apiKey;
            } else {
                // Username/Password Authentifizierung
                $params['username'] = $username;
                if (!empty($password)) {
                    $params['password'] = $password;
                }
            }

            // Rate-Limiting durch verzögerte Anfragen (max. 2 Anfragen pro Sekunde)
            if (config('app.env') !== 'testing') {
                usleep(500000); // 500ms Pause
            }

            // Verbindungsoptionen: Timeout und Retry
            $response = Http::timeout(15)
                ->retry(2, 1000, function ($exception, $request) {
                    // Nur bei Serverfehlern oder Timeouts wiederholen
                    return $exception instanceof ConnectionException ||
                           ($exception instanceof RequestException && $exception->getCode() >= 500);
                })
                ->get($apiUrl, $params);

            Log::debug('KBA API Antwort:', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Prüfen, ob Fahrzeugdaten vorhanden sind
                if (!empty($data) && isset($data['CarMake']) && isset($data['CarMake']['CurrentTextValue'])) {
                    // HSN/TSN aus dem KBA-Number extrahieren
                    $hsnTsn = explode('/', $kbaNumber);
                    $hsn = $hsnTsn[0] ?? '';
                    $tsn = $hsnTsn[1] ?? '';

                    // Umwandlung in unser internes Format
                    return [[
                        'hsn' => $hsn,
                        'tsn' => $tsn,
                        'make' => $data['CarMake']['CurrentTextValue'] ?? '',
                        'model' => $data['CarModel']['CurrentTextValue'] ?? '',
                        'type' => $data['Description'] ?? '',
                        'fuel_type' => $this->mapKbaFuelType($data['Fuel'] ?? ''),
                        'power' => $data['PowerKW'] ?? null,
                        'power_hp' => $data['PowerHP'] ?? null,
                        'engine_size' => $data['EngineSize'] ?? null,
                        'year_from' => $this->extractYearFromDescription($data['Description'] ?? '', 'from'),
                        'year_to' => $this->extractYearFromDescription($data['Description'] ?? '', 'to'),
                        'image_url' => $data['ImageUrl'] ?? null,
                        'raw_data' => config('app.debug') ? $data : null, // Im Debug-Modus die Rohdaten mit zurückgeben
                    ]];
                } elseif (!empty($data) && isset($data['Description']) && $data['Description'] === 'No data found') {
                    Log::notice('KBA API: Keine Daten gefunden für KBANumber: ' . $kbaNumber);
                } elseif (!empty($data) && isset($data['faultstring'])) {
                    // API-Fehler mit detaillierter Meldung
                    Log::warning('KBA API Fehler: ' . ($data['faultstring'] ?? 'Unbekannter Fehler'), [
                        'kba_number' => $kbaNumber,
                        'response' => $data
                    ]);
                } else {
                    Log::warning('KBA API: Unerwartetes Antwortformat', [
                        'kba_number' => $kbaNumber,
                        'response' => $data
                    ]);
                }
            } else {
                $status = $response->status();

                // Detaillierte Fehlerbehandlung nach HTTP-Code
                if ($status === 401 || $status === 403) {
                    Log::error('KBA API: Authentifizierungsfehler', [
                        'status' => $status,
                        'response' => $response->body()
                    ]);
                } elseif ($status === 429) {
                    Log::warning('KBA API: Rate-Limit erreicht', [
                        'status' => $status,
                        'response' => $response->body()
                    ]);
                    // Bei Rate-Limit etwas länger warten vor dem nächsten Versuch
                    sleep(2);
                } else {
                    Log::warning('KBA API HTTP Fehler: ' . $status, [
                        'kba_number' => $kbaNumber,
                        'response' => $response->body(),
                    ]);
                }
            }

            return [];
        } catch (\Exception $e) {
            Log::error('Fehler beim Zugriff auf die KBA API: ' . $e->getMessage(), [
                'kba_number' => $kbaNumber,
                'exception' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Extrahiert das Jahr aus der Beschreibung
     * z.B. aus "ALFA GIULIETTA Spider 1.3 [196101 - 196212] (59kW 80hp Otto AR 00508)"
     */
    private function extractYearFromDescription(string $description, string $type = 'from'): ?int
    {
        $pattern = '/\[(\d{4})(\d{2})\s*-\s*(\d{4})(\d{2})\]/';
        if (preg_match($pattern, $description, $matches)) {
            if ($type === 'from') {
                return (int)$matches[1]; // Das erste Jahr
            } else {
                return (int)$matches[3]; // Das zweite Jahr
            }
        }
        return null;
    }

    /**
     * Mappt KBA-Kraftstofftypen auf unser internes Format
     */
    private function mapKbaFuelType(string $fuelType): string
    {
        $mapping = [
            'Petrol' => 'petrol',
            'Diesel' => 'diesel',
            'Electric' => 'electric',
            'Hybrid' => 'hybrid',
            'Plug-in Hybrid' => 'plugin_hybrid',
            'LPG' => 'lpg',
            'CNG' => 'cng',
            'Hydrogen' => 'hydrogen',
            'Benzin' => 'petrol',
            'Elektro' => 'electric',
            'Wasserstoff' => 'hydrogen',
            'Erdgas' => 'cng',
            'Autogas' => 'lpg',
        ];

        return $mapping[trim($fuelType)] ?? 'other';
    }
}
