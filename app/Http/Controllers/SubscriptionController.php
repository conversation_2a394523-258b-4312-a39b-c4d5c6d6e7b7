<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Inertia\Inertia;
use Lara<PERSON>\Cashier\Exceptions\IncompletePayment;

class SubscriptionController extends Controller
{
    /**
     * Show subscription plans page
     */
    public function index()
    {
        $user = Auth::user();
        
        return Inertia::render('Subscription/Plans', [
            'intent' => $user->createSetupIntent(),
            'plans' => $this->getPlans(),
            'hasSubscription' => $user->subscription('default') ? true : false,
            'inTrialPeriod' => $user->isInTrialPeriod(),
            'trialEndsAt' => $user->trial_ends_at ? $user->trial_ends_at->format('Y-m-d') : null,
            'restrictedMode' => $user->isInRestrictedMode(),
        ]);
    }

    /**
     * Process subscription checkout
     */
    public function checkout(Request $request)
    {
        $user = Auth::user();
        $paymentMethod = $request->input('payment_method');
        $planId = $request->input('plan_id');

        try {
            // <PERSON>elle sicher, dass der Benutzer ein Stripe-Kunde ist
            $user->createOrGetStripeCustomer();
            
            // Save payment method info for future payments
            $user->updateDefaultPaymentMethod($paymentMethod);
            
            // Prüfe auf ein bestehendes Abonnement oder ein früheres Abonnement, das kürzlich endete
            $existingSubscription = $user->subscription('default');
            $isRenewal = false;
            
            if ($existingSubscription) {
                // Wenn es ein bestehendes Abonnement gibt, überprüfe, ob es derselbe Plan ist
                $isRenewal = $existingSubscription->stripe_plan === $planId;
            } else if ($user->last_subscription_end) {
                // Wenn es kein aktives Abonnement gibt, aber ein früheres, prüfe, ob es weniger als 7 Tage her ist
                $isRenewal = Carbon::now()->diffInDays($user->last_subscription_end) < 7;
            }
            
            // Subscribe the user to the plan
            $subscription = $user->newSubscription('default', $planId)
                ->create($paymentMethod);
                
            // Wenn es eine Erneuerung ist, verlängere die Abonnementdauer entsprechend
            if ($isRenewal && $this->isPlanBasis($planId)) {
                $user->extendSubscription();
            }

            return to_route('subscription.success');
        } catch (IncompletePayment $exception) {
            return to_route('cashier.payment', [
                $exception->payment->id,
                'redirect' => route('subscription.index'),
            ]);
        } catch (\Exception $e) {
            // Allgemeine Fehlerbehandlung hinzufügen
            \Log::error('Subscription error: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'plan_id' => $planId
            ]);
            
            return to_route('subscription.index')
                ->with('error', 'Bei der Verarbeitung Ihres Abonnements ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.');
        }
    }

    /**
     * Prüft, ob es sich um den Basis-Plan handelt
     */
    private function isPlanBasis($planId): bool
    {
        return in_array($planId, [
            'price_1RAwm7FbLhupDbC8WBFS4MMI',
            'price_1RAwLkFbLhupDbC8TEnFHStA',
            'price_1RAxWCFbLhupDbC8MaTVzDDW'
        ]);
    }

    /**
     * Show subscription success page
     */
    public function success()
    {
        $user = Auth::user();
        $subscription = $user->subscription('default');
        
        // Hole Details zum aktiven Plan
        $planDetails = null;
        if ($subscription) {
            $stripePlan = $subscription->stripe_plan;
            $planDetails = $this->getPlanDetails($stripePlan);
        }
        
        return Inertia::render('Subscription/Success', [
            'subscription' => $subscription,
            'planDetails' => $planDetails,
            'consecutiveSubscriptions' => $user->consecutive_subscriptions,
        ]);
    }

    /**
     * Get detailed information about a specific plan
     */
    private function getPlanDetails($stripePlanId)
    {
        $allPlans = $this->getPlans();
        
        foreach ($allPlans as $plan) {
            if ($plan['id'] === $stripePlanId) {
                return $plan;
            }
        }
        
        // Fallback mit Basisinformationen, falls der Plan nicht in der Liste gefunden wird
        return [
            'id' => $stripePlanId,
            'name' => 'Ihr Abonnementplan',
            'price' => null,
            'interval' => null
        ];
    }

    /**
     * Show manage subscription page
     */
    public function manage()
    {
        $user = auth()->user();
        
        // Aktives Abonnement abrufen
        $subscription = null;
        $invoices = [];
        $paymentMethod = null;
        $expiresAt = null;
        $daysRemaining = null;
        $isInTrialPeriod = false;
        $restrictedMode = false;
        $consecutiveSubscriptions = 0;
        $trialEndsAt = null;
        $purchasedSubscriptions = 0;
        
        \Log::info('User trial info:', [
            'user_id' => $user->id,
            'trial_ends_at' => $user->trial_ends_at,
        ]);

        // Prüfe, ob der Benutzer ein aktives Abonnement hat
        if ($user->subscribed('default')) {
            $subscription = $user->subscription('default');
            
            // Alle Rechnungen abrufen
            $invoices = $user->invoices();
            
            // Zähle, wie viele Abos der gleichen Art gekauft wurden
            $purchasedSubscriptions = count($invoices);
            
            // Ablaufdatum ermitteln und ggf. anpassen auf Basis der Anzahl gekaufter Abos
            if ($subscription->ends_at) {
                $expiresAt = $subscription->ends_at;
            } else {
                // Basis-Enddatum aus Stripe abrufen
                $baseExpiresAt = $subscription->asStripeSubscription()->current_period_end
                    ? Carbon::createFromTimestamp($subscription->asStripeSubscription()->current_period_end)
                    : null;
                
                // Wenn mehrere Abos gekauft wurden und ein Basisdatum vorhanden ist, verlängere entsprechend
                if ($baseExpiresAt && $purchasedSubscriptions > 1) {
                    // Standard-Abodauer ist 1 Monat, also verlängere um (Anzahl-1) Monate
                    // Die erste Rechnung deckt bereits die ersten 30 Tage ab
                    $additionalMonths = $purchasedSubscriptions - 1;
                    $baseExpiresAt->addMonths($additionalMonths);
                    
                    // Logge für Debug-Zwecke
                    \Log::info('Mehrere Abos gekauft:', [
                        'Anzahl' => $purchasedSubscriptions,
                        'Basis-Enddatum' => $baseExpiresAt->toDateString(),
                        'Verlängert um Monate' => $additionalMonths
                    ]);
                }
                
                $expiresAt = $baseExpiresAt ? $baseExpiresAt->toDateTimeString() : null;
            }
            
            // Testphasendatum ermitteln
            if ($subscription->trial_ends_at) {
                $trialEndsAt = $subscription->trial_ends_at;
                $isInTrialPeriod = now()->lt(Carbon::parse($trialEndsAt));
            }
            
            // Tage berechnen
            if ($expiresAt) {
                $now = Carbon::now();
                $end = Carbon::parse($expiresAt);
                // Vergleiche nur die Daten, nicht die Zeit
                $now->setTime(0, 0, 0);
                $end->setTime(0, 0, 0);
                $daysRemaining = $now->diffInDays($end);
                
                // Log für Debugging
                \Log::info('Abonnement Berechnung', [
                    'Gekaufte Abos' => $purchasedSubscriptions,
                    'Jetzt' => $now->toDateTimeString(),
                    'Ende' => $end->toDateTimeString(),
                    'Tage' => $daysRemaining,
                ]);
            }
            
            // Weitere Abonnementdaten
            $consecutiveSubscriptions = $user->consecutive_subscriptions ?? 0;
            $restrictedMode = $user->restricted_mode ?? false;
            
            // Standard-Zahlungsmethode
            $paymentMethod = $user->defaultPaymentMethod();
        } 
        // Prüfe, ob der Benutzer in der kostenlosen Testphase ist (auch ohne aktives Abonnement)
        else if ($user->trial_ends_at) {
            $trialEndsAt = $user->trial_ends_at;
            $isInTrialPeriod = now()->lt(Carbon::parse($trialEndsAt));
            
            // Berechne die verbleibenden Tage der Testphase
            $now = Carbon::now();
            $end = Carbon::parse($trialEndsAt);
            $now->setTime(0, 0, 0);
            $end->setTime(0, 0, 0);
            $daysRemaining = $now->diffInDays($end);
            
            // Das Ablaufdatum entspricht dem Ende der Testphase
            $expiresAt = $trialEndsAt;
            
            // Logge für Debug-Zwecke
            \Log::info('Kostenlose Testphase Berechnung', [
                'Jetzt' => $now->toDateTimeString(),
                'Ende der Testphase' => $end->toDateTimeString(),
                'Verbleibende Tage' => $daysRemaining,
                'isInTrialPeriod' => $isInTrialPeriod,
            ]);
        } else {
            // Neuer Nutzer ohne Abonnement und ohne explizite Testphase
            // Trotzdem Testphase für 6 Monate automatisch aktivieren
            $trialEndsAt = now()->addMonths(6)->toDateTimeString();
            $isInTrialPeriod = true;
            
            // Berechne die verbleibenden Tage der Testphase
            $now = Carbon::now();
            $end = Carbon::parse($trialEndsAt);
            $now->setTime(0, 0, 0);
            $end->setTime(0, 0, 0);
            $daysRemaining = $now->diffInDays($end);
            
            // Das Ablaufdatum entspricht dem Ende der Testphase
            $expiresAt = $trialEndsAt;
            
            // Aktualisiere den Benutzer in der Datenbank
            $user->trial_ends_at = $trialEndsAt;
            $user->save();
            
            \Log::info('Neue Testphase aktiviert:', [
                'user_id' => $user->id,
                'trial_ends_at' => $trialEndsAt,
                'daysRemaining' => $daysRemaining,
            ]);
        }

        // Nochmal final loggen, was übergeben wird
        \Log::info('Daten an View übergeben:', [
            'isInTrialPeriod' => $isInTrialPeriod,
            'trialEndsAt' => $trialEndsAt,
            'expiresAt' => $expiresAt,
            'daysRemaining' => $daysRemaining,
        ]);

        return Inertia::render('Subscription/Manage', [
            'subscription' => $subscription,
            'invoices' => $invoices,
            'paymentMethod' => $paymentMethod,
            'expiresAt' => $expiresAt,
            'daysRemaining' => $daysRemaining,
            'isInTrialPeriod' => $isInTrialPeriod,
            'trialEndsAt' => $trialEndsAt,
            'restrictedMode' => $restrictedMode,
            'consecutiveSubscriptions' => $consecutiveSubscriptions,
            'purchasedSubscriptions' => $purchasedSubscriptions,
        ]);
    }

    /**
     * Download an invoice PDF
     */
    public function downloadInvoice($invoiceId)
    {
        return Auth::user()->downloadInvoice($invoiceId, [
            'vendor' => config('app.name'),
            'product' => 'Abonnement',
        ]);
    }

    /**
     * Cancel a subscription
     */
    public function cancel()
    {
        $user = Auth::user();
        $subscription = $user->subscription('default');
        
        if ($subscription->cancelNow()) {
            // Speichere den Zeitpunkt, zu dem das Abonnement endete
            $user->last_subscription_end = Carbon::now();
            $user->save();
            
            return to_route('subscription.index')
                ->with('success', 'Ihr Abonnement wurde gekündigt.');
        }
        
        return back()->with('error', 'Das Abonnement konnte nicht gekündigt werden.');
    }

    /**
     * Resume a subscription
     */
    public function resume()
    {
        $user = Auth::user();
        
        if ($user->subscription('default')->resume()) {
            return to_route('subscription.manage')
                ->with('success', 'Ihr Abonnement wurde fortgesetzt.');
        }
        
        return back()->with('error', 'Das Abonnement konnte nicht fortgesetzt werden.');
    }

    /**
     * Update payment method
     */
    public function updatePaymentMethod(Request $request)
    {
        $user = Auth::user();
        $paymentMethod = $request->input('payment_method');
        
        $user->updateDefaultPaymentMethod($paymentMethod);
        
        return back()->with('success', 'Zahlungsmethode erfolgreich aktualisiert.');
    }

    /**
     * Get list of subscription plans
     */
    private function getPlans()
    {
        // Live-Umgebung: Verwenden der Produktions-Preis-IDs
        $stripeTestMode = false;

        if ($stripeTestMode) {
            // Test-Preis-IDs für den Testmodus
            return [
                [
                    'id' => 'price_1RAxWCFbLhupDbC8MaTVzDDW', // Testpreis ID
                    'name' => 'Basis',
                    'price' => 0.99,
                    'interval' => 'monat',
                    'features' => [
                        'Bis zu 2 Fahrzeuge',
                        'Grundlegende Serviceerinnerungen',
                        'PDF-Berichte',
                    ],
                ],
                [
                    'id' => 'price_1RAxXCFbLhupDbC89yDZL0vy', // Testpreis ID
                    'name' => 'Premium',
                    'price' => 1.99,
                    'interval' => 'monat',
                    'features' => [
                        'Bis zu 5 Fahrzeuge',
                        'Erweiterte Serviceplanung',
                        'Teilesuche-Integration',
                        'Premium PDF-Berichte',
                    ],
                ],
                [
                    'id' => 'price_1RAxXuFbLhupDbC8xmbUeR1h', // Testpreis ID
                    'name' => 'Professional',
                    'price' => 4.99,
                    'interval' => 'monat',
                    'features' => [
                        'Unbegrenzte Fahrzeuge',
                        'Erweiterte Serviceplanung',
                        'Teilesuche-Integration',
                        'Premium PDF-Berichte',
                        'API-Zugang',
                        'Prioritäts-Support',
                    ],
                ],
            ];
        } else {
            // WICHTIG: Diese Preis-IDs müssen mit denen in Ihrem Stripe-Dashboard übereinstimmen
            // Sie können die IDs im Stripe Dashboard unter Produkte > Preise finden
            return [
                [
                    'id' => 'price_1RAwm7FbLhupDbC8WBFS4MMI', // price_1RAwLkFbLhupDbC8TEnFHStA WICHTIG: Preis-ID (beginnt mit price_), nicht Produkt-ID
                    'name' => 'Basis',
                    'price' => 0.01,
                    'interval' => 'monat',
                    'features' => [
                        'Bis zu 2 Fahrzeuge',
                        'Grundlegende Serviceerinnerungen',
                        'PDF-Berichte',
                    ],
                ],
                [
                    'id' => 'price_1RAwNVFbLhupDbC8JG6MNRw9', // WICHTIG: Preis-ID (beginnt mit price_), nicht Produkt-ID
                    'name' => 'Premium',
                    'price' => 1.99,
                    'interval' => 'monat',
                    'features' => [
                        'Bis zu 5 Fahrzeuge',
                        'Erweiterte Serviceplanung',
                        'Teilesuche-Integration',
                        'Premium PDF-Berichte',
                    ],
                ],
                [
                    'id' => 'price_1RAwORFbLhupDbC8ZuzL1Lcj', // WICHTIG: Preis-ID (beginnt mit price_), nicht Produkt-ID
                    'name' => 'Professional',
                    'price' => 4.99,
                    'interval' => 'monat',
                    'features' => [
                        'Unbegrenzte Fahrzeuge',
                        'Erweiterte Serviceplanung',
                        'Teilesuche-Integration',
                        'Premium PDF-Berichte',
                        'API-Zugang',
                        'Prioritäts-Support',
                    ],
                ],
            ];
        }
    }
}
