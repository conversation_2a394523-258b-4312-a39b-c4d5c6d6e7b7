<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Barryvdh\DomPDF\Facade\Pdf;

class VehicleController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $vehicles = Vehicle::where('user_id', Auth::id())
            ->with('maintenanceLogs', 'serviceReminders')
            ->withCount('maintenanceLogs')
            ->orderBy('is_active', 'desc')
            ->orderBy('make')
            ->orderBy('model')
            ->get();

        return Inertia::render('Vehicles/Index', [
            'vehicles' => $vehicles,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Vehicles/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'make' => ['required', 'string', 'max:255'],
            'model' => ['required', 'string', 'max:255'],
            'type' => ['nullable', 'string', 'max:255'],
            'hsn' => ['nullable', 'string', 'max:20'],
            'tsn' => ['nullable', 'string', 'max:20'],
            'vin' => ['nullable', 'string', 'max:17', Rule::unique('vehicles')->where(fn ($query) => $query->where('user_id', Auth::id()))],
            'license_plate' => ['nullable', 'string', 'max:20'],
            'year' => ['nullable', 'integer', 'min:1900', 'max:' . (date('Y') + 1)],
            'mileage' => ['nullable', 'integer', 'min:0'],
            'annual_mileage' => ['nullable', 'integer', 'min:0', 'max:200000'],
            'purchase_date' => ['nullable', 'date'],
            'purchase_price' => ['nullable', 'numeric', 'min:0'],
            'notes' => ['nullable', 'string'],
            'color' => ['nullable', 'string', 'max:50'],
            'fuel_type' => ['nullable', 'string', 'max:50'],
            'power' => ['nullable', 'integer', 'min:0'],
            'engine_size' => ['nullable', 'string', 'max:20'],
            'transmission' => ['nullable', 'string', 'max:50'],
            'image' => ['nullable', 'image', 'max:2048'],
            'tire_sizes' => ['nullable', 'array'],
            'parts' => ['nullable', 'array'],
        ]);

        // Bild hochladen, falls vorhanden
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store('vehicles', 'public');
            $validated['image'] = $path;
        }

        $validated['user_id'] = Auth::id();
        $validated['is_active'] = true;
        
        // Setze das letzte Aktualisierungsdatum des Kilometerstandes auf heute, falls jährliche Fahrleistung angegeben wurde
        if (!empty($validated['annual_mileage'])) {
            $validated['last_mileage_update'] = now();
        }

        $vehicle = Vehicle::create($validated);

        return redirect()->route('vehicles.show', $vehicle)
            ->with('success', 'Fahrzeug erfolgreich angelegt.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Vehicle $vehicle)
    {
        $this->authorize('view', $vehicle);

        $vehicle->load([
            'maintenanceLogs' => function ($query) {
                $query->orderBy('date', 'desc')
                      ->with(['parts'])
                      ->select(['id', 'vehicle_id', 'user_id', 'title', 'type', 'date', 'mileage', 'cost', 'description', 'location', 'next_inspection_date', 'created_at', 'updated_at']);
            },
            'serviceReminders' => function ($query) {
                $query->orderBy('due_date');
            },
            'attachments',
            'quickAccessEntries' => function ($query) {
                $query->orderBy('date', 'desc');
            }
        ]);

        // Gesamte Wartungskosten berechnen inklusive Teilekosten
        $maintenanceCosts = $vehicle->maintenanceLogs->sum(function ($log) {
            $laborCost = $log->cost ?? 0;
            $partsCost = $log->parts->sum(function ($part) {
                return ($part->cost ?? 0) * ($part->quantity ?? 1);
            });
            return $laborCost + $partsCost;
        });

        // Kosten aus Schnellzugriff-Einträgen berechnen
        $quickAccessCosts = $vehicle->quickAccessEntries->sum(function ($entry) {
            return $entry->amount ?? 0;
        });

        // Gesamtkosten berechnen (Wartung + Schnellzugriff)
        $totalCosts = $maintenanceCosts + $quickAccessCosts;

        // Anstehende Service-Termine
        $upcomingServices = $vehicle->serviceReminders()
            ->where('status', 'pending')
            ->orderBy('due_date')
            ->limit(5)
            ->get();

        return Inertia::render('Vehicles/Show', [
            'vehicle' => $vehicle,
            'totalCosts' => $totalCosts,
            'maintenanceCosts' => $maintenanceCosts,
            'quickAccessCosts' => $quickAccessCosts,
            'upcomingServices' => $upcomingServices,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        return Inertia::render('Vehicles/Edit', [
            'vehicle' => $vehicle,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        $validated = $request->validate([
            'make' => ['required', 'string', 'max:255'],
            'model' => ['required', 'string', 'max:255'],
            'type' => ['nullable', 'string', 'max:255'],
            'hsn' => ['nullable', 'string', 'max:20'],
            'tsn' => ['nullable', 'string', 'max:20'],
            'vin' => ['nullable', 'string', 'max:17', Rule::unique('vehicles')->where(fn ($query) => $query->where('user_id', Auth::id()))->ignore($vehicle->id)],
            'license_plate' => ['nullable', 'string', 'max:20'],
            'year' => ['nullable', 'integer', 'min:1900', 'max:' . (date('Y') + 1)],
            'mileage' => ['nullable', 'integer', 'min:0'],
            'annual_mileage' => ['nullable', 'integer', 'min:0', 'max:200000'],
            'purchase_date' => ['nullable', 'date'],
            'purchase_price' => ['nullable', 'numeric', 'min:0'],
            'notes' => ['nullable', 'string'],
            'color' => ['nullable', 'string', 'max:50'],
            'fuel_type' => ['nullable', 'string', 'max:50'],
            'power' => ['nullable', 'integer', 'min:0'],
            'engine_size' => ['nullable', 'string', 'max:20'],
            'transmission' => ['nullable', 'string', 'max:50'],
            'image' => ['nullable', 'image', 'max:2048'],
            'is_active' => ['sometimes', 'boolean'],
            'tire_sizes' => ['nullable', 'array'],
            'parts' => ['nullable', 'array'],
        ]);

        // Bild hochladen, falls vorhanden
        if ($request->hasFile('image')) {
            // Altes Bild löschen, falls vorhanden
            if ($vehicle->image) {
                Storage::disk('public')->delete($vehicle->image);
            }

            $path = $request->file('image')->store('vehicles', 'public');
            $validated['image'] = $path;
        } else {
            // Wenn kein neues Bild hochgeladen wurde, behalte das vorhandene Bild bei
            unset($validated['image']);
        }
        
        // Überprüfe, ob der jährliche Kilometerstand geändert wurde oder neu hinzugefügt wurde
        $annualMileageChanged = isset($validated['annual_mileage']) && 
                               ($vehicle->annual_mileage != $validated['annual_mileage'] || $vehicle->last_mileage_update === null);
        
        if ($annualMileageChanged) {
            $validated['last_mileage_update'] = now();
        }

        $vehicle->update($validated);

        return redirect()->route('vehicles.show', $vehicle)
            ->with('success', 'Fahrzeug erfolgreich aktualisiert.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vehicle $vehicle)
    {
        $this->authorize('delete', $vehicle);

        $vehicle->delete();

        return redirect()->route('vehicles.index')
            ->with('success', 'Fahrzeug wurde in den Papierkorb verschoben. Sie können es innerhalb von 30 Tagen wiederherstellen.');
    }

    /**
     * Zeigt den Papierkorb mit allen gelöschten Fahrzeugen an.
     */
    public function trash()
    {
        $trashedVehicles = Vehicle::onlyTrashed()
            ->where('user_id', Auth::id())
            ->orderBy('deleted_at', 'desc')
            ->get();

        return Inertia::render('Vehicles/Trash', [
            'trashedVehicles' => $trashedVehicles,
        ]);
    }

    /**
     * Stellt ein gelöschtes Fahrzeug wieder her.
     */
    public function restore($id)
    {
        $vehicle = Vehicle::onlyTrashed()->findOrFail($id);
        
        $this->authorize('restore', $vehicle);
        
        $vehicle->restore();
        
        return redirect()->route('vehicles.show', $vehicle)
            ->with('success', 'Fahrzeug wurde erfolgreich wiederhergestellt.');
    }

    /**
     * Löscht ein Fahrzeug endgültig.
     */
    public function forceDelete($id)
    {
        $vehicle = Vehicle::onlyTrashed()->findOrFail($id);
        
        $this->authorize('forceDelete', $vehicle);
        
        if ($vehicle->image) {
            Storage::disk('public')->delete($vehicle->image);
        }
        
        $vehicle->forceDelete();
        
        return redirect()->route('vehicles.trash')
            ->with('success', 'Fahrzeug wurde endgültig gelöscht.');
    }

    /**
     * Generiere einen PDF-Bericht für das Fahrzeug
     */
    public function generateReport(Vehicle $vehicle)
    {
        $this->authorize('view', $vehicle);

        $vehicle->load([
            'maintenanceLogs.parts',
            'attachments',
            'serviceReminders',
        ]);

        // Gesamte Wartungskosten berechnen inklusive Teilekosten
        $totalCosts = $vehicle->maintenanceLogs->sum(function ($log) {
            $laborCost = $log->cost ?? 0;
            $partsCost = $log->parts->sum(function ($part) {
                return ($part->cost ?? 0) * ($part->quantity ?? 1);
            });
            return $laborCost + $partsCost;
        });

        // Anstehende Services für das Fahrzeug ermitteln
        $upcomingServices = $vehicle->serviceReminders()
            ->where(function ($query) use ($vehicle) {
                $query->where('status', 'pending')
                    ->orWhere('status', 'overdue');
            })
            ->orderBy('due_date')
            ->get();

        $pdf = PDF::loadView('pdf.vehicle-report', [
            'vehicle' => $vehicle,
            'totalCosts' => $totalCosts,
            'upcomingServices' => $upcomingServices,
        ]);

        return $pdf->download($vehicle->make . '-' . $vehicle->model . '-Bericht.pdf');
    }
}
