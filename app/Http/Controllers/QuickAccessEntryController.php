<?php

namespace App\Http\Controllers;

use App\Models\QuickAccessEntry;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class QuickAccessEntryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Vehicle $vehicle): JsonResponse
    {
        // Temporarily disable auth check for testing
        /*
        // Ensure the user owns the vehicle
        if ($vehicle->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this vehicle'
            ], 403);
        }
        */

        $entries = $vehicle->quickAccessEntries()
            ->orderBy('date', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $entries
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Vehicle $vehicle): JsonResponse
    {
        // Temporarily disable auth check for testing
        /*
        // Ensure the user owns the vehicle
        if ($vehicle->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this vehicle'
            ], 403);
        }
        */

        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:refueling,car_wash,parking_fee',
            'date' => 'required|date',
            'mileage' => 'nullable|integer',
            'amount' => 'required|numeric|min:0',
            'quantity' => 'nullable|numeric|min:0',
            'price_per_unit' => 'nullable|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $entry = $vehicle->quickAccessEntries()->create($request->all());

        return response()->json([
            'success' => true,
            'data' => $entry
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Vehicle $vehicle, $quickAccessEntryId): JsonResponse
    {
        // Temporarily disable auth check for testing
        /*
        // Ensure the user owns the vehicle
        if ($vehicle->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this vehicle'
            ], 403);
        }
        */

        // Find the quick access entry manually by ID
        $entry = QuickAccessEntry::find($quickAccessEntryId);

        // Check if entry exists
        if (!$entry) {
            return response()->json([
                'success' => false,
                'message' => 'Entry not found'
            ], 404);
        }

        // Check if the entry belongs to the vehicle
        if ($entry->vehicle_id !== $vehicle->id) {
            return response()->json([
                'success' => false,
                'message' => 'Entry does not belong to this vehicle'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $entry
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vehicle $vehicle, $quickAccessEntryId): JsonResponse
    {
        // Temporarily disable auth check for testing
        /*
        // Ensure the user owns the vehicle
        if ($vehicle->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this vehicle'
            ], 403);
        }
        */

        // Find the quick access entry manually by ID
        $entry = QuickAccessEntry::find($quickAccessEntryId);

        // Check if entry exists
        if (!$entry) {
            return response()->json([
                'success' => false,
                'message' => 'Entry not found'
            ], 404);
        }

        // Check if the entry belongs to the vehicle
        if ($entry->vehicle_id !== $vehicle->id) {
            return response()->json([
                'success' => false,
                'message' => 'Entry does not belong to this vehicle'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'type' => 'sometimes|required|string|in:refueling,car_wash,parking_fee',
            'date' => 'sometimes|required|date',
            'mileage' => 'nullable|integer',
            'amount' => 'sometimes|required|numeric|min:0',
            'quantity' => 'nullable|numeric|min:0',
            'price_per_unit' => 'nullable|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $entry->update($request->all());

        return response()->json([
            'success' => true,
            'data' => $entry
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vehicle $vehicle, $quickAccessEntryId): JsonResponse
    {
        // Temporarily disable auth check for testing
        /*
        // Ensure the user owns the vehicle
        if ($vehicle->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to this vehicle'
            ], 403);
        }
        */

        // Find the quick access entry manually by ID
        $entry = QuickAccessEntry::find($quickAccessEntryId);

        // Check if entry exists
        if (!$entry) {
            return response()->json([
                'success' => false,
                'message' => 'Entry not found'
            ], 404);
        }

        // Check if the entry belongs to the vehicle
        if ($entry->vehicle_id !== $vehicle->id) {
            return response()->json([
                'success' => false,
                'message' => 'Entry does not belong to this vehicle'
            ], 403);
        }

        $entry->delete();

        return response()->json([
            'success' => true,
            'message' => 'Entry deleted successfully'
        ]);
    }

    /**
     * Get all entries for a vehicle (public access for debugging)
     */
    public function getEntriesForVehicle(Request $request, $vehicleId): JsonResponse
    {
        $vehicle = Vehicle::find($vehicleId);

        if (!$vehicle) {
            return response()->json([
                'success' => false,
                'message' => 'Vehicle not found'
            ], 404);
        }

        $entries = $vehicle->quickAccessEntries()
            ->orderBy('date', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $entries
        ]);
    }
}
