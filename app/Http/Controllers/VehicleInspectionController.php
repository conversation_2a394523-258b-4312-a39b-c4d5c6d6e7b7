<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\MaintenanceLog;
use Illuminate\Http\JsonResponse;

class VehicleInspectionController extends Controller
{
    /**
     * Get the latest TÜV/HU inspection for a vehicle
     *
     * @param Vehicle $vehicle
     * @return JsonResponse
     */
    public function getLatestInspection(Vehicle $vehicle): JsonResponse
    {
        // Find the latest inspection entry with a next_inspection_date
        $latestInspection = MaintenanceLog::where('vehicle_id', $vehicle->id)
            ->where('type', 'inspection')
            ->whereNotNull('next_inspection_date')
            ->orderBy('date', 'desc')
            ->first();

        if (!$latestInspection) {
            return response()->json(['message' => 'No inspection found'], 404);
        }

        return response()->json([
            'id' => $latestInspection->id,
            'date' => $latestInspection->date,
            'next_inspection_date' => $latestInspection->next_inspection_date,
            'title' => $latestInspection->title,
        ]);
    }
} 