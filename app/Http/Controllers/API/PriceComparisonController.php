<?php

declare(strict_types=1);

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PriceComparisonController extends Controller
{
    /**
     * Liste der verfügbaren Online-Shops für den Preisvergleich
     * 
     * @var array<string, array<string, mixed>>
     */
    private array $stores = [
        'amazon' => [
            'name' => 'Amazon',
            'logo' => '/images/stores/amazon.png',
            'base_rating' => 4.7,
        ],
        'ebay' => [
            'name' => 'eBay',
            'logo' => '/images/stores/ebay.png',
            'base_rating' => 4.2,
        ],
        'autoteile24' => [
            'name' => 'AutoTeile24',
            'logo' => '/images/stores/autoteile24.png',
            'base_rating' => 4.5,
        ],
        'autodoc' => [
            'name' => 'AUTODOC',
            'logo' => '/images/stores/autodoc.png',
            'base_rating' => 4.3,
        ],
        'kfzteile24' => [
            'name' => 'kfzteile24',
            'logo' => '/images/stores/kfzteile24.png',
            'base_rating' => 4.4,
        ],
    ];

    /**
     * Preisvergleich für ein Ersatzteil abrufen
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getComparison(Request $request): JsonResponse
    {
        try {
            // Validiere die Request-Parameter
            $validated = $request->validate([
                'part_number' => 'required|string',
                'manufacturer' => 'required|string',
                'name' => 'required|string',
            ]);

            $partNumber = $validated['part_number'];
            $manufacturer = $validated['manufacturer'];
            $name = $validated['name'];
            
            // Erstelle ein Suchbegriff aus den Parametern
            $searchTerm = "{$manufacturer} {$name} {$partNumber}";
            
            // Cache-Key generieren, um wiederholte Abfragen zu optimieren
            $cacheKey = "price_comparison_{$partNumber}_" . md5($searchTerm);
            
            // Prüfe, ob Ergebnisse bereits im Cache vorhanden sind
            if (Cache::has($cacheKey)) {
                return response()->json([
                    'success' => true,
                    'data' => Cache::get($cacheKey),
                    'cached' => true,
                ]);
            }
            
            // Preise von verschiedenen Quellen sammeln
            $results = $this->collectPrices($partNumber, $searchTerm);
            
            // Ergebnisse für 24 Stunden cachen
            Cache::put($cacheKey, $results, now()->addHours(24));
            
            return response()->json([
                'success' => true,
                'data' => $results,
                'cached' => false,
            ]);
        } catch (\Exception $e) {
            Log::error('Fehler beim Preisvergleich: ' . $e->getMessage(), [
                'request' => $request->all(),
                'exception' => $e,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Preisvergleich',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Preise von verschiedenen Shops sammeln
     * 
     * @param string $partNumber
     * @param string $searchTerm
     * @return array
     */
    private function collectPrices(string $partNumber, string $searchTerm): array
    {
        $results = [];

        foreach ($this->stores as $storeKey => $storeInfo) {
            $priceData = $this->fetchPriceFromStore($storeKey, $partNumber, $searchTerm);
            
            if ($priceData !== null) {
                $results[] = $priceData;
            }
        }

        // Sortiere die Ergebnisse nach Preis (aufsteigend)
        usort($results, function ($a, $b) {
            return $a['price'] <=> $b['price'];
        });

        return $results;
    }

    /**
     * Preisdaten von einem bestimmten Shop abrufen
     * 
     * @param string $store
     * @param string $partNumber
     * @param string $searchTerm
     * @return array|null
     */
    private function fetchPriceFromStore(string $store, string $partNumber, string $searchTerm): ?array
    {
        try {
            // In einer echten Implementierung würden hier API-Aufrufe zu den jeweiligen Shops stehen
            // oder Scraping-Logik, um die Preise zu extrahieren
            
            // Für den Moment implementieren wir eine Simulation mit realistischen Preisen
            $price = null;
            $storeInfo = $this->stores[$store];
            
            switch ($store) {
                case 'amazon':
                    $price = $this->simulateApiRequest($store, $partNumber, 1.05, 1.15);
                    break;
                case 'ebay':
                    $price = $this->simulateApiRequest($store, $partNumber, 0.90, 1.05);
                    break;
                case 'autoteile24':
                    $price = $this->simulateApiRequest($store, $partNumber, 0.95, 1.10);
                    break;
                case 'autodoc':
                    $price = $this->simulateApiRequest($store, $partNumber, 0.85, 1.00);
                    break;
                case 'kfzteile24':
                    $price = $this->simulateApiRequest($store, $partNumber, 0.92, 1.08);
                    break;
            }
            
            if ($price === null) {
                return null;
            }
            
            // Erzeuge eine leichte Variation im Rating
            $ratingVariation = (mt_rand(-5, 5) / 100);
            $rating = max(1, min(5, $storeInfo['base_rating'] + $ratingVariation));
            
            return [
                'store' => $storeInfo['name'],
                'price' => $price,
                'url' => $this->generateStoreUrl($store, $searchTerm),
                'rating' => round($rating, 1),
                'logo' => $storeInfo['logo'],
            ];
        } catch (\Exception $e) {
            Log::warning("Fehler beim Abrufen von Preisdaten von {$store}: " . $e->getMessage(), [
                'store' => $store,
                'part_number' => $partNumber,
                'search_term' => $searchTerm,
                'exception' => $e,
            ]);
            return null;
        }
    }

    /**
     * Simuliert einen API-Request und gibt einen konsistenten aber zufälligen Preis zurück
     * 
     * @param string $store
     * @param string $partNumber
     * @param float $minMultiplier
     * @param float $maxMultiplier
     * @return float|null
     */
    private function simulateApiRequest(string $store, string $partNumber, float $minMultiplier, float $maxMultiplier): ?float
    {
        // Erzeuge einen Pseudo-Zufallswert basierend auf der Teilenummer und dem Shop
        // Dadurch erhalten wir bei gleicher Teilenummer immer ähnliche, aber nicht identische Werte
        $seed = crc32($store . $partNumber);
        mt_srand($seed);
        
        // Basispreis ist eine Funktion der Teilenummer
        $basePrice = 30 + (hexdec(substr(md5($partNumber), 0, 4)) % 200);
        
        // Erzeuge einen Multiplikator im angegebenen Bereich
        $range = $maxMultiplier - $minMultiplier;
        $multiplier = $minMultiplier + (mt_rand(0, 1000) / 1000) * $range;
        
        // Berechne den endgültigen Preis
        $price = $basePrice * $multiplier;
        
        // Auf zwei Nachkommastellen runden
        $price = round($price, 2);
        
        // In ca. 10% der Fälle geben wir null zurück, um zu simulieren, dass einige Anfragen keine Ergebnisse liefern
        return (mt_rand(1, 10) > 1) ? $price : null;
    }

    /**
     * Generiert eine URL für den Shop basierend auf dem Suchbegriff
     * 
     * @param string $store
     * @param string $searchTerm
     * @return string
     */
    private function generateStoreUrl(string $store, string $searchTerm): string
    {
        $encodedSearchTerm = urlencode($searchTerm);
        
        return match($store) {
            'amazon' => "https://www.amazon.de/s?k={$encodedSearchTerm}",
            'ebay' => "https://www.ebay.de/sch/i.html?_nkw={$encodedSearchTerm}",
            'autoteile24' => "https://www.autoteile24.de/search?query={$encodedSearchTerm}",
            'autodoc' => "https://www.autodoc.de/search?keyword={$encodedSearchTerm}",
            'kfzteile24' => "https://www.kfzteile24.de/search?query={$encodedSearchTerm}",
            default => "https://www.google.com/search?q={$encodedSearchTerm}",
        };
    }
} 