<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class MaintenanceTypeController extends Controller
{
    /**
     * Die verfügbaren Wartungstypen mit ihren Bezeichnungen
     */
    private static $maintenanceTypes = [
        'service' => 'Wartung',
        'repair' => 'Reparatur',
        'modification' => 'Umbau',
        'inspection' => 'TÜV/HU',
        'purchase' => 'Kauf',
        'sale' => 'Verkauf',
        'insurance' => 'Versicherung',
        'refueling' => 'Tanken',
        'carwash' => 'Autowäsche',
        'parking' => 'Parkgebühren',
        'tires' => 'Reifen',
        'other' => 'Sonstiges'
    ];

    /**
     * Farbklassen für die verschiedenen Typen (für UI-Badges)
     */
    private static $typeClasses = [
        'repair' => 'badge-error',
        'service' => 'badge-info',
        'modification' => 'badge-secondary',
        'inspection' => 'badge-warning',
        'purchase' => 'badge-success',
        'sale' => 'badge-primary',
        'refueling' => 'badge-accent',
        'carwash' => 'badge-info',
        'parking' => 'badge-neutral',
        'tires' => 'badge-accent',
        'other' => 'badge-neutral'
    ];

    /**
     * Display a listing of the maintenance types.
     */
    public function index()
    {
        return response()->json([
            'types' => self::$maintenanceTypes,
            'typeClasses' => self::$typeClasses
        ]);
    }

    /**
     * Gibt statistische Informationen zu Wartungseinträgen nach Typ zurück
     */
    public function stats(Request $request, $vehicleId = null)
    {
        $query = DB::table('maintenance_logs')
            ->select('type', DB::raw('count(*) as count'), DB::raw('sum(cost) as total_cost'))
            ->groupBy('type');

        if ($vehicleId) {
            $query->where('vehicle_id', $vehicleId);
        } else if ($request->user()) {
            // Wenn kein Fahrzeug angegeben, aber Benutzer authentifiziert,
            // nur Einträge für Fahrzeuge des aktuellen Benutzers
            $vehicleIds = DB::table('vehicles')
                ->where('user_id', $request->user()->id)
                ->pluck('id');

            $query->whereIn('vehicle_id', $vehicleIds);
        }

        $stats = $query->get();

        // Typlabels hinzufügen
        $stats = $stats->map(function($item) {
            $item->label = self::$maintenanceTypes[$item->type] ?? $item->type;
            $item->class = self::$typeClasses[$item->type] ?? 'badge-neutral';
            return $item;
        });

        return response()->json($stats);
    }

    /**
     * Return all available maintenance types for use in forms
     */
    public function getTypesForForms()
    {
        $types = collect(self::$maintenanceTypes)
            ->map(function($label, $value) {
                return [
                    'value' => $value,
                    'label' => $label,
                    'class' => self::$typeClasses[$value] ?? 'badge-neutral'
                ];
            })
            ->values()
            ->sortBy('label')
            ->toArray();

        return response()->json($types);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
