<?php

namespace App\Http\Controllers;

use App\Models\MaintenanceLog;
use App\Models\ServiceReminder;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class CalendarController extends Controller
{
    /**
     * Display the calendar view with all service reminders and maintenance logs.
     */
    public function index(Request $request)
    {
        // Get all user's vehicles
        $vehicles = Vehicle::where('user_id', Auth::id())->get();
        $vehicleIds = $vehicles->pluck('id')->toArray();

        // Get date range from request or default to current month
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))
            : Carbon::now()->startOfMonth();

        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))
            : Carbon::now()->endOfMonth();

        // Get service reminders
        $reminders = ServiceReminder::with('vehicle')
            ->whereIn('vehicle_id', $vehicleIds)
            ->orderBy('due_date')
            ->get();

        // Check for overdue reminders and update their status
        foreach ($reminders as $reminder) {
            if ($reminder->status === 'pending' && $reminder->due_date && Carbon::parse($reminder->due_date)->isPast()) {
                $reminder->status = 'overdue';
                $reminder->save();
            }
        }

        // Get maintenance logs
        $maintenanceLogs = MaintenanceLog::with('vehicle')
            ->whereIn('vehicle_id', $vehicleIds)
            ->whereBetween('date', [$startDate->toDateString(), $endDate->toDateString()])
            ->orderBy('date', 'desc')
            ->get();

        // Get upcoming service reminders
        $upcomingReminders = $reminders->filter(function ($reminder) {
            return $reminder->status === 'pending' || $reminder->status === 'overdue';
        })->take(5);

        return Inertia::render('Calendar/Index', [
            'reminders' => $reminders,
            'maintenance_logs' => $maintenanceLogs,
            'upcoming_reminders' => $upcomingReminders,
            'vehicles' => $vehicles,
            'date_range' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString(),
            ],
            'stats' => [
                'pending_count' => $reminders->where('status', 'pending')->count(),
                'overdue_count' => $reminders->where('status', 'overdue')->count(),
                'completed_count' => $reminders->where('status', 'completed')->count(),
                'maintenance_count' => $maintenanceLogs->count(),
            ]
        ]);
    }

    /**
     * Update a service reminder status (e.g., mark as completed)
     */
    public function updateReminderStatus(Request $request, ServiceReminder $reminder)
    {
        $validated = $request->validate([
            'status' => 'required|string|in:pending,completed,overdue',
        ]);

        $reminder->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Status erfolgreich aktualisiert',
            'reminder' => $reminder
        ]);
    }

    /**
     * Create a quick reminder from the calendar
     */
    public function createQuickReminder(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'vehicle_id' => 'required|exists:vehicles,id',
            'due_date' => 'required|date',
            'priority' => 'required|string|in:high,medium,low',
            'notes' => 'nullable|string',
        ]);

        // Verify the vehicle belongs to the current user
        $vehicle = Vehicle::findOrFail($validated['vehicle_id']);
        if ($vehicle->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Sie haben keine Berechtigung für dieses Fahrzeug.'
            ], 403);
        }

        // Create the reminder
        $reminder = new ServiceReminder();
        $reminder->title = $validated['title'];
        $reminder->vehicle_id = $validated['vehicle_id'];
        $reminder->due_date = $validated['due_date'];
        $reminder->priority = $validated['priority'];
        $reminder->description = $validated['notes'] ?? null;
        $reminder->status = 'pending';
        $reminder->user_id = Auth::id();
        $reminder->save();

        return response()->json([
            'success' => true,
            'message' => 'Servicerinnerung erfolgreich erstellt',
            'reminder' => $reminder->load('vehicle')
        ]);
    }
}
