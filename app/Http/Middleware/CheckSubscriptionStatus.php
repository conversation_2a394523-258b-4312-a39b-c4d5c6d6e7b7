<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionStatus
{
    /**
     * Liste der Routen, die auch im eingeschränkten Modus verfügbar sein sollen
     */
    protected $allowedRoutes = [
        'dashboard',
        'profile.edit',
        'profile.update',
        'profile.destroy',
        'vehicles.index',
        'vehicles.show',
        'subscription.index',
        'subscription.checkout',
        'subscription.success',
        'subscription.manage',
        'subscription.payment-method.update',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        if (!$user) {
            return $next($request);
        }
        
        // Überprüfe, ob im eingeschränkten Modus
        $hasActiveSubscription = $user->subscription('default') && $user->subscription('default')->active();
        $inTrialPeriod = $user->trial_ends_at && Carbon::now()->lt($user->trial_ends_at);
        
        // Setze eingeschränkten Modus basierend auf Abonnement- und Testphase-Status
        if (!$hasActiveSubscription && !$inTrialPeriod) {
            $user->restricted_mode = true;
            $user->save();
            
            // Wenn Routenname in nicht erlaubten Routen und es handelt sich um eine Schreiboperation
            $currentRoute = $request->route()->getName();
            $isWriteOperation = in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE']);
            
            if ($isWriteOperation && !in_array($currentRoute, $this->allowedRoutes)) {
                return redirect()->route('subscription.index')
                    ->with('error', 'Ihr Abonnement ist abgelaufen. Bitte erneuern Sie es, um alle Funktionen nutzen zu können.');
            }
        } else {
            // Wenn aktives Abonnement oder in Testphase, stelle sicher, dass eingeschränkter Modus deaktiviert ist
            if ($user->restricted_mode) {
                $user->restricted_mode = false;
                $user->save();
            }
        }

        // Füge Statusflag zum View-Kontext hinzu, damit wir es in der UI verwenden können
        view()->share('restrictedMode', $user->restricted_mode);
        
        return $next($request);
    }
}
