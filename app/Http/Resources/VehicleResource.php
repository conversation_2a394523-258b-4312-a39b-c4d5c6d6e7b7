<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'make' => $this->make,
            'model' => $this->model,
            'type' => $this->type,
            'hsn' => $this->hsn,
            'tsn' => $this->tsn,
            'vin' => $this->vin,
            'license_plate' => $this->license_plate,
            'year' => $this->year,
            'mileage' => $this->mileage ?? 0,
            'annual_mileage' => $this->annual_mileage,
            'last_mileage_update' => $this->last_mileage_update,
            'purchase_date' => $this->purchase_date,
            'purchase_price' => $this->purchase_price,
            'notes' => $this->notes,
            'color' => $this->color,
            'fuel_type' => $this->fuel_type,
            'power' => $this->power,
            'engine_size' => $this->engine_size,
            'transmission' => $this->transmission,
            'image' => $this->image ? asset('storage/' . $this->image) : null,
            'is_active' => $this->is_active,
            'tire_sizes' => $this->tire_sizes ?? [],
            'parts' => $this->parts ?? [],
            'age' => $this->age,
            'full_name' => $this->full_name,
            'maintenance_logs' => $this->whenLoaded('maintenanceLogs'),
            'service_reminders' => $this->whenLoaded('serviceReminders'),
            'attachments' => $this->whenLoaded('attachments'),
            'quick_access_entries' => $this->whenLoaded('quickAccessEntries'),
            'maintenance_logs_count' => $this->maintenance_logs_count,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
        ];
    }
} 