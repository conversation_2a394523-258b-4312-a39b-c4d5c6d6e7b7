<?php

namespace App\Providers;

use App\Events\UserRegistered;
use App\Listeners\SetupTrialPeriodAfterRegistration;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        UserRegistered::class => [
            SetupTrialPeriodAfterRegistration::class,
        ],
        Registered::class => [
            // Wenn Illuminate\Auth\Events\Registered getriggert wird,
            // feuern wir auch unser eigenes Event ab
        ],
    ];

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Verbinde das Laravel-Event mit unserem eigenen Event
        Event::listen(Registered::class, function (Registered $event) {
            event(new UserRegistered($event->user));
        });
    }
}
