<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class MaintenanceLog extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'vehicle_id',
        'user_id',
        'title',
        'type',
        'date',
        'mileage',
        'cost',
        'description',
        'location',
        'invoice_number',
        'is_recurring',
        'recurring_interval',
        'recurring_mileage',
        'next_inspection_date',
    ];

    protected $casts = [
        'date' => 'date',
        'mileage' => 'integer',
        'cost' => 'decimal:2',
        'is_recurring' => 'boolean',
        'recurring_interval' => 'integer',
        'recurring_mileage' => 'integer',
        'next_inspection_date' => 'date',
    ];

    /**
     * Beziehung zum Fahrzeug
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Beziehung zum <PERSON>er, der den Eintrag erstellt hat
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Beziehung zu den direkt diesem Wartungseintrag zugeordneten Teilen
     */
    public function parts(): HasMany
    {
        return $this->hasMany(Part::class);
    }

    /**
     * Beziehung zu allgemeinen Ersatzteilen über die Pivot-Tabelle
     * (wird nur verwendet, wenn Teile aus einem zentralen Katalog ausgewählt werden)
     */
    public function catalogParts(): BelongsToMany
    {
        return $this->belongsToMany(Part::class, 'maintenance_parts')
            ->withPivot('quantity', 'price')
            ->withTimestamps();
    }

    /**
     * Beziehung zu allen Anhängen des Wartungseintrags
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Beziehung zu Serviceerinnerungen, die durch diesen Wartungseintrag abgeschlossen wurden
     */
    public function serviceReminders(): HasMany
    {
        return $this->hasMany(ServiceReminder::class);
    }

    /**
     * Scope für Wartungseinträge eines bestimmten Typs
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope für Wartungseinträge in einem bestimmten Zeitraum
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Gibt die Gesamtkosten dieser Wartung zurück (inkl. Ersatzteile)
     */
    public function getTotalCostAttribute(): float
    {
        // Kosten der direkt zugeordneten Teile berechnen
        $directPartsCost = $this->parts->sum(function ($part) {
            return ($part->cost ?? 0) * $part->quantity;
        });

        // Kosten der Teile aus dem Katalog berechnen (falls verwendet)
        $catalogPartsCost = $this->catalogParts->sum(function ($part) {
            return $part->pivot->price * $part->pivot->quantity;
        });

        return ($this->cost ?? 0) + $directPartsCost + $catalogPartsCost;
    }
}
