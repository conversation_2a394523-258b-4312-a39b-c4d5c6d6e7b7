<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Vehicle extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'make',
        'model',
        'type',
        'hsn',
        'tsn',
        'vin',
        'license_plate',
        'year',
        'mileage',
        'annual_mileage',
        'last_mileage_update',
        'purchase_date',
        'purchase_price',
        'notes',
        'color',
        'fuel_type',
        'power',
        'engine_size',
        'transmission',
        'image',
        'is_active',
        'tire_sizes',
        'parts',
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'last_mileage_update' => 'date',
        'year' => 'integer',
        'mileage' => 'integer',
        'annual_mileage' => 'integer',
        'power' => 'integer',
        'purchase_price' => 'decimal:2',
        'is_active' => 'boolean',
        'tire_sizes' => 'array',
        'parts' => 'array',
    ];

    /**
     * Beziehung zum Benutzer, dem das Fahrzeug gehört
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Beziehung zu allen Wartungseinträgen des Fahrzeugs
     */
    public function maintenanceLogs(): HasMany
    {
        return $this->hasMany(MaintenanceLog::class);
    }

    /**
     * Beziehung zu allen Serviceerinnerungen des Fahrzeugs
     */
    public function serviceReminders(): HasMany
    {
        return $this->hasMany(ServiceReminder::class);
    }

    /**
     * Beziehung zu allen Anhängen des Fahrzeugs
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Beziehung zu allen Schnellzugriffseinträgen des Fahrzeugs
     */
    public function quickAccessEntries(): HasMany
    {
        return $this->hasMany(QuickAccessEntry::class);
    }

    /**
     * Beziehung zu allen Fahrten des Fahrzeugs (Fahrtenbuch)
     */
    public function trips(): HasMany
    {
        return $this->hasMany(Trip::class);
    }

    /**
     * Scope für aktive Fahrzeuge
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Berechnet das Alter des Fahrzeugs in Jahren
     */
    public function getAgeAttribute(): int
    {
        return $this->year ? now()->year - $this->year : 0;
    }

    /**
     * Gibt den vollständigen Namen des Fahrzeugs zurück (Marke + Modell)
     */
    public function getFullNameAttribute(): string
    {
        return $this->make . ' ' . $this->model;
    }

    /**
     * Aktualisiert den Kilometerstand basierend auf der jährlichen Fahrleistung
     */
    public function updateMileageFromAnnualRate(): bool
    {
        if (!$this->annual_mileage || $this->annual_mileage <= 0) {
            return false;
        }

        $lastUpdate = $this->last_mileage_update ?? $this->updated_at ?? now()->subDay();
        $today = now();
        
        // Berechne die vergangenen Tage seit der letzten Aktualisierung
        $daysPassed = $lastUpdate->diffInDays($today);
        
        if ($daysPassed <= 0) {
            return false;
        }
        
        // Berechne die hinzuzufügenden Kilometer (jährliche Fahrleistung / 365 * vergangene Tage)
        $mileageToAdd = round(($this->annual_mileage / 365) * $daysPassed);
        
        if ($mileageToAdd > 0) {
            $this->mileage += $mileageToAdd;
            $this->last_mileage_update = $today;
            return $this->save();
        }
        
        return false;
    }
}
