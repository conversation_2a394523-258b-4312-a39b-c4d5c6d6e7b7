<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Part extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'part_number',
        'manufacturer',
        'description',
        'category',
        'maintenance_log_id',
        'quantity',
        'cost',
        'purchase_url',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'cost' => 'float',
        'quantity' => 'integer',
    ];

    /**
     * Beziehung zum zugehörigen Wartungseintrag
     */
    public function maintenanceLog(): BelongsTo
    {
        return $this->belongsTo(MaintenanceLog::class);
    }

    /**
     * Beziehung zu Wartungseinträgen (über pivot-Tabelle)
     */
    public function maintenanceLogs(): BelongsToMany
    {
        return $this->belongsToMany(MaintenanceLog::class, 'maintenance_parts')
            ->withPivot('quantity', 'price')
            ->withTimestamps();
    }
}
