<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QuickAccessEntry extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'vehicle_id',
        'type',
        'date',
        'mileage',
        'amount',
        'quantity',
        'price_per_unit',
        'location',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'mileage' => 'integer',
        'amount' => 'decimal:2',
        'quantity' => 'decimal:2',
        'price_per_unit' => 'decimal:2',
    ];

    /**
     * Get the vehicle that this entry belongs to.
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get formatted type for display
     */
    public function getFormattedTypeAttribute(): string
    {
        return match($this->type) {
            'refueling' => 'Tanken',
            'car_wash' => 'Autowäsche',
            'parking_fee' => 'Parkgebühren',
            default => $this->type,
        };
    }
}
