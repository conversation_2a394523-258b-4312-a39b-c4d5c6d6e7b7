<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceReminder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'vehicle_id',
        'user_id',
        'title',
        'description',
        'due_date',
        'due_mileage',
        'status',
        'priority',
        'repeat_interval',
        'repeat_unit',
        'is_recurring',
        'recurring_interval',
        'recurring_mileage',
        'maintenance_log_id',
        'processed_at',
    ];

    protected $casts = [
        'due_date' => 'date',
        'due_mileage' => 'integer',
        'repeat_interval' => 'integer',
        'is_recurring' => 'boolean',
        'recurring_interval' => 'integer',
        'recurring_mileage' => 'integer',
        'processed_at' => 'datetime',
    ];

    /**
     * Beziehung zum Fahrzeug
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Beziehung zum Ben<PERSON>, der die Erinnerung erstellt hat
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Beziehung zum Wartungseintrag, der die Erinnerung abgeschlossen hat
     */
    public function maintenanceLog(): BelongsTo
    {
        return $this->belongsTo(MaintenanceLog::class);
    }

    /**
     * Scope für ausstehende Erinnerungen
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope für überfällige Erinnerungen
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue');
    }

    /**
     * Scope für abgeschlossene Erinnerungen
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope für Erinnerungen mit hoher Priorität
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', 'high');
    }

    /**
     * Prüft, ob die Erinnerung überfällig ist
     */
    public function isDue(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        if ($this->due_date && $this->due_date->isPast()) {
            return true;
        }

        if ($this->due_mileage && $this->vehicle && $this->vehicle->mileage >= $this->due_mileage) {
            return true;
        }

        return false;
    }

    /**
     * Erstellt eine Folgeerinnerung basierend auf den wiederkehrenden Einstellungen
     */
    public function createNextReminder(): ?self
    {
        // Prüfen, ob es eine Wiederholung geben soll
        if (!$this->repeat_interval || !$this->repeat_unit) {
            return null;
        }

        $nextDueDate = null;
        if ($this->due_date) {
            switch ($this->repeat_unit) {
                case 'days':
                    $nextDueDate = $this->due_date->addDays($this->repeat_interval);
                    break;
                case 'weeks':
                    $nextDueDate = $this->due_date->addWeeks($this->repeat_interval);
                    break;
                case 'months':
                    $nextDueDate = $this->due_date->addMonths($this->repeat_interval);
                    break;
                case 'years':
                    $nextDueDate = $this->due_date->addYears($this->repeat_interval);
                    break;
            }
        }

        $nextDueMileage = null;
        if ($this->due_mileage) {
            if ($this->repeat_unit === 'km') {
                $nextDueMileage = $this->due_mileage + $this->repeat_interval;
            }
        }

        if (!$nextDueDate && !$nextDueMileage) {
            return null;
        }

        return self::create([
            'vehicle_id' => $this->vehicle_id,
            'user_id' => $this->user_id,
            'title' => $this->title,
            'description' => $this->description,
            'due_date' => $nextDueDate,
            'due_mileage' => $nextDueMileage,
            'status' => 'pending',
            'priority' => $this->priority,
            'repeat_interval' => $this->repeat_interval,
            'repeat_unit' => $this->repeat_unit,
        ]);
    }
}
