<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Trip extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date',
        'vehicle_id',
        'user_id',
        'start_odometer',
        'end_odometer',
        'start_location',
        'end_location',
        'purpose',
        'type',
        'notes',
        'is_recurring',
        'recurrence_type',
        'recurrence_end_date',
        'weekdays',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'start_odometer' => 'integer',
        'end_odometer' => 'integer',
        'is_recurring' => 'boolean',
        'recurrence_end_date' => 'date',
        'weekdays' => 'json',
    ];

    /**
     * Get the vehicle that the trip belongs to.
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the user that the trip belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calculate the distance of the trip.
     */
    public function getDistanceAttribute(): int
    {
        return $this->end_odometer - $this->start_odometer;
    }
}