<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SetupTrialPeriodAfterRegistration implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event): void
    {
        try {
            // Richte die 6-monatige kostenlose Testphase für den neuen Benutzer ein
            $user = $event->user;
            $user->setTrialPeriod();
            
            Log::info('Kostenlose Testphase für Benutzer ' . $user->id . ' eingerichtet.');
        } catch (\Exception $e) {
            Log::error('Fehler beim Einrichten der kostenlosen Testphase: ' . $e->getMessage(), [
                'user_id' => $event->user->id ?? null
            ]);
        }
    }
}
