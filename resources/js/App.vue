<script setup lang="ts">
import { computed, onMounted, watch } from 'vue';
import { usePage } from '@inertiajs/vue3';
import { useTheme } from './composables/useTheme';
import { useAppearance } from './composables/useAppearance';

// Initialize theme management
const { theme } = useTheme();
const { appearance } = useAppearance();

onMounted(() => {
  // Ensure the theme is applied when the app mounts
  const htmlElement = document.documentElement;
  if (appearance.value === 'dark' ||
      (appearance.value === 'system' &&
       window.matchMedia('(prefers-color-scheme: dark)').matches)) {
    htmlElement.classList.add('dark');
    htmlElement.setAttribute('data-theme', 'dark');
  } else {
    htmlElement.classList.remove('dark');
    htmlElement.setAttribute('data-theme', 'light');
  }
});
</script>
