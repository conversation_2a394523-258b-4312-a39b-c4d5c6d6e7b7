<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuItem, SidebarMenuButton } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import { BookOpen, Calendar, Car, Grid, Search, Settings, Mail, Book } from 'lucide-vue-next';
import AppLogo from './AppLogo.vue';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: route('dashboard'),
        icon: Grid,
    },
    {
        title: 'Fahrzeuge',
        href: route('vehicles.index'),
        icon: Car,
    },
    {
        title: 'Fahrtenbuch',
        href: route('fahrtenbuch.index'),
        icon: Book,
    },
    {
        title: 'Servicekalender',
        href: route('calendar'),
        icon: Calendar,
    },
    {
        title: 'Ersatzteile',
        href: route('parts.index'),
        icon: Settings,
        badge: {
            text: 'BETA',
            variant: 'warning'
        },
    },
];

const footerNavItems: NavItem[] = [

    {
        title: 'Feedback senden',
        href: 'mailto:<EMAIL>?subject=Feedback%20zur%20fahrzeugakte.app',
        icon: Mail,
    },
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset">
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('dashboard')">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
