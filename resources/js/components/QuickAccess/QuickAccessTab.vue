<template>
  <div class="card bg-base-100 shadow-xl overflow-hidden">
    <div class="card-body p-0">
      <div class="bg-accent text-accent-content p-4 flex justify-between items-center">
        <h2 class="text-xl font-bold"><PERSON><PERSON><PERSON></h2>
        <div class="flex gap-2">
          <button
            @click="openEntryModal('refueling')"
            class="btn btn-sm btn-accent-content"
            data-type="refueling"
          >
            Tanken
          </button>
          <button
            @click="openEntryModal('car_wash')"
            class="btn btn-sm btn-accent-content"
            data-type="car_wash"
          >
            Autowäsche
          </button>
          <button
            @click="openEntryModal('parking_fee')"
            class="btn btn-sm btn-accent-content"
            data-type="parking_fee"
          >
            Parkgebühren
          </button>
        </div>
      </div>

      <div v-if="isOfflineMode" class="alert alert-warning m-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
        <span>Offline-Modus: Daten werden lokal gespeichert und synchronisiert, wenn die Verbindung wiederhergestellt ist.</span>
      </div>

      <div class="p-6">
        <div class="tabs mb-6">
          <a
            class="tab tab-bordered"
            :class="{ 'tab-active': activeTab === 'all' }"
            @click="activeTab = 'all'"
          >
            Alle
          </a>
          <a
            class="tab tab-bordered"
            :class="{ 'tab-active': activeTab === 'refueling' }"
            @click="activeTab = 'refueling'"
          >
            Tanken
          </a>
          <a
            class="tab tab-bordered"
            :class="{ 'tab-active': activeTab === 'car_wash' }"
            @click="activeTab = 'car_wash'"
          >
            Autowäsche
          </a>
          <a
            class="tab tab-bordered"
            :class="{ 'tab-active': activeTab === 'parking_fee' }"
            @click="activeTab = 'parking_fee'"
          >
            Parkgebühren
          </a>
        </div>

        <div v-if="loading" class="flex justify-center my-8">
          <span class="loading loading-spinner loading-lg"></span>
        </div>

        <div v-else-if="filteredEntries.length === 0" class="text-center py-10">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-accent/30 mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path :d="emptyStateTemplate.icon"></path>
          </svg>
          <p class="text-lg font-medium">{{ emptyStateTemplate.title }}</p>
          <p class="text-sm text-gray-500 mt-1">{{ emptyStateTemplate.subtitle }}</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th class="py-3">Typ</th>
                <th class="py-3">Datum</th>
                <th class="py-3">km-Stand</th>
                <th class="py-3">Betrag</th>
                <th class="py-3">Menge</th>
                <th class="py-3">Preis/Einheit</th>
                <th class="py-3">Ort</th>
                <th class="py-3">Aktionen</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="entry in filteredEntries" :key="entry.id">
                <td class="py-3">
                  <div class="badge py-2 px-3" :class="getBadgeClass(entry.type)">
                    {{ getTypeLabel(entry.type) }}
                  </div>
                </td>
                <td class="py-3">{{ formatDate(entry.date) }}</td>
                <td class="py-3">{{ entry.mileage ? entry.mileage.toLocaleString() + ' km' : '-' }}</td>
                <td class="py-3">{{ formatCurrency(entry.amount) }}</td>
                <td class="py-3">{{ entry.quantity ? entry.quantity + (entry.type === 'refueling' ? ' l' : '') : '-' }}</td>
                <td class="py-3">{{ entry.price_per_unit ? formatCurrency(entry.price_per_unit) : '-' }}</td>
                <td class="py-3">{{ entry.location || '-' }}</td>
                <td class="py-3 flex gap-2">
                  <button @click="openEditModal(entry)" class="btn btn-xs btn-ghost text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                  </button>
                  <button @click="openDeleteModal(entry.id)" class="btn btn-xs btn-ghost text-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Modal for Entry Form -->
    <dialog ref="entryModal" class="modal">
      <div class="modal-box bg-white max-w-lg p-6">
        <h3 class="text-2xl font-semibold mb-6">{{ getTypeLabel(form.type) }} {{ isEditing ? 'bearbeiten' : 'hinzufügen' }}</h3>

        <form @submit.prevent="saveEntry" class="space-y-6">
          <div v-if="isEditing" class="mb-6">
            <div class="text-base font-medium text-gray-700 mb-2">Typ</div>
            <select v-model="form.type" class="w-full h-12 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              <option value="refueling">Tanken</option>
              <option value="car_wash">Autowäsche</option>
              <option value="parking_fee">Parkgebühren</option>
            </select>
          </div>

          <div class="mb-6">
            <div class="text-base font-medium text-gray-700 mb-2">Datum</div>
            <input
              type="date"
              v-model="form.date"
              class="w-full h-12 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          <div class="mb-6">
            <div class="text-base font-medium text-gray-700 mb-2">Kilometerstand</div>
            <input
              type="number"
              v-model="form.mileage"
              class="w-full h-12 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Kilometerstand (optional)"
            />
          </div>

          <div class="mb-6">
            <div class="text-base font-medium text-gray-700 mb-2">Kosten (€)</div>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <span class="text-gray-500">€</span>
              </div>
              <input
                type="number"
                v-model="form.amount"
                class="w-full h-12 pl-8 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="0.00"
                step="0.01"
                required
              />
            </div>
          </div>

          <div v-if="form.type === 'refueling'" class="mb-6">
            <div class="text-base font-medium text-gray-700 mb-2">Menge (Liter)</div>
            <input
              type="number"
              v-model="form.quantity"
              class="w-full h-12 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="0.00"
              step="0.01"
            />
          </div>

          <div class="mb-6">
            <div class="text-base font-medium text-gray-700 mb-2">Ort</div>
            <input
              type="text"
              v-model="form.location"
              class="w-full h-12 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Ort"
            />
          </div>

          <div class="mb-6">
            <div class="text-base font-medium text-gray-700 mb-2">Beschreibung (optional)</div>
            <textarea
              v-model="form.notes"
              class="w-full h-24 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Beschreibung"
            ></textarea>
          </div>

          <div class="flex justify-end space-x-3 mt-8">
            <button type="button" class="px-6 py-2 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50" @click="closeEntryModal">Abbrechen</button>
            <button type="submit" class="px-6 py-2 rounded-lg bg-indigo-600 text-white font-medium hover:bg-indigo-700">Speichern</button>
          </div>
        </form>
      </div>
      <form method="dialog" class="modal-backdrop">
        <button>close</button>
      </form>
    </dialog>

    <!-- Confirmation Dialog -->
    <dialog ref="deleteConfirmModal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg">Eintrag löschen</h3>
        <p class="py-4">Möchten Sie diesen Eintrag wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.</p>
        <div class="modal-action">
          <button class="btn" @click="closeDeleteModal">Abbrechen</button>
          <button class="btn btn-error" @click="deleteEntry">Löschen</button>
        </div>
      </div>
      <form method="dialog" class="modal-backdrop">
        <button>close</button>
      </form>
    </dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import axios from 'axios';

// Configure axios for CSRF protection and credentials
axios.defaults.withCredentials = true;
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

const props = defineProps({
  vehicleId: {
    type: Number,
    required: true
  },
  initialEntries: {
    type: Array,
    default: () => []
  },
  emptyStateTemplate: {
    type: Object,
    default: () => ({
      icon: 'M20 12H4M4 12V20a2 2 0 002 2h12a2 2 0 002-2v-8M4 12V4a2 2 0 012-2h12a2 2 0 012 2v8',
      title: 'Keine Einträge gefunden.',
      subtitle: 'Fügen Sie laufende Kosten wie Tanken, Autowäsche oder Parkgebühren hinzu.'
    })
  }
});

const loading = ref(true);
const entries = ref([]);
const activeTab = ref('all');
const entryModal = ref(null);
const deleteConfirmModal = ref(null);
const entryIdToDelete = ref(null);
const isEditing = ref(false);
const isOfflineMode = ref(false);

const form = ref({
  type: 'refueling',
  date: new Date().toISOString().split('T')[0],
  mileage: null,
  amount: null,
  quantity: null,
  price_per_unit: null,
  location: '',
  notes: ''
});

const filteredEntries = computed(() => {
  if (activeTab.value === 'all') {
    return entries.value;
  }
  return entries.value.filter(entry => entry.type === activeTab.value);
});

watch(() => props.initialEntries, (newEntries) => {
  if (newEntries && newEntries.length > 0) {
    entries.value = newEntries;
    loading.value = false;
  }
}, { immediate: true });

onMounted(async () => {
  if (!props.initialEntries || props.initialEntries.length === 0) {
    await fetchEntries();
  }
});

const fetchEntries = async () => {
  loading.value = true;
  try {
    // Try the primary endpoint with credentials
    const response = await axios.get(`/api/vehicles/${props.vehicleId}/quick-access-entries`, {
      withCredentials: true,
    });

    if (response.data && response.data.success) {
      entries.value = response.data.data;
      // Store in local storage for offline access
      localStorage.setItem(`quickAccessEntries_${props.vehicleId}`, JSON.stringify(entries.value));
      isOfflineMode.value = false;
    }
  } catch (error) {
    console.error('Error fetching quick access entries:', error);

    // Handle 401/403 errors specifically (authentication issues)
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      console.log('Authentication error - please log in again');
    }

    // Fallback to local storage if available
    const storedEntries = localStorage.getItem(`quickAccessEntries_${props.vehicleId}`);
    if (storedEntries) {
      entries.value = JSON.parse(storedEntries);
      isOfflineMode.value = true;
    }

    // If no stored entries, create empty array
    if (!entries.value || entries.value.length === 0) {
      entries.value = [];
    }
  } finally {
    loading.value = false;
  }
};

const getTypeLabel = (type) => {
  const labels = {
    'refueling': 'Tanken',
    'car_wash': 'Autowäsche',
    'parking_fee': 'Parkgebühren'
  };
  return labels[type] || type;
};

const formatDate = (dateString) => {
  if (!dateString) return "-";

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

const formatCurrency = (value) => {
  if (value === null || value === undefined) return "-";

  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(value);
};

const openEntryModal = (type) => {
  resetForm();
  form.value.type = type;
  isEditing.value = false;
  entryModal.value.showModal();
};

const closeEntryModal = () => {
  entryModal.value.close();
};

const resetForm = () => {
  form.value = {
    type: 'refueling',
    date: new Date().toISOString().split('T')[0],
    mileage: null,
    amount: null,
    quantity: null,
    price_per_unit: null,
    location: '',
    notes: ''
  };
};

const saveEntry = async () => {
  try {
    // Calculate price_per_unit for refueling entries
    if (form.value.type === 'refueling' && form.value.amount && form.value.quantity && form.value.quantity > 0) {
      form.value.price_per_unit = parseFloat((form.value.amount / form.value.quantity).toFixed(2));
    }

    if (isOfflineMode.value) {
      // Local storage mode - generate temporary ID
      if (isEditing.value) {
        // Update existing entry
        const index = entries.value.findIndex(e => e.id === form.value.id);
        if (index !== -1) {
          entries.value[index] = { ...form.value };
        }
      } else {
        // Create new entry with temporary ID
        const newEntry = {
          ...form.value,
          id: Date.now(), // Use timestamp as temporary ID
          vehicle_id: props.vehicleId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        entries.value.unshift(newEntry);
      }

      // Store in local storage
      localStorage.setItem(`quickAccessEntries_${props.vehicleId}`, JSON.stringify(entries.value));
      closeEntryModal();
      return;
    }

    // Online mode - use API
    let response;
    const config = {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };

    if (isEditing.value) {
      response = await axios.put(
        `/api/vehicles/${props.vehicleId}/quick-access-entries/${form.value.id}`,
        form.value,
        config
      );
    } else {
      response = await axios.post(
        `/api/vehicles/${props.vehicleId}/quick-access-entries`,
        form.value,
        config
      );
    }

    if (response.data && response.data.success) {
      await fetchEntries();
      closeEntryModal();
    }
  } catch (error) {
    console.error('Error saving entry:', error);

    // Handle 401/403 errors specifically (authentication issues)
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      alert('Authentication error - please log in again');
      return;
    }

    // If API fails, fallback to offline mode
    isOfflineMode.value = true;
    saveEntry(); // Call again with offline mode
  }
};

const editEntry = (entry) => {
  isEditing.value = true;
  form.value = { ...entry };
  entryModal.value.showModal();
};

const confirmDelete = (id) => {
  entryIdToDelete.value = id;
  deleteConfirmModal.value.showModal();
};

const closeDeleteModal = () => {
  deleteConfirmModal.value.close();
  entryIdToDelete.value = null;
};

const deleteEntry = async () => {
  if (!entryIdToDelete.value) return;

  try {
    if (isOfflineMode.value) {
      // Remove from local array
      entries.value = entries.value.filter(e => e.id !== entryIdToDelete.value);
      // Update local storage
      localStorage.setItem(`quickAccessEntries_${props.vehicleId}`, JSON.stringify(entries.value));
      closeDeleteModal();
      return;
    }

    // Online mode - use API
    const response = await axios.delete(
      `/api/vehicles/${props.vehicleId}/quick-access-entries/${entryIdToDelete.value}`,
      {
        withCredentials: true,
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      }
    );

    if (response.data && response.data.success) {
      await fetchEntries();
      closeDeleteModal();
    }
  } catch (error) {
    console.error('Error deleting entry:', error);

    // Handle 401/403 errors specifically (authentication issues)
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      alert('Authentication error - please log in again or reload the page');
      return;
    }

    // If API fails, fallback to offline mode
    isOfflineMode.value = true;
    deleteEntry(); // Call again with offline mode
  }
};

// Helper method to get badge class based on entry type
const getBadgeClass = (type) => {
  switch (type) {
    case 'refueling':
      return 'badge-primary';
    case 'car_wash':
      return 'badge-info';
    case 'parking_fee':
      return 'badge-accent';
    default:
      return 'badge-neutral';
  }
};

// Update function names
const openEditModal = (entry) => {
  isEditing.value = true;
  
  // Create a copy of the entry to avoid modifying the original
  const entryCopy = { ...entry };
  
  // Format the date correctly for input type="date"
  if (entryCopy.date) {
    try {
      // First try to create a Date object from the input
      const dateObj = new Date(entryCopy.date);
      
      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        // If invalid, try parsing DD.MM.YYYY format
        const parts = entryCopy.date.split('.');
        if (parts.length === 3) {
          entryCopy.date = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
        }
      } else {
        // If valid Date object, format to YYYY-MM-DD
        entryCopy.date = dateObj.toISOString().split('T')[0];
      }
    } catch (e) {
      console.error('Error parsing date:', e);
      // Fallback to current date if parsing fails
      entryCopy.date = new Date().toISOString().split('T')[0];
    }
  }
  
  form.value = entryCopy;
  entryModal.value.showModal();
};

const openDeleteModal = (id) => {
  entryIdToDelete.value = id;
  deleteConfirmModal.value.showModal();
};
</script>
