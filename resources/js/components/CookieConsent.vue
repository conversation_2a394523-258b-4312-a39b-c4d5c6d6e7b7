<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { Link, usePage } from '@inertiajs/vue3';

interface CookieCategory {
  id: string;
  name: string;
  description: string;
  required: boolean;
  enabled: boolean;
}

interface CookieSettings {
  consent: boolean;
  categories: Record<string, boolean>;
  timestamp: number;
}

const page = usePage();

// Config: Cookie-Kategorien definieren
const cookieCategories = ref<CookieCategory[]>([
  {
    id: 'necessary',
    name: 'Notwendig',
    description: 'Diese Cookies sind für die Grundfunktionen der Website erforderlich und können nicht deaktiviert werden.',
    required: true,
    enabled: true,
  },
  {
    id: 'functional',
    name: 'Funktional',
    description: 'Diese Cookies ermöglichen erweiterte Funktionalitäten und Personalisierung, wie z.B. Videos und Live-Chats.',
    required: false,
    enabled: false,
  },
  {
    id: 'analytics',
    name: 'Analytisch',
    description: 'Diese Cookies helfen uns zu verstehen, wie Besucher mit unserer Website interagieren, um die Nutzererfahrung zu verbessern.',
    required: false,
    enabled: false,
  },
  {
    id: 'marketing',
    name: 'Marketing',
    description: 'Diese Cookies werden verwendet, um Besuchern auf Websites relevante Werbung anzuzeigen und Marketing-Kampagnen effektiver zu gestalten.',
    required: false,
    enabled: false,
  },
]);

// Zustände für die Anzeige der Banner
const showBanner = ref(false);
const showSettings = ref(false);
const settingsSaved = ref(false);
const hasConsent = ref(false);

// Lokale Kopie der Einstellungen für den Dialog
const selectedCategories = ref<Record<string, boolean>>({});

// Speichert die Cookie-Einstellungen im Local Storage
const saveCookieSettings = (acceptAll = false) => {
  const settings: CookieSettings = {
    consent: true,
    categories: {},
    timestamp: Date.now(),
  };

  // Wenn "alle akzeptieren" gewählt wurde, alle nicht erforderlichen Cookies aktivieren
  if (acceptAll) {
    cookieCategories.value.forEach(category => {
      settings.categories[category.id] = true;
      selectedCategories.value[category.id] = true;
    });
  } else {
    cookieCategories.value.forEach(category => {
      // Erforderliche Cookies sind immer aktiviert
      if (category.required) {
        settings.categories[category.id] = true;
        selectedCategories.value[category.id] = true;
      } else {
        settings.categories[category.id] = selectedCategories.value[category.id] || false;
      }
    });
  }

  // Einstellungen im Local Storage speichern
  localStorage.setItem('cookie_consent', JSON.stringify(settings));
  
  // Banner verstecken
  showBanner.value = false;
  showSettings.value = false;
  hasConsent.value = true;
  
  // Kurze Bestätigung anzeigen
  settingsSaved.value = true;
  setTimeout(() => {
    settingsSaved.value = false;
  }, 3000);

  // Ereignis auslösen, damit andere Komponenten reagieren können
  window.dispatchEvent(new CustomEvent('cookie-consent-updated', { detail: settings }));
};

// Nur notwendige Cookies akzeptieren
const acceptNecessary = () => {
  cookieCategories.value.forEach(category => {
    selectedCategories.value[category.id] = category.required;
  });
  saveCookieSettings();
};

// Alle Cookies akzeptieren
const acceptAll = () => {
  saveCookieSettings(true);
};

// Einstellungen anzeigen
const showCookieSettings = () => {
  showSettings.value = true;
};

// Cookie-Banner erneut anzeigen
const reopenCookieSettings = () => {
  showBanner.value = true;
  showSettings.value = false;
};

// Cookie-Einstellungen laden
const loadCookieSettings = () => {
  const storedSettings = localStorage.getItem('cookie_consent');
  
  if (storedSettings) {
    try {
      const settings: CookieSettings = JSON.parse(storedSettings);
      
      // Einstellungen auf die Kategorien anwenden
      cookieCategories.value.forEach(category => {
        // Stelle sicher, dass erforderliche Cookies immer aktiviert sind
        if (category.required) {
          category.enabled = true;
          selectedCategories.value[category.id] = true;
        } else {
          category.enabled = settings.categories[category.id] || false;
          selectedCategories.value[category.id] = settings.categories[category.id] || false;
        }
      });
      
      // Consent-Status setzen
      hasConsent.value = settings.consent;
      
      // Wenn Consent gegeben wurde, Banner nicht anzeigen
      if (settings.consent) {
        showBanner.value = false;
      } else {
        showBanner.value = true;
      }
    } catch (error) {
      console.error('Fehler beim Laden der Cookie-Einstellungen:', error);
      showBanner.value = true;
    }
  } else {
    // Keine Einstellungen gefunden, Banner anzeigen
    showBanner.value = true;
    
    // Standardwerte für Kategorien setzen
    cookieCategories.value.forEach(category => {
      selectedCategories.value[category.id] = category.required;
    });
  }
};

// Event-Listener für das Öffnen der Cookie-Einstellungen
const handleReopenCookieSettings = () => {
  reopenCookieSettings();
};

// Listener bei Mount hinzufügen und bei Unmount entfernen
onMounted(() => {
  loadCookieSettings();
  window.addEventListener('reopen-cookie-settings', handleReopenCookieSettings);
});

onUnmounted(() => {
  window.removeEventListener('reopen-cookie-settings', handleReopenCookieSettings);
});

// Änderungen an Kategorien überwachen
watch(
  selectedCategories,
  (newValues) => {
    cookieCategories.value.forEach(category => {
      if (!category.required) {
        category.enabled = newValues[category.id] || false;
      }
    });
  },
  { deep: true }
);

// Öffentliche API für andere Komponenten
const isCategoryEnabled = (categoryId: string): boolean => {
  const category = cookieCategories.value.find(c => c.id === categoryId);
  if (!category) return false;
  return category.enabled;
};

// Expose API für template refs
defineExpose({
  isCategoryEnabled,
  reopenCookieSettings
});
</script>

<template>
  <!-- Cookie-Banner -->
  <div v-if="showBanner" 
       class="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white dark:bg-slate-800 shadow-lg border-t border-slate-200 dark:border-slate-700">
    <div class="container mx-auto max-w-7xl">
      <div v-if="!showSettings" class="flex flex-col md:flex-row items-start md:items-center gap-4">
        <div class="flex-1">
          <h3 class="text-lg font-bold text-slate-900 dark:text-white mb-2">Datenschutzeinstellungen</h3>
          <p class="text-slate-600 dark:text-slate-300 text-sm">
            Wir verwenden Cookies, um Ihnen die bestmögliche Erfahrung auf unserer Website zu bieten. 
            Weitere Informationen finden Sie in unserer <Link href="/legal/datenschutz" class="text-primary hover:underline">Datenschutzerklärung</Link> und <Link href="/legal/datenschutz-details" class="text-primary hover:underline">Cookie-Richtlinie</Link>.
          </p>
        </div>
        <div class="flex flex-col sm:flex-row gap-2 mt-2 md:mt-0">
          <button 
            @click="showCookieSettings" 
            class="px-4 py-2 text-sm font-medium rounded-lg bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-900 dark:text-white transition-colors">
            Einstellungen
          </button>
          <button 
            @click="acceptNecessary" 
            class="px-4 py-2 text-sm font-medium rounded-lg bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-900 dark:text-white transition-colors">
            Nur notwendige
          </button>
          <button 
            @click="acceptAll" 
            class="px-4 py-2 text-sm font-medium rounded-lg bg-primary hover:bg-primary/90 text-white transition-colors">
            Alle akzeptieren
          </button>
        </div>
      </div>

      <div v-if="showSettings" class="cookie-settings">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold text-slate-900 dark:text-white">Cookie-Einstellungen anpassen</h3>
          <button 
            @click="showSettings = false" 
            class="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200">
            <span class="sr-only">Schließen</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <div class="space-y-4 max-h-96 overflow-y-auto pr-2 mb-4">
          <div v-for="category in cookieCategories" :key="category.id" class="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
            <div class="flex items-start">
              <div class="flex items-center h-6 mt-0.5">
                <input 
                  :id="`cookie-${category.id}`" 
                  type="checkbox" 
                  v-model="selectedCategories[category.id]" 
                  :disabled="category.required"
                  class="h-4 w-4 rounded border-slate-300 text-primary focus:ring-primary disabled:opacity-60"
                >
              </div>
              <div class="ml-3">
                <label :for="`cookie-${category.id}`" class="text-sm font-medium text-slate-900 dark:text-white flex items-center">
                  {{ category.name }}
                  <span v-if="category.required" class="ml-2 text-xs bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 px-2 py-0.5 rounded">
                    Erforderlich
                  </span>
                </label>
                <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  {{ category.description }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-2">
          <button 
            @click="showSettings = false" 
            class="px-4 py-2 text-sm font-medium rounded-lg bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-900 dark:text-white transition-colors">
            Abbrechen
          </button>
          <button 
            @click="saveCookieSettings()" 
            class="px-4 py-2 text-sm font-medium rounded-lg bg-primary hover:bg-primary/90 text-white transition-colors">
            Speichern
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bestätigung beim Speichern der Einstellungen -->
  <div v-if="settingsSaved && !showBanner" 
       class="fixed bottom-4 right-4 z-50 p-4 bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-lg shadow-lg border border-green-200 dark:border-green-900 flex items-center">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
    </svg>
    <span>Ihre Cookie-Einstellungen wurden gespeichert</span>
  </div>

  <!-- Schwebende Schaltfläche zum Öffnen der Cookie-Einstellungen (sichtbar, wenn Banner nicht angezeigt wird) -->
  <div v-if="hasConsent && !showBanner && !settingsSaved" 
       class="fixed bottom-4 right-4 z-40">
    <button 
      @click="reopenCookieSettings" 
      class="p-3 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 rounded-full shadow-lg text-slate-700 dark:text-slate-300 border border-slate-200 dark:border-slate-600"
      title="Cookie-Einstellungen ändern">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    </button>
  </div>
</template> 