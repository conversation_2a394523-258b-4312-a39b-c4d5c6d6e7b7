<script setup lang="ts">
import { onMounted } from 'vue';
import { useCookieConsent } from '@/composables/useCookieConsent';

// Cookie-Consent-API importieren
const { isCategoryEnabled, runIfCategoryEnabled } = useCookieConsent();

// Funktion zum Laden des Analytics-Scripts
const loadAnalytics = () => {
  console.log('Analytics-Script wird geladen...');
  
  // Hier würde normalerweise der tatsächliche Analytics-Code geladen werden
  // z.B. Google Analytics, Matomo, etc.
  const script = document.createElement('script');
  script.innerHTML = `
    console.log('Analytics-Tracking ist aktiv');
    // Hier würde der eigentliche Analytics-Code stehen
  `;
  document.head.appendChild(script);
};

// Bei Komponenten-Mounting prüfen, ob Analytics geladen werden darf
onMounted(() => {
  // Methode 1: Direkte Prüfung mit bedingter Logik
  if (isCategoryEnabled('analytics')) {
    loadAnalytics();
  }
  
  // ODER Methode 2: Helper-Funktion verwenden
  runIfCategoryEnabled('analytics', () => {
    // Alternative Implementierung oder weitere Analytics-Dienste
    console.log('Zusätzliche Analytics-Dienste werden geladen...');
  });
});
</script>

<template>
  <div class="p-4 bg-white dark:bg-slate-800 rounded-lg shadow border border-slate-200 dark:border-slate-700">
    <h3 class="text-lg font-bold text-slate-900 dark:text-white mb-2">Analytics-Beispiel</h3>
    
    <div v-if="isCategoryEnabled('analytics')" class="text-green-600 dark:text-green-400 flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
      Analytics-Tracking ist aktiv
    </div>
    
    <div v-else class="text-slate-600 dark:text-slate-400 flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
      Analytics-Tracking ist deaktiviert
    </div>
    
    <p class="mt-4 text-sm text-slate-600 dark:text-slate-400">
      Diese Komponente zeigt, wie der Cookie-Consent-Status verwendet werden kann, 
      um Analytics-Skripte nur dann zu laden, wenn der Benutzer die Einwilligung gegeben hat.
    </p>
  </div>
</template> 