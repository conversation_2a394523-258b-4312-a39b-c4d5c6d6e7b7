<template>
  <div v-if="isActive" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" :class="$attrs.class">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { inject, computed } from 'vue';

defineOptions({
  name: 'TabsContent',
  inheritAttrs: false,
});

const props = defineProps<{
  value: string;
}>();

const activeTab = inject('activeTab', '') as any;

const isActive = computed(() => activeTab.value === props.value);
</script> 