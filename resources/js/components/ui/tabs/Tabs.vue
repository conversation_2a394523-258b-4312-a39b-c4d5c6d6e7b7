<template>
  <div :class="$attrs.class">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { provide, ref } from 'vue';

defineOptions({
  name: 'Tabs',
  inheritAttrs: false,
});

const props = defineProps<{
  defaultValue?: string;
}>();

const activeTab = ref(props.defaultValue || '');

provide('activeTab', activeTab);
provide('setActiveTab', (value: string) => {
  activeTab.value = value;
});
</script> 