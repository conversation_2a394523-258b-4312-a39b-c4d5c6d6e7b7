<template>
  <div class="p-3 w-full" :class="$attrs.class">
    <div class="text-center">
      <div class="flex justify-between items-center mb-2">
        <button @click="prevMonth" class="p-1">&lt;</button>
        <div>{{ currentMonthName }} {{ currentYear }}</div>
        <button @click="nextMonth" class="p-1">&gt;</button>
      </div>
      <div class="grid grid-cols-7 gap-1">
        <div v-for="day in weekDays" :key="day" class="text-sm font-medium">
          {{ day }}
        </div>
        <div 
          v-for="(day, index) in calendarDays" 
          :key="index"
          class="p-2 text-sm rounded-md cursor-pointer"
          :class="{ 
            'bg-primary text-primary-foreground': isSelected(day), 
            'text-muted-foreground': day.month !== currentMonth 
          }"
          @click="selectDate(day)"
        >
          {{ day.date }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

defineOptions({
  name: 'Calendar',
  inheritAttrs: false,
});

const props = defineProps<{
  modelValue?: Date;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', date: Date): void;
}>();

const currentDate = ref(props.modelValue || new Date());
const currentMonth = ref(currentDate.value.getMonth());
const currentYear = ref(currentDate.value.getFullYear());

const weekDays = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];

const currentMonthName = computed(() => {
  const options = { month: 'long' };
  return new Date(currentYear.value, currentMonth.value).toLocaleDateString('en-US', options);
});

const daysInMonth = (year: number, month: number) => {
  return new Date(year, month + 1, 0).getDate();
};

const getFirstDayOfMonth = (year: number, month: number) => {
  let day = new Date(year, month, 1).getDay();
  return day === 0 ? 6 : day - 1; // Adjust from Sunday-based to Monday-based
};

const calendarDays = computed(() => {
  const days = [];
  const firstDay = getFirstDayOfMonth(currentYear.value, currentMonth.value);
  const daysCount = daysInMonth(currentYear.value, currentMonth.value);
  
  // Previous month days
  const prevMonth = currentMonth.value === 0 ? 11 : currentMonth.value - 1;
  const prevYear = currentMonth.value === 0 ? currentYear.value - 1 : currentYear.value;
  const prevMonthDays = daysInMonth(prevYear, prevMonth);
  
  for (let i = firstDay - 1; i >= 0; i--) {
    days.push({ 
      date: prevMonthDays - i, 
      month: prevMonth,
      year: prevYear
    });
  }
  
  // Current month days
  for (let i = 1; i <= daysCount; i++) {
    days.push({ 
      date: i, 
      month: currentMonth.value,
      year: currentYear.value
    });
  }
  
  // Next month days
  const nextMonth = currentMonth.value === 11 ? 0 : currentMonth.value + 1;
  const nextYear = currentMonth.value === 11 ? currentYear.value + 1 : currentYear.value;
  const remainingDays = 42 - days.length;
  
  for (let i = 1; i <= remainingDays; i++) {
    days.push({ 
      date: i, 
      month: nextMonth,
      year: nextYear
    });
  }
  
  return days;
});

const isSelected = (day: { date: number, month: number, year: number }) => {
  if (!props.modelValue) return false;
  
  const selectedDate = props.modelValue;
  return selectedDate.getDate() === day.date && 
         selectedDate.getMonth() === day.month && 
         selectedDate.getFullYear() === day.year;
};

const selectDate = (day: { date: number, month: number, year: number }) => {
  const date = new Date(day.year, day.month, day.date);
  emit('update:modelValue', date);
};

const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }
};

const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentMonth.value = 0;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }
};

onMounted(() => {
  if (props.modelValue) {
    currentMonth.value = props.modelValue.getMonth();
    currentYear.value = props.modelValue.getFullYear();
  }
});
</script> 