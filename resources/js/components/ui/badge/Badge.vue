<template>
  <div 
    class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2" 
    :class="variantClasses"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

defineOptions({
  name: 'Badge',
});

const props = defineProps<{
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'blue' | 'green' | 'gray' | 'sky' | 'purple';
}>();

const variantClasses = computed(() => {
  switch (props.variant) {
    case 'secondary':
      return 'bg-secondary hover:bg-secondary/80 border-transparent text-secondary-foreground';
    case 'destructive':
      return 'bg-destructive hover:bg-destructive/80 border-transparent text-destructive-foreground';
    case 'outline':
      return 'text-foreground';
    case 'success':
      return 'bg-green-100 text-green-800 hover:bg-green-200 border-transparent';
    case 'blue':
      return 'bg-blue-100 text-blue-800 hover:bg-blue-200 border-transparent';
    case 'green':
      return 'bg-green-100 text-green-800 hover:bg-green-200 border-transparent';
    case 'gray':
      return 'bg-gray-100 text-gray-800 hover:bg-gray-200 border-transparent';
    case 'sky':
      return 'bg-indigo-600 text-white hover:bg-indigo-700 border-transparent';
    case 'purple':
      return 'bg-purple-100 text-purple-800 hover:bg-purple-200 border-transparent';
    default:
      return 'bg-primary hover:bg-primary/80 border-transparent text-primary-foreground';
  }
});
</script> 