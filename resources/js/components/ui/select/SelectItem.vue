<template>
  <div
    @click="select"
    class="relative cursor-default select-none py-2 pl-10 pr-4 text-gray-900 hover:bg-gray-100"
    :class="{ 'font-medium': isSelected }"
  >
    <span class="block truncate">
      <slot />
    </span>
    <span v-if="isSelected" class="absolute inset-y-0 left-0 flex items-center pl-3 text-indigo-600">
      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
    </span>
  </div>
</template>

<script setup lang="ts">
import { inject, computed } from 'vue';

defineOptions({
  name: 'SelectItem',
});

const props = defineProps<{
  value: any;
}>();

const selectedValue = inject('selectedValue', null);
const updateValue = inject('updateValue', (value: any) => {});
const setIsOpen = inject('setIsOpen', (value: boolean) => {});

const isSelected = computed(() => selectedValue.value === props.value);

const select = () => {
  updateValue(props.value);
  setIsOpen(false);
};
</script>
EOL 
