<template>
  <div class="relative" :class="$attrs.class">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { provide, ref, watch } from 'vue';

defineOptions({
  name: 'Select',
  inheritAttrs: false,
});

const props = defineProps<{
  modelValue?: any;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void;
}>();

const selectedValue = ref(props.modelValue);

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue;
});

// Provide to child components
provide('selectedValue', selectedValue);
provide('updateValue', (value: any) => {
  selectedValue.value = value;
  emit('update:modelValue', value);
});

// Is the dropdown open
const isOpen = ref(false);
provide('isOpen', isOpen);
provide('setIsOpen', (value: boolean) => {
  isOpen.value = value;
});
</script> 