<script setup lang="ts">
import { isVNode } from 'vue'
import { Toast, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from '.'
import { useToast } from './use-toast'
import { CheckCircle, AlertCircle, XCircle } from 'lucide-vue-next'

const { toasts } = useToast()

// Helper function to determine which icon to show based on variant
const getToastIcon = (variant) => {
  switch (variant) {
    case 'success':
      return CheckCircle
    case 'destructive':
      return AlertCircle
    default:
      return null
  }
}
</script>

<template>
  <ToastProvider>
    <Toast v-for="toast in toasts" :key="toast.id" v-bind="toast">
      <div class="flex items-center gap-3">
        <component :is="getToastIcon(toast.variant)" v-if="getToastIcon(toast.variant)" class="h-5 w-5 flex-shrink-0" />
        <div class="grid gap-1">
          <ToastTitle v-if="toast.title" class="font-medium">
            {{ toast.title }}
          </ToastTitle>
          <template v-if="toast.description">
            <ToastDescription v-if="isVNode(toast.description)">
              <component :is="toast.description" />
            </ToastDescription>
            <ToastDescription v-else class="text-sm opacity-90">
              {{ toast.description }}
            </ToastDescription>
          </template>
        </div>
      </div>
      <ToastClose />
      <component :is="toast.action" />
    </Toast>
    <ToastViewport />
  </ToastProvider>
</template>
