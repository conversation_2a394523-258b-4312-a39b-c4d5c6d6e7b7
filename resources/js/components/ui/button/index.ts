import { cva, type VariantProps } from 'class-variance-authority';

export { default as But<PERSON> } from './Button.vue';

export const buttonVariants = cva(
    'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
    {
        variants: {
            variant: {
                default: 'bg-primary text-white shadow hover:bg-primary/90 dark:bg-primary dark:text-primary-foreground dark:hover:bg-primary/80',
                destructive: 'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 dark:bg-destructive dark:text-destructive-foreground dark:hover:bg-destructive/80',
                outline: 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground dark:border-base-300 dark:bg-base-100 dark:text-base-content dark:hover:bg-base-200',
                secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 dark:bg-base-200 dark:text-base-content dark:hover:bg-base-300',
                ghost: 'hover:bg-accent hover:text-accent-foreground dark:text-base-content dark:hover:bg-base-200',
                link: 'text-primary underline-offset-4 hover:underline dark:text-primary',
            },
            size: {
                default: 'h-9 px-4 py-2',
                sm: 'h-8 rounded-md px-3 text-xs',
                lg: 'h-10 rounded-md px-8',
                icon: 'h-9 w-9',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    },
);

export type ButtonVariants = VariantProps<typeof buttonVariants>;
