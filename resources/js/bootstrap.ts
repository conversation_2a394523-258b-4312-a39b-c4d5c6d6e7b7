import axios from 'axios';

// Konfiguriere Axios, um automatisch CSRF-Tokens einzufügen
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.withCredentials = true;

// Funktion zum Abrufen des CSRF-Tokens aus dem Meta-Tag
const getMetaToken = (): string | null => {
    const token = document.head?.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : null;
};

// Funktion zum Abrufen des XSRF-Tokens aus Cookies
const getCookieToken = (): string => {
    return decodeURIComponent(
        document.cookie.split('; ')
            .find(row => row.startsWith('XSRF-TOKEN='))
            ?.split('=')[1] || ''
    );
};

// Initial CSRF-Token setzen
const metaToken = getMetaToken();
if (metaToken) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = metaToken;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// XSRF-TOKEN Cookie für SPA Anwendungen hinzufügen
axios.defaults.headers.common['X-XSRF-TOKEN'] = getCookieToken();

// Interceptor für 419 Fehler (CSRF Token Mismatch)
axios.interceptors.response.use(
    response => response,
    error => {
        // Bei 419 Fehlern (CSRF Token Mismatch) die Seite neu laden
        if (error.response && error.response.status === 419) {
            console.warn('CSRF token mismatch detected. Reloading page to refresh token.');
            window.location.reload();
            return new Promise(() => {});
        }
        return Promise.reject(error);
    }
);

// CSRF-Token nach dem Document Load aktualisieren
document.addEventListener('DOMContentLoaded', () => {
    // Tokens aktualisieren
    const newMetaToken = getMetaToken();
    const newCookieToken = getCookieToken();
    
    if (newMetaToken) {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = newMetaToken;
    }
    
    if (newCookieToken) {
        axios.defaults.headers.common['X-XSRF-TOKEN'] = newCookieToken;
    }
});

export default axios;
