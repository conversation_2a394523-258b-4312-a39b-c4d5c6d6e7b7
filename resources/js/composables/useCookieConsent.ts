import { ref } from 'vue';

interface CookieSettings {
  consent: boolean;
  categories: Record<string, boolean>;
  timestamp: number;
}

// Singleton-Instanz für den gesamten App-Zustand
const hasConsent = ref<boolean>(false);
const categories = ref<Record<string, boolean>>({});

// Laden der gespeicherten Cookie-Einstellungen
const loadSettings = (): void => {
  const storedSettings = localStorage.getItem('cookie_consent');
  
  if (storedSettings) {
    try {
      const settings: CookieSettings = JSON.parse(storedSettings);
      hasConsent.value = settings.consent;
      categories.value = settings.categories || {};
    } catch (error) {
      console.error('Fehler beim Laden der Cookie-Einstellungen:', error);
    }
  }
};

// Listener für Consent-Aktualisierungen
window.addEventListener('cookie-consent-updated', ((event: CustomEvent<CookieSettings>) => {
  hasConsent.value = event.detail.consent;
  categories.value = event.detail.categories || {};
}) as EventListener);

// Laden der Einstellungen beim ersten Import
loadSettings();

export function useCookieConsent() {
  /**
   * Prüft, ob für eine bestimmte Cookie-Kategorie die Einwilligung vorliegt
   * @param categoryId ID der Cookie-Kategorie (z.B. 'analytics', 'marketing')
   * @returns true, wenn die Einwilligung vorliegt, sonst false
   */
  const isCategoryEnabled = (categoryId: string): boolean => {
    // 'necessary' ist immer true
    if (categoryId === 'necessary') return true;
    
    // Keine Einwilligung = keine nicht-notwendigen Cookies
    if (!hasConsent.value) return false;
    
    // Kategorie explizit aktiviert?
    return !!categories.value[categoryId];
  };

  /**
   * Öffnet das Cookie-Banner erneut (ruft Event auf, das von der Komponente abgefangen wird)
   */
  const reopenCookieSettings = (): void => {
    window.dispatchEvent(new CustomEvent('reopen-cookie-settings'));
  };

  /**
   * Hilfsfunktion für bedingte Script-Ausführung basierend auf Cookie-Einwilligung
   * @param categoryId ID der Cookie-Kategorie (z.B. 'analytics', 'marketing')
   * @param callback Funktion, die ausgeführt werden soll, wenn die Einwilligung vorliegt
   */
  const runIfCategoryEnabled = (categoryId: string, callback: () => void): void => {
    if (isCategoryEnabled(categoryId)) {
      callback();
    }
  };

  return {
    hasConsent,
    categories,
    isCategoryEnabled,
    reopenCookieSettings,
    runIfCategoryEnabled
  };
} 