import { ref, watch, onMounted } from 'vue';
import { useAppearance } from './useAppearance';

// Define available themes
const themes = ['light', 'dark'] as const;
type Theme = typeof themes[number];

// Default theme
const defaultTheme: Theme = 'light';

// Create a composable for theme management
export function useTheme() {
    const { appearance } = useAppearance();
    const theme = ref<Theme>(defaultTheme);

    // Function to apply theme to document
    const applyTheme = (newTheme: Theme) => {
        document.documentElement.setAttribute('data-theme', newTheme);

        // Toggle dark class for DaisyUI
        if (newTheme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }

        // Store theme preference
        localStorage.setItem('theme', newTheme);
    };

    // Function to set theme
    const setTheme = (newTheme: Theme) => {
        theme.value = newTheme;
        applyTheme(newTheme);
    };

    // Initialize theme based on appearance or stored preference
    onMounted(() => {
        // Get stored theme
        const storedTheme = localStorage.getItem('theme') as Theme | null;

        // If system preference is set
        if (appearance.value === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            setTheme(systemTheme);
        }
        // If a theme is explicitly set
        else if (appearance.value === 'dark' || appearance.value === 'light') {
            setTheme(appearance.value);
        }
        // If a theme was previously stored
        else if (storedTheme && themes.includes(storedTheme)) {
            setTheme(storedTheme);
        }
        // Default to light theme
        else {
            setTheme(defaultTheme);
        }

        // Listen for system preference changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (appearance.value === 'system') {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });
    });

    // Watch for appearance changes
    watch(appearance, (newAppearance) => {
        if (newAppearance === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            setTheme(systemTheme);
        } else if (newAppearance === 'dark' || newAppearance === 'light') {
            setTheme(newAppearance);
        }
    });

    return {
        theme,
        setTheme,
    };
}
