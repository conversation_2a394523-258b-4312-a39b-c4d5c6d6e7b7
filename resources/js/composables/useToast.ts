import { ref } from 'vue';

interface Toast {
  id: number;
  message: string;
  type: 'info' | 'success' | 'error' | 'warning';
  duration: number;
}

interface ToastOptions {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

export function useToast() {
  const toasts = ref<Toast[]>([]);
  let nextId = 0;

  /**
   * Show a toast notification
   * @param message The message to display
   * @param type The type of notification (info, success, error, warning)
   * @param duration How long to show the toast in milliseconds
   */
  const showToast = (options: ToastOptions) => {
    // This is a stub function that could be implemented with a toast notification library
    // For now we'll just log to console as a placeholder
    console.log(`Toast: ${options.type || 'info'} - ${options.message}`);
    
    // You could replace this with an actual toast implementation like:
    // toast.add({
    //   severity: options.type || 'info',
    //   summary: options.type?.charAt(0).toUpperCase() + options.type?.slice(1) || 'Info',
    //   detail: options.message,
    //   life: options.duration || 3000
    // });
  };

  /**
   * Remove a toast by ID
   */
  const removeToast = (id: number) => {
    const index = toasts.value.findIndex(toast => toast.id === id);
    if (index !== -1) {
      toasts.value.splice(index, 1);
    }
  };

  return {
    toasts,
    showToast,
    removeToast
  };
}
