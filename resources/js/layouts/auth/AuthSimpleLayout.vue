<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { Link } from '@inertiajs/vue3';

defineProps<{
    title?: string;
    description?: string;
}>();
</script>

<template>
    <div class="flex min-h-svh bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950 overflow-hidden relative">
        <!-- Background pattern -->
        <div class="absolute inset-0 bg-grid-pattern opacity-[0.03] dark:opacity-[0.04]"></div>
        
        <!-- Left panel (hidden on mobile) -->
        <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden bg-primary">
            <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(255,255,255,0.2)_0%,rgba(0,0,0,0.2)_100%)]"></div>
            
            <!-- Glass panels decorations -->
            <div class="absolute top-[5%] left-[10%] w-64 h-64 bg-white/10 backdrop-blur-xl rounded-3xl rotate-12 border border-white/20"></div>
            <div class="absolute bottom-[15%] right-[5%] w-80 h-80 bg-white/10 backdrop-blur-xl rounded-3xl -rotate-12 border border-white/20"></div>
            
            <!-- Content -->
            <div class="absolute inset-0 flex flex-col justify-center items-center p-12 text-white">
                <div class="max-w-md text-center">
                    <h1 class="text-3xl md:text-4xl font-bold mb-6">Digitale Lebensakte für Ihr Fahrzeug</h1>
                    <p class="text-lg text-white/80 mb-8">
                        Dokumentieren Sie lückenlos die gesamte Historie Ihres Fahrzeugs - komplett kostenlos verfügbar.
                    </p>
                    
                    <!-- Feature points -->
                    <div class="space-y-4 text-left">
                        <div class="flex items-start gap-3">
                            <div class="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <span class="text-white/90">Wartungshistorie und Service-Planung</span>
                        </div>
                        <div class="flex items-start gap-3">
                            <div class="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <span class="text-white/90">PDF-Berichte mit Kostenübersicht</span>
                        </div>
                        <div class="flex items-start gap-3">
                            <div class="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <span class="text-white/90">Integrierte Ersatzteilsuche</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right panel (form) -->
        <div class="w-full lg:w-1/2 flex flex-col justify-center items-center">
            <div class="w-full max-w-md px-6 py-12 md:px-12">
                <div class="mb-8">
                    <Link :href="route('home')" class="flex items-center justify-center lg:justify-start gap-3 mb-8">
                        <div class="flex h-10 w-10 items-center justify-center">
                            <AppLogoIcon class="size-10 fill-current text-primary dark:text-white" />
                        </div>
                        <span class="text-xl font-semibold text-gray-900 dark:text-white">Fahrzeugakte<span class="text-primary">.app</span></span>
                    </Link>
                    
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ title }}</h1>
                    <p class="text-gray-600 dark:text-gray-400">{{ description }}</p>
                </div>
                
                <!-- Form content -->
                <div class="space-y-6">
                    <slot />
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

@media (prefers-color-scheme: dark) {
  .bg-grid-pattern {
    background-image: linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                      linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
  }
}
</style>
