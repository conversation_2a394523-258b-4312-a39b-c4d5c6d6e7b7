<script setup lang="ts">
import { Link, usePage } from '@inertiajs/vue3';
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { useCookieConsent } from '@/composables/useCookieConsent';
import { computed } from 'vue';

const { reopenCookieSettings } = useCookieConsent();
const page = usePage();
const auth = computed(() => page.props.auth);
</script>

<template>
  <div class="min-h-screen bg-slate-50 dark:bg-slate-900">
    <!-- Simple Header -->
    <header class="border-b border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-800 shadow-sm">
      <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link href="/" class="flex items-center gap-3">
          <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-10 w-auto" />
          <span class="text-xl font-bold text-slate-900 dark:text-white">Fahrzeugakte<span class="text-slate-400 dark:text-slate-400">.app</span></span>
        </Link>

        <div class="flex items-center gap-4">
          <Link
            v-if="auth.user"
            href="/dashboard"
            class="px-4 py-2 text-sm font-medium bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Dashboard
          </Link>
          <template v-else>
            <Link
              href="/login"
              class="px-4 py-2 text-sm font-medium text-slate-900 dark:text-slate-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Anmelden
            </Link>
            <Link
              href="/register"
              class="px-4 py-2 text-sm font-medium bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
            >
              Registrieren
            </Link>
          </template>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="py-8">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white py-8">
      <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="mb-4 md:mb-0 flex items-center gap-3">
            <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-8 w-auto" />
            <div>
              <span class="text-lg font-bold">Fahrzeugakte<span class="text-slate-400">.app</span></span>
              <p class="text-sm text-slate-400 mt-1">Die digitale Lebensakte für Ihr Fahrzeug</p>
            </div>
          </div>

          <div class="flex gap-6 text-slate-400">
            <Link href="/legal/impressum" class="text-sm hover:text-white transition-colors">Impressum</Link>
            <Link href="/legal/datenschutz" class="text-sm hover:text-white transition-colors">Datenschutz</Link>
            <Link href="/legal/agb" class="text-sm hover:text-white transition-colors">AGB</Link>
            <button @click="reopenCookieSettings" class="text-sm hover:text-white transition-colors">Cookie-Einstellungen</button>
          </div>
        </div>

        <div class="border-t border-slate-800 mt-6 pt-6 text-center text-sm text-slate-500">
          &copy; {{ new Date().getFullYear() }} Fahrzeugakte.app. Alle Rechte vorbehalten.
        </div>
      </div>
    </footer>
  </div>
</template>
