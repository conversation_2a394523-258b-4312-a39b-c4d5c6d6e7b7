const Ziggy = {"url":"https:\/\/fahrzeugakte.app","port":null,"defaults":{},"routes":{"cashier.payment":{"uri":"stripe\/payment\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"cashier.webhook":{"uri":"stripe\/webhook","methods":["POST"]},"livewire.update":{"uri":"livewire\/update","methods":["POST"]},"livewire.upload-file":{"uri":"livewire\/upload-file","methods":["POST"]},"livewire.preview-file":{"uri":"livewire\/preview-file\/{filename}","methods":["GET","HEAD"],"parameters":["filename"]},"home":{"uri":"\/","methods":["GET","HEAD"]},"legal.impressum":{"uri":"legal\/impressum","methods":["GET","HEAD"]},"legal.datenschutz":{"uri":"legal\/datenschutz","methods":["GET","HEAD"]},"legal.datenschutz-details":{"uri":"legal\/datenschutz-details","methods":["GET","HEAD"]},"legal.agb":{"uri":"legal\/agb","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"subscription.index":{"uri":"subscription","methods":["GET","HEAD"]},"subscription.checkout":{"uri":"subscription\/checkout","methods":["POST"]},"subscription.success":{"uri":"subscription\/success","methods":["GET","HEAD"]},"subscription.manage":{"uri":"subscription\/manage","methods":["GET","HEAD"]},"subscription.cancel":{"uri":"subscription\/cancel","methods":["POST"]},"subscription.resume":{"uri":"subscription\/resume","methods":["POST"]},"subscription.payment-method.update":{"uri":"subscription\/payment-method","methods":["POST"]},"subscription.invoice.download":{"uri":"subscription\/invoice\/{invoiceId}","methods":["GET","HEAD"],"parameters":["invoiceId"]},"vehicles.index":{"uri":"vehicles","methods":["GET","HEAD"]},"vehicles.create":{"uri":"vehicles\/create","methods":["GET","HEAD"]},"vehicles.store":{"uri":"vehicles","methods":["POST"]},"vehicles.show":{"uri":"vehicles\/{vehicle}","methods":["GET","HEAD"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.edit":{"uri":"vehicles\/{vehicle}\/edit","methods":["GET","HEAD"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.update":{"uri":"vehicles\/{vehicle}","methods":["PUT","PATCH"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.destroy":{"uri":"vehicles\/{vehicle}","methods":["DELETE"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.maintenance-logs.index":{"uri":"vehicles\/{vehicle}\/maintenance-logs","methods":["GET","HEAD"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.maintenance-logs.create":{"uri":"vehicles\/{vehicle}\/maintenance-logs\/create","methods":["GET","HEAD"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.maintenance-logs.store":{"uri":"vehicles\/{vehicle}\/maintenance-logs","methods":["POST"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"maintenance-logs.show":{"uri":"maintenance-logs\/{maintenance_log}","methods":["GET","HEAD"],"parameters":["maintenance_log"]},"maintenance-logs.edit":{"uri":"maintenance-logs\/{maintenance_log}\/edit","methods":["GET","HEAD"],"parameters":["maintenance_log"]},"maintenance-logs.update":{"uri":"maintenance-logs\/{maintenance_log}","methods":["PUT","PATCH"],"parameters":["maintenance_log"]},"maintenance-logs.destroy":{"uri":"maintenance-logs\/{maintenance_log}","methods":["DELETE"],"parameters":["maintenance_log"]},"vehicles.service-reminders.index":{"uri":"vehicles\/{vehicle}\/service-reminders","methods":["GET","HEAD"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.service-reminders.create":{"uri":"vehicles\/{vehicle}\/service-reminders\/create","methods":["GET","HEAD"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.service-reminders.store":{"uri":"vehicles\/{vehicle}\/service-reminders","methods":["POST"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"service-reminders.show":{"uri":"service-reminders\/{service_reminder}","methods":["GET","HEAD"],"parameters":["service_reminder"]},"service-reminders.edit":{"uri":"service-reminders\/{service_reminder}\/edit","methods":["GET","HEAD"],"parameters":["service_reminder"]},"service-reminders.update":{"uri":"service-reminders\/{service_reminder}","methods":["PUT","PATCH"],"parameters":["service_reminder"]},"service-reminders.destroy":{"uri":"service-reminders\/{service_reminder}","methods":["DELETE"],"parameters":["service_reminder"]},"parts.index":{"uri":"parts","methods":["GET","HEAD"]},"parts.create":{"uri":"parts\/create","methods":["GET","HEAD"]},"parts.store":{"uri":"parts","methods":["POST"]},"parts.show":{"uri":"parts\/{part}","methods":["GET","HEAD"],"parameters":["part"]},"parts.edit":{"uri":"parts\/{part}\/edit","methods":["GET","HEAD"],"parameters":["part"]},"parts.update":{"uri":"parts\/{part}","methods":["PUT","PATCH"],"parameters":["part"]},"parts.destroy":{"uri":"parts\/{part}","methods":["DELETE"],"parameters":["part"]},"parts.find-best-price":{"uri":"parts\/find-best-price","methods":["POST"]},"parts.find-by-hsn-tsn":{"uri":"parts\/find-by-hsn-tsn","methods":["POST"]},"vehicles.attachments.store":{"uri":"vehicles\/{vehicle}\/attachments","methods":["POST"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"vehicles.attachments.destroy":{"uri":"vehicles\/{vehicle}\/attachments\/{attachment}","methods":["DELETE"],"parameters":["vehicle","attachment"],"bindings":{"vehicle":"id","attachment":"id"}},"maintenance-logs.attachments.store":{"uri":"maintenance-logs\/{maintenanceLog}\/attachments","methods":["POST"],"parameters":["maintenanceLog"]},"calendar":{"uri":"calendar","methods":["GET","HEAD"]},"calendar.reminder.status":{"uri":"calendar\/reminder\/{reminder}\/status","methods":["PUT"],"parameters":["reminder"],"bindings":{"reminder":"id"}},"calendar.quick-reminder":{"uri":"calendar\/quick-reminder","methods":["POST"]},"vehicles.report":{"uri":"vehicles\/{vehicle}\/report","methods":["GET","HEAD"],"parameters":["vehicle"],"bindings":{"vehicle":"id"}},"api.vehicle-data.hsn-tsn":{"uri":"api-fallback\/vehicle-data","methods":["GET","HEAD"]},"api.vehicle-data.vin":{"uri":"api-fallback\/vehicle-data\/vin","methods":["GET","HEAD"]},"maintenance-types.index":{"uri":"maintenance-types","methods":["GET","HEAD"]},"maintenance-types.form-options":{"uri":"maintenance-types\/form-options","methods":["GET","HEAD"]},"maintenance-types.stats":{"uri":"maintenance-types\/stats\/{vehicleId?}","methods":["GET","HEAD"],"parameters":["vehicleId"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD","POST","PUT","PATCH","DELETE","OPTIONS"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
