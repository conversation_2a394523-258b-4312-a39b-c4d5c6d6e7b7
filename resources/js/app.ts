import '../css/app.css';
import './bootstrap';

import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';
import ToastContainer from './components/ui/ToastContainer.vue';
import CookieConsent from './components/CookieConsent.vue';
import axios from 'axios';

// Extend ImportMeta interface for Vite...
declare module 'vite/client' {
    interface ImportMetaEnv {
        readonly VITE_APP_NAME: string;
        [key: string]: string | boolean | undefined;
    }

    interface ImportMeta {
        readonly env: ImportMetaEnv;
        readonly glob: <T>(pattern: string) => Record<string, () => Promise<T>>;
    }
}

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

// This will set light / dark mode on page load...
initializeTheme();

// CSRF Token refresh on 419 errors
axios.interceptors.response.use(
    response => response,
    error => {
        if (error.response && error.response.status === 419) {
            // On CSRF token mismatch (419 error), refresh the page to get a new token
            window.location.reload();
        }
        return Promise.reject(error);
    }
);

// Überprüfe Theme während der App-Initialisierung
document.addEventListener('DOMContentLoaded', () => {
    const htmlElement = document.documentElement;
    const currentTheme = htmlElement.getAttribute('data-theme') || 'light';
    const isDark = currentTheme === 'dark' || htmlElement.classList.contains('dark');

    // Stelle sicher, dass sowohl data-theme als auch dark-Klasse übereinstimmen
    if (isDark) {
        htmlElement.classList.add('dark');
        htmlElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-mode');
    } else {
        htmlElement.classList.remove('dark');
        htmlElement.setAttribute('data-theme', 'light');
        document.body.classList.add('light-mode');
    }
});

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./pages/${name}.vue`, import.meta.glob<DefineComponent>('./pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) });

        app.use(plugin)
           .use(ZiggyVue)
           .component('ToastContainer', ToastContainer)
           .component('CookieConsent', CookieConsent)
           .mount(el);

        // Mount toast container outside of the app root
        const toastEl = document.createElement('div');
        document.body.appendChild(toastEl);
        createApp(ToastContainer).mount(toastEl);
        
        // Mount cookie consent outside of the app root
        const cookieConsentEl = document.createElement('div');
        document.body.appendChild(cookieConsentEl);
        createApp(CookieConsent).mount(cookieConsentEl);
    },
    progress: {
        color: '#4B5563',
    },
});
