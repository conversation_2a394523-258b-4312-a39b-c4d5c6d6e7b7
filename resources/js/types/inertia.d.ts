// Typ-Definitionen für Inertia.js v2.0-beta3
import { Component, DefineComponent } from 'vue';

declare module '@inertiajs/vue3' {
  // Define proper types for Form
  interface FormProps<TForm> {
    errors: Record<keyof TForm, string>;
    hasErrors: boolean;
    processing: boolean;
    progress: number | null;
    wasSuccessful: boolean;
    recentlySuccessful: boolean;
    // Stellt sicher, dass wir direkt auf Formularfelder zugreifen können
    [key: string]: any;
  }

  // Überschreibe die useForm-Definition
  export function useForm<TForm extends Record<string, any>>(data: TForm): TForm & FormProps<TForm>;
}
