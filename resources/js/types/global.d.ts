/*
 * Globale TypeScript-Deklarationen
 */

// Deklaration für MainLayout zur Verwendung in Vue-Komponenten
declare module '@/layouts/MainLayout.vue' {
    import { DefineComponent } from 'vue';
    const component: DefineComponent<{}, {}, any>;
    export default component;
}

// Inertia.js Route-Typen
declare module 'ziggy-js' {
    export type RouteParamsWithQueryOverload = {
        [key: string]: string | number | boolean | undefined;
    };

    export type RouteParam = string | number | boolean | null | undefined;

    export type RouteParams =
        | string
        | RouteParamsWithQueryOverload;

    export type Config = {
        url: string;
        port: number | null;
        defaults: { [key: string]: number | string | boolean };
        routes: { [key: string]: any };
    };

    export default function route(
        name: string,
        params?: RouteParams,
        absolute?: boolean,
        config?: Config
    ): string;
}

// Deklaration für Inertiajs-Vue Komponenten
declare module '@inertiajs/vue3' {
    import { Component } from 'vue';

    export interface InertiaPageProps {
        [key: string]: any;
    }

    export interface Page<SharedPropsType extends Record<string, any> = Record<string, any>> {
        props: InertiaPageProps & SharedPropsType;
        url: string;
        version: string | null;
        component: Component;
        [key: string]: any;
    }

    export function usePage<
        SharedPropsType extends Record<string, any> = Record<string, any>
    >(): {
        props: ComputedRef<InertiaPageProps & SharedPropsType>;
        url: ComputedRef<string>;
        component: ComputedRef<Component>;
        version: ComputedRef<string | null>;
    };

    export function Link(props: any): JSX.Element;

    export interface FormProps<TForm> {
        isDirty: boolean;
        errors: Record<keyof TForm, string>;
        hasErrors: boolean;
        processing: boolean;
        progress: {
            percentage: number | null;
            size: number | null;
            totalSize: number | null;
        } | null;
        wasSuccessful: boolean;
        recentlySuccessful: boolean;
        data(): TForm;
        transform(callback: (data: TForm) => object): this;
        setError(key: keyof TForm, value: string): this;
        clearErrors(...fields: Array<keyof TForm>): this;
        reset(...fields: Array<keyof TForm>): this;
        setData(key: keyof TForm, value: any): this;
        setData(values: Partial<TForm>): this;
        get(url: string, options?: RequestOptions): void;
        post(url: string, options?: RequestOptions): void;
        put(url: string, options?: RequestOptions): void;
        patch(url: string, options?: RequestOptions): void;
        delete(url: string, options?: RequestOptions): void;
        submit(method: string, url: string, options?: RequestOptions): void;
        cancel(): void;
    }

    export interface RequestOptions {
        preserveScroll?: boolean;
        preserveState?: boolean;
        only?: string[];
        headers?: { [key: string]: string };
        errorBag?: string;
        forceFormData?: boolean;
        onCancelToken?: (cancelToken: CancelTokenSource) => void;
        onBefore?: () => boolean | void;
        onStart?: () => void;
        onProgress?: (progress: ProgressEvent) => void;
        onSuccess?: (page: Page) => void | boolean;
        onError?: (errors: Record<string, string>) => void;
        onCancel?: () => void;
        onFinish?: () => void;
    }

    export function useForm<TForm extends Record<string, any>>(initialValues: TForm): FormProps<TForm>;

    export interface RouterInterface {
        visit(url: string, options?: RequestOptions): void;
        reload(options?: RequestOptions): void;
        back(): void;
        restore(): void;
        get(url: string, options?: RequestOptions): void;
        post(url: string, options?: RequestOptions): void;
        put(url: string, options?: RequestOptions): void;
        patch(url: string, options?: RequestOptions): void;
        delete(url: string, options?: RequestOptions): void;
    }

    export const router: RouterInterface;
}
