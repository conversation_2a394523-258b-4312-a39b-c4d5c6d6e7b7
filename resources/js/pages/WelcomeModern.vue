<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import { <PERSON> } from '@inertiajs/vue3';
import {
  Car, Calendar, History, FileText, Search, Wrench, Clock, ChevronRight,
  Activity, Shield, ChevronDown, Check, FileBarChart, Zap, Sparkles,
  TrendingUp, Users, Database, BarChart3, Star, ArrowRight, Play,
  CheckCircle, Globe, Smartphone, Lock, Download, Award, Target,
  Rocket, Brain, Eye, Layers, Cpu, Wifi, Route, MapPin, Calculator,
  Receipt, PieChart, Navigation
} from 'lucide-vue-next';
import { usePage } from '@inertiajs/vue3';

// Get page props from Inertia
const page = usePage();

// Scroll-based animations
const heroRef = ref<HTMLElement>();
const featuresRef = ref<HTMLElement>();
const statsRef = ref<HTMLElement>();
const isVisible = ref({
  hero: false,
  features: false,
  stats: false
});

// Modern features with enhanced data
const modernFeatures = [
  {
    icon: Brain,
    title: 'KI-Fahrzeugassistent',
    description: 'Intelligente Wartungsvorhersagen durch Machine Learning. Optimiert Kosten und verlängert die Fahrzeuglebensdauer.',
    color: 'from-blue-500 via-purple-500 to-pink-500',
    gradient: 'bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20',
    delay: '0ms',
    features: ['Predictive Analytics', 'Cost Optimization', 'Smart Alerts'],
    stats: '94% Genauigkeit'
  },
  {
    icon: Zap,
    title: 'Instant-Synchronisation',
    description: 'Blitzschnelle Echtzeit-Updates auf allen Geräten. Cloud-native Architektur für maximale Performance.',
    color: 'from-yellow-400 via-orange-500 to-red-500',
    gradient: 'bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20',
    delay: '100ms',
    features: ['Real-time Sync', 'Offline-fähig', 'Multi-Device'],
    stats: '<100ms Latenz'
  },
  {
    icon: Shield,
    title: 'Blockchain-Sicherheit',
    description: 'Unveränderliche Fahrzeughistorie durch Blockchain-Technologie. Maximale Transparenz und Vertrauen.',
    color: 'from-green-400 via-teal-500 to-blue-500',
    gradient: 'bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20',
    delay: '200ms',
    features: ['Tamper-proof', 'Audit Trail', 'Crypto-secured'],
    stats: '100% Unveränderlich'
  },
  {
    icon: Eye,
    title: 'AR-Fahrzeugscanner',
    description: 'Augmented Reality für Schadenserkennung und Ersatzteilidentifikation. Die Zukunft der Fahrzeugdiagnose.',
    color: 'from-purple-500 via-pink-500 to-red-500',
    gradient: 'bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20',
    delay: '300ms',
    features: ['AR-Scanning', 'Damage Detection', 'Part Recognition'],
    stats: '99.7% Erkennungsrate'
  },
  {
    icon: Cpu,
    title: 'Edge Computing',
    description: 'Lokale KI-Verarbeitung für Datenschutz und Geschwindigkeit. Ihre Daten bleiben auf Ihrem Gerät.',
    color: 'from-indigo-500 via-blue-500 to-cyan-500',
    gradient: 'bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20',
    delay: '400ms',
    features: ['Local Processing', 'Privacy First', 'Zero Latency'],
    stats: '0ms Verzögerung'
  },
  {
    icon: Layers,
    title: 'Digital Twin',
    description: 'Digitaler Zwilling Ihres Fahrzeugs mit Echtzeit-Simulation und Vorhersagemodellen.',
    color: 'from-teal-400 via-green-500 to-emerald-500',
    gradient: 'bg-gradient-to-br from-teal-50 to-green-50 dark:from-teal-900/20 dark:to-green-900/20',
    delay: '500ms',
    features: ['3D-Simulation', 'Predictive Models', 'Virtual Testing'],
    stats: '1:1 Genauigkeit'
  },
  {
    icon: Route,
    title: 'Smart Fahrtenbuch',
    description: 'KI-gestütztes, finanzamtkonformes Fahrtenbuch mit automatischer Kategorisierung und Steueroptimierung.',
    color: 'from-emerald-400 via-teal-500 to-cyan-500',
    gradient: 'bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20',
    delay: '600ms',
    features: ['Auto-Kategorisierung', 'Steuer-Export', 'GPS-Tracking'],
    stats: '100% Finanzamt-konform'
  }
];

// Enhanced statistics with real-time updates
const liveStats = [
  {
    icon: Users,
    label: 'Aktive Nutzer',
    value: ref(0),
    target: 25847,
    suffix: '+',
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    description: 'Wachstum: +15% monatlich'
  },
  {
    icon: Car,
    label: 'Verwaltete Fahrzeuge',
    value: ref(0),
    target: 67156,
    suffix: '+',
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/30',
    description: 'Alle Fahrzeugtypen'
  },
  {
    icon: Database,
    label: 'KI-Analysen',
    value: ref(0),
    target: 487943,
    suffix: '+',
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    description: 'Täglich verarbeitet'
  },
  {
    icon: TrendingUp,
    label: 'Eingesparte Kosten',
    value: ref(0),
    target: 2847,
    suffix: 'k€',
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-100 dark:bg-orange-900/30',
    description: 'Durch KI-Optimierung'
  },
  {
    icon: Route,
    label: 'Fahrtenbuch-Einträge',
    value: ref(0),
    target: 156789,
    suffix: '+',
    color: 'text-emerald-600 dark:text-emerald-400',
    bgColor: 'bg-emerald-100 dark:bg-emerald-900/30',
    description: 'Steuerkonform dokumentiert'
  }
];

// Enhanced benefits with modern icons
const modernBenefits = [
  { icon: Rocket, text: 'Komplett kostenlos - für immer', highlight: true },
  { icon: Wifi, text: 'Cloud-native - überall verfügbar' },
  { icon: Smartphone, text: 'Progressive Web App - native Performance' },
  { icon: Lock, text: 'DSGVO-konform - Made in Germany' },
  { icon: Download, text: 'Vollständiger Datenexport - jederzeit' },
  { icon: Award, text: 'Finanzamt-zertifiziert - steueroptimiert' },
  { icon: Brain, text: 'KI-gestützt - selbstlernend' },
  { icon: Zap, text: 'Echtzeit-Sync - millisekunden-genau' },
  { icon: Route, text: 'Automatisches Fahrtenbuch - GPS-basiert', highlight: true },
  { icon: Calculator, text: 'Steuerersparnis-Rechner - integriert' },
  { icon: Receipt, text: 'Export für Steuerberater - ein Klick' }
];

// Animation functions
const animateCounter = (stat: any, duration: number = 2500) => {
  const startTime = Date.now();
  const startValue = 0;
  const endValue = stat.target;
  
  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // Advanced easing function for smooth animation
    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
    stat.value.value = Math.floor(startValue + (endValue - startValue) * easeOutCubic);
    
    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };
  
  animate();
};

// Intersection Observer for scroll animations
const setupIntersectionObserver = () => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const target = entry.target as HTMLElement;
          if (target === statsRef.value && !isVisible.value.stats) {
            isVisible.value.stats = true;
            // Animate counters with staggered delays
            liveStats.forEach((stat, index) => {
              setTimeout(() => animateCounter(stat), index * 300);
            });
          }
        }
      });
    },
    { threshold: 0.2 }
  );

  if (statsRef.value) observer.observe(statsRef.value);
};

// Floating animation for hero elements
const floatingAnimation = ref(true);

onMounted(() => {
  nextTick(() => {
    setupIntersectionObserver();
  });
});
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 overflow-hidden">
    
    <!-- Animated Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-teal-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-400/10 to-pink-600/10 rounded-full blur-2xl animate-spin" style="animation-duration: 20s;"></div>
    </div>

    <!-- Navigation -->
    <nav class="relative z-50 backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border-b border-white/20 dark:border-slate-700/50">
      <div class="container mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <Car class="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Fahrzeugakte.app
              </h1>
              <p class="text-xs text-gray-500 dark:text-gray-400">Next-Gen Vehicle Management</p>
            </div>
          </div>

          <div class="flex items-center gap-3">
            <template v-if="page.props.auth.user">
              <Link
                :href="route('dashboard')"
                class="px-6 py-2.5 text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl hover:shadow-blue-500/25 hover:scale-105 transition-all duration-300 flex items-center gap-2"
              >
                <Rocket class="h-4 w-4" />
                Dashboard
              </Link>
              <Link
                :href="route('fahrtenbuch.index')"
                class="px-6 py-2.5 text-sm font-medium bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-full shadow-lg hover:shadow-xl hover:shadow-emerald-500/25 hover:scale-105 transition-all duration-300 flex items-center gap-2"
              >
                <Route class="h-4 w-4" />
                Fahrtenbuch
              </Link>
            </template>
            <template v-else>
              <Link
                :href="route('login')"
                class="px-5 py-2.5 text-sm font-medium rounded-full backdrop-blur-sm bg-white/90 hover:bg-white dark:bg-slate-800/90 dark:hover:bg-slate-800 text-gray-800 dark:text-white border border-gray-200/50 dark:border-gray-700/50 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300"
              >
                Anmelden
              </Link>
              <Link
                :href="route('register')"
                class="px-5 py-2.5 text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl hover:shadow-blue-500/25 hover:scale-105 transition-all duration-300 flex items-center gap-2"
              >
                <Sparkles class="h-4 w-4" />
                Kostenlos starten
              </Link>
            </template>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section ref="heroRef" class="relative min-h-screen flex items-center justify-center px-6 py-20">
      <div class="container mx-auto text-center relative z-10">
        
        <!-- Hero Badge -->
        <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-blue-600/10 to-purple-600/10 border border-blue-200/50 dark:border-blue-800/50 text-blue-700 dark:text-blue-300 mb-8 backdrop-blur-sm">
          <Sparkles class="h-4 w-4 mr-2" />
          Revolutionäre KI-Technologie
          <div class="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>

        <!-- Main Headline -->
        <h1 class="text-5xl md:text-7xl font-black mb-6 leading-tight animate-slide-up">
          <span class="gradient-text animate-gradient">
            Die Zukunft
          </span>
          <br />
          <span class="text-gray-900 dark:text-white animate-slide-up animate-delay-200">
            der Fahrzeug-
          </span>
          <br />
          <span class="bg-gradient-to-r from-teal-500 to-blue-600 bg-clip-text text-transparent animate-slide-up animate-delay-400">
            verwaltung
          </span>
        </h1>

        <!-- Subtitle -->
        <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
          Erleben Sie die nächste Generation der Fahrzeugverwaltung mit
          <span class="font-semibold text-blue-600 dark:text-blue-400">KI-gestützter Wartungsvorhersage</span>,
          <span class="font-semibold text-emerald-600 dark:text-emerald-400">automatischem Fahrtenbuch</span>,
          <span class="font-semibold text-purple-600 dark:text-purple-400">Blockchain-Sicherheit</span> und
          <span class="font-semibold text-teal-600 dark:text-teal-400">Augmented Reality</span>.
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16 animate-fade-in-scale animate-delay-500">
          <Link
            :href="route('register')"
            class="group relative px-8 py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl shadow-2xl hover:shadow-3xl hover:shadow-blue-500/25 hover:scale-105 transition-all duration-300 flex items-center gap-3 btn-modern animate-pulse-glow"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition-opacity"></div>
            <div class="relative flex items-center gap-3">
              <Rocket class="h-5 w-5" />
              Kostenlos starten
              <ArrowRight class="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </div>
          </Link>

          <button class="group px-8 py-4 text-lg font-semibold text-gray-700 dark:text-gray-300 rounded-2xl border-2 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 flex items-center gap-3 glass dark:glass-dark">
            <Play class="h-5 w-5 group-hover:scale-110 transition-transform" />
            Demo ansehen
          </button>
        </div>

        <!-- Fahrtenbuch Quick Preview -->
        <div class="mt-16 max-w-4xl mx-auto">
          <div class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-3xl p-8 border border-white/20 dark:border-slate-700/50 shadow-2xl">
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                  <Route class="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900 dark:text-white">Smart Fahrtenbuch</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Automatisch • Finanzamtkonform</p>
                </div>
              </div>
              <div class="flex items-center gap-2 px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
                <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span class="text-xs font-medium text-emerald-700 dark:text-emerald-300">Live Tracking</span>
              </div>
            </div>

            <div class="grid md:grid-cols-3 gap-6">
              <div class="text-center p-4 bg-gray-50 dark:bg-slate-700/50 rounded-2xl">
                <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400 mb-1">2.847</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Geschäftliche km</div>
                <div class="text-xs text-emerald-600 dark:text-emerald-400 mt-1">+854€ Ersparnis</div>
              </div>
              <div class="text-center p-4 bg-gray-50 dark:bg-slate-700/50 rounded-2xl">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">1.203</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Private km</div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Automatisch erkannt</div>
              </div>
              <div class="text-center p-4 bg-gray-50 dark:bg-slate-700/50 rounded-2xl">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">97%</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">KI-Genauigkeit</div>
                <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Selbstlernend</div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </section>

    <!-- Features Section -->
    <section ref="featuresRef" class="relative py-32 px-6">
      <div class="container mx-auto">

        <!-- Section Header -->
        <div class="text-center mb-20">
          <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-purple-600/10 to-pink-600/10 border border-purple-200/50 dark:border-purple-800/50 text-purple-700 dark:text-purple-300 mb-6">
            <Brain class="h-4 w-4 mr-2" />
            Next-Gen Features
          </div>
          <h2 class="text-4xl md:text-6xl font-black text-gray-900 dark:text-white mb-6">
            Revolutionäre
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Technologien
            </span>
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Erleben Sie die Zukunft der Fahrzeugverwaltung mit bahnbrechenden Technologien,
            die heute schon verfügbar sind.
          </p>
        </div>

        <!-- Features Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="(feature, index) in modernFeatures"
            :key="feature.title"
            class="group relative"
            :style="{ animationDelay: feature.delay }"
          >
            <!-- Feature Card -->
            <div class="relative h-full p-8 rounded-3xl backdrop-blur-xl bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 hover:-translate-y-2">

              <!-- Gradient Background -->
              <div :class="feature.gradient" class="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <!-- Content -->
              <div class="relative z-10">
                <!-- Icon -->
                <div class="mb-6">
                  <div :class="`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.color} flex items-center justify-center shadow-lg`">
                    <component :is="feature.icon" class="h-8 w-8 text-white" />
                  </div>
                </div>

                <!-- Title & Description -->
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {{ feature.title }}
                </h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  {{ feature.description }}
                </p>

                <!-- Feature Tags -->
                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    v-for="tag in feature.features"
                    :key="tag"
                    class="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
                  >
                    {{ tag }}
                  </span>
                </div>

                <!-- Stats -->
                <div class="flex items-center justify-between">
                  <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">
                    {{ feature.stats }}
                  </span>
                  <ArrowRight class="h-5 w-5 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all duration-300" />
                </div>
              </div>

              <!-- Hover Effect -->
              <div class="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Interactive Demo Section -->
    <section class="relative py-32 px-6 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white overflow-hidden">

      <!-- Background Effects -->
      <div class="absolute inset-0">
        <div class="absolute top-0 left-0 w-full h-full opacity-20">
          <div class="w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23ffffff&quot; fill-opacity=&quot;0.05&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;2&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"></div>
        </div>
      </div>

      <div class="container mx-auto relative z-10">
        <div class="flex flex-col lg:flex-row items-center gap-16">

          <!-- Content -->
          <div class="flex-1 lg:order-1">
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/10 border border-white/20 text-white mb-6">
              <Eye class="h-4 w-4 mr-2" />
              Live Demo verfügbar
            </div>

            <h2 class="text-4xl md:text-5xl font-black mb-6">
              Sehen Sie die
              <span class="bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Magie
              </span>
              in Aktion
            </h2>

            <p class="text-xl text-blue-100 mb-8 leading-relaxed">
              Erleben Sie unsere revolutionäre KI-Technologie live. Sehen Sie, wie Augmented Reality
              und Machine Learning Ihre Fahrzeugverwaltung transformieren.
            </p>

            <!-- Demo Features -->
            <div class="space-y-4 mb-8">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Check class="h-4 w-4 text-green-400" />
                </div>
                <span class="text-blue-100">Echtzeit KI-Wartungsvorhersagen</span>
              </div>
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Check class="h-4 w-4 text-green-400" />
                </div>
                <span class="text-blue-100">Automatisches GPS-Fahrtenbuch</span>
              </div>
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Check class="h-4 w-4 text-green-400" />
                </div>
                <span class="text-blue-100">AR-basierte Schadenserkennung</span>
              </div>
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Check class="h-4 w-4 text-green-400" />
                </div>
                <span class="text-blue-100">Blockchain-gesicherte Historie</span>
              </div>
            </div>

            <button class="group px-8 py-4 text-lg font-semibold bg-white text-blue-900 rounded-2xl shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center gap-3">
              <Play class="h-5 w-5 group-hover:scale-110 transition-transform" />
              Interaktive Demo starten
              <ArrowRight class="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>

          <!-- Demo Preview -->
          <div class="flex-1 lg:order-2">
            <div class="relative">
              <!-- Main Demo Screen -->
              <div class="relative bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                <div class="aspect-video bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-2xl flex items-center justify-center">
                  <div class="text-center">
                    <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                      <Play class="h-10 w-10 text-white" />
                    </div>
                    <p class="text-white/80">Demo Video</p>
                  </div>
                </div>

                <!-- Floating Elements -->
                <div class="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg animate-bounce">
                  <Zap class="h-8 w-8 text-white" />
                </div>

                <div class="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-green-400 to-teal-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse">
                  <Check class="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Fahrtenbuch Feature Section -->
    <section class="relative py-32 px-6 bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 dark:from-emerald-900/20 dark:via-teal-900/20 dark:to-cyan-900/20">
      <div class="container mx-auto">

        <!-- Section Header -->
        <div class="text-center mb-20">
          <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-emerald-600/10 to-teal-600/10 border border-emerald-200/50 dark:border-emerald-800/50 text-emerald-700 dark:text-emerald-300 mb-6">
            <Route class="h-4 w-4 mr-2" />
            Intelligentes Fahrtenbuch
          </div>
          <h2 class="text-4xl md:text-6xl font-black text-gray-900 dark:text-white mb-6">
            Steueroptimiert
            <span class="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
              fahren
            </span>
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Unser KI-gestütztes Fahrtenbuch erfüllt alle Finanzamt-Anforderungen und maximiert
            automatisch Ihre Steuerersparnis durch intelligente Kategorisierung.
          </p>
        </div>

        <!-- Fahrtenbuch Features Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <!-- Feature 1: Automatische Erfassung -->
          <div class="group relative p-8 rounded-3xl backdrop-blur-xl bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <Navigation class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">GPS-Automatik</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">Automatische Erfassung aller Fahrten mit GPS-Tracking. Keine manuelle Eingabe mehr nötig.</p>
            <div class="flex items-center text-emerald-600 dark:text-emerald-400">
              <Check class="h-5 w-5 mr-2" />
              <span class="text-sm font-medium">100% automatisch</span>
            </div>
          </div>

          <!-- Feature 2: KI-Kategorisierung -->
          <div class="group relative p-8 rounded-3xl backdrop-blur-xl bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-teal-500 to-cyan-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <Brain class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Smart-Kategorisierung</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">KI erkennt automatisch geschäftliche und private Fahrten basierend auf Ihren Gewohnheiten.</p>
            <div class="flex items-center text-teal-600 dark:text-teal-400">
              <Check class="h-5 w-5 mr-2" />
              <span class="text-sm font-medium">97% Genauigkeit</span>
            </div>
          </div>

          <!-- Feature 3: Steuer-Export -->
          <div class="group relative p-8 rounded-3xl backdrop-blur-xl bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-500 to-blue-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <Receipt class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Finanzamt-Export</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">Direkter Export für Steuerberater und Finanzamt in allen gängigen Formaten.</p>
            <div class="flex items-center text-cyan-600 dark:text-cyan-400">
              <Check class="h-5 w-5 mr-2" />
              <span class="text-sm font-medium">Zertifiziert konform</span>
            </div>
          </div>
        </div>

        <!-- Steuerersparnis Highlight -->
        <div class="relative p-8 rounded-3xl bg-gradient-to-br from-emerald-500 to-teal-600 text-white overflow-hidden">
          <div class="absolute inset-0 bg-black/10"></div>
          <div class="relative z-10 text-center">
            <div class="inline-flex items-center px-4 py-2 rounded-full bg-white/20 text-white mb-6">
              <Calculator class="h-4 w-4 mr-2" />
              Steuerersparnis-Rechner
            </div>
            <h3 class="text-3xl md:text-4xl font-bold mb-4">
              Bis zu 3.000€ jährlich sparen
            </h3>
            <p class="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
              Unser intelligentes Fahrtenbuch optimiert automatisch Ihre Steuerersparnis durch
              präzise Dokumentation aller geschäftlichen Fahrten.
            </p>
            <div class="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div class="text-2xl font-bold mb-2">0,30€</div>
                <div class="text-emerald-100">pro km absetzbar</div>
              </div>
              <div>
                <div class="text-2xl font-bold mb-2">100%</div>
                <div class="text-emerald-100">finanzamtkonform</div>
              </div>
              <div>
                <div class="text-2xl font-bold mb-2">1-Klick</div>
                <div class="text-emerald-100">Export</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Statistics Section -->
    <section ref="statsRef" class="relative py-32 px-6">
      <div class="container mx-auto">

        <!-- Section Header -->
        <div class="text-center mb-20">
          <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-green-600/10 to-teal-600/10 border border-green-200/50 dark:border-green-800/50 text-green-700 dark:text-green-300 mb-6">
            <BarChart3 class="h-4 w-4 mr-2" />
            Live Statistiken
          </div>
          <h2 class="text-4xl md:text-6xl font-black text-gray-900 dark:text-white mb-6">
            Zahlen, die
            <span class="bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
              begeistern
            </span>
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Unsere Community wächst täglich und vertraut auf unsere innovative Technologie.
          </p>
        </div>

        <!-- Stats Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-5 gap-8">
          <div
            v-for="stat in liveStats"
            :key="stat.label"
            class="group relative p-8 rounded-3xl backdrop-blur-xl bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105"
          >
            <!-- Icon -->
            <div :class="`w-16 h-16 rounded-2xl ${stat.bgColor} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`">
              <component :is="stat.icon" :class="`h-8 w-8 ${stat.color}`" />
            </div>

            <!-- Value -->
            <div class="text-4xl font-black text-gray-900 dark:text-white mb-2">
              {{ stat.value.value.toLocaleString() }}{{ stat.suffix }}
            </div>

            <!-- Label -->
            <div class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
              {{ stat.label }}
            </div>

            <!-- Description -->
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ stat.description }}
            </div>

            <!-- Hover Effect -->
            <div class="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="relative py-32 px-6 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900">
      <div class="container mx-auto">

        <!-- Section Header -->
        <div class="text-center mb-20">
          <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-blue-600/10 to-purple-600/10 border border-blue-200/50 dark:border-blue-800/50 text-blue-700 dark:text-blue-300 mb-6">
            <Award class="h-4 w-4 mr-2" />
            Ihre Vorteile
          </div>
          <h2 class="text-4xl md:text-6xl font-black text-gray-900 dark:text-white mb-6">
            Warum
            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Fahrzeugakte.app?
            </span>
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Entdecken Sie die Vorteile einer modernen, KI-gestützten Fahrzeugverwaltung
            der nächsten Generation.
          </p>
        </div>

        <!-- Benefits Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div
            v-for="(benefit, index) in modernBenefits"
            :key="benefit.text"
            class="group relative p-6 rounded-2xl backdrop-blur-xl bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            :class="{ 'ring-2 ring-blue-500/50': benefit.highlight }"
          >
            <!-- Icon -->
            <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <component :is="benefit.icon" class="h-6 w-6 text-white" />
            </div>

            <!-- Text -->
            <p class="text-gray-700 dark:text-gray-300 font-medium leading-relaxed">
              {{ benefit.text }}
            </p>

            <!-- Highlight Badge -->
            <div v-if="benefit.highlight" class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <Star class="h-3 w-3 text-white" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="relative py-32 px-6 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white overflow-hidden">

      <!-- Background Effects -->
      <div class="absolute inset-0">
        <div class="absolute inset-0 opacity-30">
          <div class="w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;100&quot; height=&quot;100&quot; viewBox=&quot;0 0 100 100&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cpath d=&quot;M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z&quot; fill=&quot;%23ffffff&quot; fill-opacity=&quot;0.05&quot; fill-rule=&quot;evenodd&quot;/%3E%3C/svg%3E')"></div>
        </div>
        <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-400/20 to-transparent rounded-full blur-3xl"></div>
        <div class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-purple-400/20 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div class="container mx-auto text-center relative z-10">

        <!-- Header -->
        <div class="mb-16">
          <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/10 border border-white/20 text-white mb-6">
            <Rocket class="h-4 w-4 mr-2" />
            Bereit für die Zukunft?
          </div>

          <h2 class="text-4xl md:text-6xl font-black mb-6">
            Starten Sie
            <span class="bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              heute
            </span>
            in die Zukunft
          </h2>

          <p class="text-xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Schließen Sie sich tausenden zufriedenen Nutzern an und erleben Sie die Revolution
            der Fahrzeugverwaltung. Komplett kostenlos, für immer.
          </p>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
          <Link
            :href="route('register')"
            class="group relative px-10 py-5 text-xl font-bold bg-white text-blue-900 rounded-2xl shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 flex items-center gap-4"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl blur opacity-0 group-hover:opacity-75 transition-opacity duration-300"></div>
            <div class="relative flex items-center gap-4">
              <Rocket class="h-6 w-6" />
              Jetzt kostenlos registrieren
              <ArrowRight class="h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </div>
          </Link>

          <Link
            v-if="page.props.auth.user"
            :href="route('dashboard')"
            class="group px-10 py-5 text-xl font-bold text-white rounded-2xl border-2 border-white/30 hover:border-white hover:bg-white/10 transition-all duration-300 flex items-center gap-4"
          >
            <Target class="h-6 w-6 group-hover:scale-110 transition-transform" />
            Zum Dashboard
          </Link>
        </div>

        <!-- Trust Indicators -->
        <div class="flex flex-wrap items-center justify-center gap-8 text-blue-200">
          <div class="flex items-center gap-2">
            <Shield class="h-5 w-5" />
            <span class="text-sm">DSGVO-konform</span>
          </div>
          <div class="flex items-center gap-2">
            <Lock class="h-5 w-5" />
            <span class="text-sm">SSL-verschlüsselt</span>
          </div>
          <div class="flex items-center gap-2">
            <Award class="h-5 w-5" />
            <span class="text-sm">Made in Germany</span>
          </div>
          <div class="flex items-center gap-2">
            <CheckCircle class="h-5 w-5" />
            <span class="text-sm">100% kostenlos</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="relative bg-slate-900 text-white py-16 px-6">
      <div class="container mx-auto">
        <div class="grid md:grid-cols-4 gap-8">

          <!-- Brand -->
          <div class="md:col-span-2">
            <div class="flex items-center space-x-3 mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                <Car class="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Fahrzeugakte.app
                </h3>
                <p class="text-sm text-gray-400">Next-Gen Vehicle Management</p>
              </div>
            </div>
            <p class="text-gray-300 mb-6 max-w-md">
              Die Zukunft der Fahrzeugverwaltung mit KI-gestützter Technologie,
              Blockchain-Sicherheit und Augmented Reality.
            </p>
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2 text-green-400">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-sm">System online</span>
              </div>
            </div>
          </div>

          <!-- Links -->
          <div>
            <h4 class="text-lg font-semibold mb-4">Plattform</h4>
            <ul class="space-y-2 text-gray-300">
              <li><Link :href="route('about')" class="hover:text-blue-400 transition-colors">Über uns</Link></li>
              <li><Link :href="route('contact')" class="hover:text-blue-400 transition-colors">Kontakt</Link></li>
              <li><a href="#" class="hover:text-blue-400 transition-colors">Features</a></li>
              <li><a href="#" class="hover:text-blue-400 transition-colors">Preise</a></li>
            </ul>
          </div>

          <!-- Legal -->
          <div>
            <h4 class="text-lg font-semibold mb-4">Rechtliches</h4>
            <ul class="space-y-2 text-gray-300">
              <li><Link :href="route('legal.datenschutz')" class="hover:text-blue-400 transition-colors">Datenschutz</Link></li>
              <li><Link :href="route('legal.impressum')" class="hover:text-blue-400 transition-colors">Impressum</Link></li>
              <li><Link :href="route('legal.agb')" class="hover:text-blue-400 transition-colors">AGB</Link></li>
              <li><Link :href="route('legal.terms')" class="hover:text-blue-400 transition-colors">Nutzungsbedingungen</Link></li>
            </ul>
          </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row items-center justify-between">
          <p class="text-gray-400 text-sm">
            © 2024 Fahrzeugakte.app. Alle Rechte vorbehalten.
          </p>
          <div class="flex items-center gap-4 mt-4 md:mt-0">
            <span class="text-gray-400 text-sm">Powered by</span>
            <div class="flex items-center gap-2">
              <Brain class="h-4 w-4 text-blue-400" />
              <span class="text-sm text-blue-400 font-medium">KI-Technologie</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* Modern gradient animations */
.gradient-text {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.animate-gradient {
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Slide up animations */
.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade in scale animation */
.animate-fade-in-scale {
  animation: fadeInScale 0.8s ease-out forwards;
  opacity: 0;
  transform: scale(0.9);
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

@keyframes fadeInScale {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse glow effect */
.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}

/* Glass morphism effects */
.glass {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern button styles */
.btn-modern {
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Floating animation for cards */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .grid.lg\\:grid-cols-5 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid.lg\\:grid-cols-5 > div:last-child {
    grid-column: span 2;
  }
}
</style>
