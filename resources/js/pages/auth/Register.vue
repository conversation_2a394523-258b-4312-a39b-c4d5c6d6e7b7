<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
});

const isSubmitting = ref(false);

const submit = () => {
    isSubmitting.value = true;
    form.post(route('register'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation');
            isSubmitting.value = false;
        },
    });
};
</script>

<template>
    <div class="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950 flex items-center justify-center p-6">
        <div class="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
            <div class="text-center mb-8">
                <a :href="route('home')" class="inline-block mb-6">
                    <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-12 w-auto mx-auto" />
                </a>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Konto erstellen</h1>
                <p class="text-gray-600 dark:text-gray-400">Willkommen bei Fahrzeugakte.app - Ihre digitale Fahrzeugverwaltung</p>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
                <!-- Name Field -->
                <div>
                    <label for="name" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Vollständiger Name</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <input
                            id="name"
                            type="text"
                            v-model="form.name"
                            class="pl-10 w-full h-12 rounded-full bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:border-primary"
                            placeholder="Max Mustermann"
                            required
                            autofocus
                        />
                    </div>
                    <p v-if="form.errors.name" class="mt-2 text-sm text-red-600 dark:text-red-400">
                        {{ form.errors.name }}
                    </p>
                </div>

                <!-- Email Field -->
                <div>
                    <label for="email" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">E-Mail-Adresse</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <input
                            id="email"
                            type="email"
                            v-model="form.email"
                            class="pl-10 w-full h-12 rounded-full bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:border-primary"
                            placeholder="<EMAIL>"
                            required
                        />
                    </div>
                    <p v-if="form.errors.email" class="mt-2 text-sm text-red-600 dark:text-red-400">
                        {{ form.errors.email }}
                    </p>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Passwort</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <input
                            id="password"
                            type="password"
                            v-model="form.password"
                            class="pl-10 w-full h-12 rounded-full bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:border-primary"
                            placeholder="••••••••"
                            required
                        />
                    </div>
                    <p v-if="form.errors.password" class="mt-2 text-sm text-red-600 dark:text-red-400">
                        {{ form.errors.password }}
                    </p>
                </div>

                <!-- Password Confirmation Field -->
                <div>
                    <label for="password_confirmation" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Passwort bestätigen</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <input
                            id="password_confirmation"
                            type="password"
                            v-model="form.password_confirmation"
                            class="pl-10 w-full h-12 rounded-full bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:border-primary"
                            placeholder="••••••••"
                            required
                        />
                    </div>
                    <p v-if="form.errors.password_confirmation" class="mt-2 text-sm text-red-600 dark:text-red-400">
                        {{ form.errors.password_confirmation }}
                    </p>
                </div>

                <!-- Terms -->
                <div class="mt-2 text-xs text-gray-500 dark:text-gray-400 px-2">
                    Mit der Registrierung stimmen Sie unseren <a href="/legal/terms" class="text-primary hover:underline">Nutzungsbedingungen</a> und <a href="/legal/datenschutz" class="text-primary hover:underline">Datenschutzrichtlinien</a> zu.
                </div>

                <!-- Submit Button -->
                <button 
                    type="submit" 
                    class="w-full h-12 rounded-full text-base font-medium bg-primary hover:bg-primary/90 text-white transition-all shadow-md hover:shadow-lg flex items-center justify-center"
                    :disabled="isSubmitting"
                >
                    <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Konto erstellen
                </button>

                <!-- Divider -->
                <div class="relative my-6">
                    <div class="absolute inset-0 flex items-center">
                        <span class="w-full border-t border-gray-200 dark:border-gray-700"></span>
                    </div>
                    <div class="relative flex justify-center text-xs">
                        <span class="bg-white dark:bg-gray-800 px-2 text-gray-500">oder</span>
                    </div>
                </div>

                <!-- Login Link -->
                <div class="text-center text-sm text-gray-600 dark:text-gray-400">
                    Sie haben bereits ein Konto?
                    <a :href="route('login')" class="text-primary hover:text-primary/80 font-medium">
                        Jetzt anmelden
                    </a>
                </div>
            </form>
        </div>
    </div>
</template>

<style>
:root {
  --primary: #6366f1;
  --primary-rgb: 99, 102, 241;
}

.bg-primary {
  background-color: var(--primary);
}

.text-primary {
  color: var(--primary);
}

.hover\:bg-primary\/90:hover {
  background-color: rgba(var(--primary-rgb), 0.9);
}

.hover\:text-primary\/80:hover {
  color: rgba(var(--primary-rgb), 0.8);
}

.focus\:ring-primary:focus {
  --tw-ring-color: var(--primary);
}

.focus\:border-primary:focus {
  border-color: var(--primary);
}
</style>
