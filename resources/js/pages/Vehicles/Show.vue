<script setup lang="ts">
import { Link, router, usePage } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import axios from 'axios';
import QuickAccessTab from '@/components/QuickAccess/QuickAccessTab.vue';

// Component options
defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  vehicle: {
    id: number;
    make: string;
    model: string;
    type: string | null;
    hsn: string | null;
    tsn: string | null;
    vin: string | null;
    license_plate: string | null;
    year: number | null;
    mileage: number;
    purchase_date: string | null;
    purchase_price: number | null;
    notes: string | null;
    color: string | null;
    fuel_type: string | null;
    power: number | null;
    engine_size: string | null;
    transmission: string | null;
    image: string | null;
    is_active: boolean;
    tire_sizes: Array<{
      size: string;
      position: 'front' | 'rear' | 'both';
      type?: string;
    }> | null;
    parts: Array<{
      name: string;
      type: string;
      article_number: string | null;
      oem_number: string | null;
      manufacturer_number: string | null;
      notes: string | null;
      purchase_links: Array<{
        label: string;
        url: string;
      }>;
    }> | null;
    created_at: string;
    updated_at: string;
    maintenance_logs: Array<{
      id: number;
      title: string;
      type: string;
      date: string;
      mileage: number | null;
      cost: number | null;
      description: string | null;
      location: string | null;
      parts: Array<{
        id: number;
        name: string;
        part_number: string | null;
        quantity: number;
        cost: number | null;
      }>;
      next_inspection_date?: string;
    }>;
    service_reminders: Array<{
      id: number;
      title: string;
      description: string | null;
      due_date: string | null;
      due_mileage: number | null;
      status: string;
      priority: string;
    }>;
    attachments: Array<{
      id: number;
      filename: string;
      original_filename: string;
      mime_type: string;
      type: string;
      file_path: string;
      description: string | null;
      created_at: string;
    }>;
    quick_access_entries: Array<{
      id: number;
      type: string;
      date: string;
      mileage: number | null;
      amount: number;
      quantity: number | null;
      price_per_unit: number | null;
      location: string | null;
      notes: string | null;
    }>;
  };
  totalCosts: number;
  maintenanceCosts: number;
  quickAccessCosts: number;
  upcomingServices: Array<{
    id: number;
    title: string;
    due_date: string | null;
    due_mileage: number | null;
    status: string;
    priority: string;
  }>;
}>();

// Dialog für Löschbestätigung
const showDeleteDialog = ref(false);
const showDeleteReminderDialog = ref(false);
const showDeleteAttachmentDialog = ref(false);
const reminderToDelete = ref<number | null>(null);
const attachmentToDelete = ref<number | null>(null);

// Toast Notification
const showToast = ref(false);
const toastMessage = ref('');

// Add this new line for tab management
const activeTab = ref(localStorage.getItem(`vehicle_${props.vehicle.id}_activeTab`) || 'fahrzeugdaten'); // default tab
const fileInputRef = ref<HTMLInputElement | null>(null);
const isUploading = ref(false);

// Watch for changes to activeTab and save to localStorage
watch(activeTab, (newValue) => {
  localStorage.setItem(`vehicle_${props.vehicle.id}_activeTab`, newValue);
});

// Get the current user from Inertia shared props
const page = usePage();
const currentUser = computed(() => page.props.auth.user);

const formatDate = (dateString: string | null) => {
  if (!dateString) return "-";

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

const formatCurrency = (value: number | null) => {
  if (value === null) return "-";

  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(value);
};

const getPriorityClass = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'badge-error';
    case 'medium':
      return 'badge-warning';
    case 'low':
      return 'badge-info';
    default:
      return 'badge-secondary';
  }
};

const getMaintenanceTypeClass = (type: string) => {
  switch (type) {
    case 'repair':
      return 'badge-error';
    case 'service':
      return 'badge-info';
    case 'modification':
      return 'badge-secondary';
    case 'inspection':
      return 'badge-warning';
    case 'purchase':
      return 'badge-success';
    case 'sale':
      return 'badge-primary';
    case 'refueling':
      return 'badge-accent';
    case 'carwash':
      return 'badge-info';
    case 'parking':
      return 'badge-neutral';
    case 'tires':
      return 'badge-accent';
    default:
      return 'badge-neutral';
  }
};

const typeLabels: Record<string, string> = {
  'service': 'Wartung',
  'repair': 'Reparatur',
  'modification': 'Umbau',
  'inspection': 'TÜV/HU',
  'purchase': 'Kauf',
  'sale': 'Verkauf',
  'insurance': 'Versicherung',
  'refueling': 'Tanken',
  'carwash': 'Autowäsche',
  'parking': 'Parkgebühren',
  'tires': 'Reifen',
  'other': 'Sonstiges'
};

const fuelTypeLabels: Record<string, string> = {
  'petrol': 'Benzin',
  'diesel': 'Diesel',
  'electric': 'Elektro',
  'hybrid': 'Hybrid',
  'plugin_hybrid': 'Plug-in-Hybrid',
  'lpg': 'Autogas (LPG)',
  'cng': 'Erdgas (CNG)',
  'hydrogen': 'Wasserstoff',
  'other': 'Sonstiges'
};

const transmissionLabels: Record<string, string> = {
  'manual': 'Manuell',
  'automatic': 'Automatik',
  'semi_automatic': 'Halbautomatik',
  'cvt': 'Stufenlos (CVT)',
  'dual_clutch': 'Doppelkupplung (DSG)'
};

const getFuelTypeLabel = (type: string | null): string => {
  if (!type) return "-";
  return fuelTypeLabels[type] || type;
};

const getTransmissionLabel = (type: string | null): string => {
  if (!type) return "-";
  return transmissionLabels[type] || type;
};

const deleteVehicle = () => {
  router.delete(route('vehicles.destroy', props.vehicle.id));
};

const confirmDelete = () => {
  showDeleteDialog.value = true;
};

const cancelDelete = () => {
  showDeleteDialog.value = false;
};

const generateReport = () => {
  window.open(route('vehicles.report', props.vehicle.id), '_blank');
};

const confirmDeleteReminder = (reminderId: number) => {
  reminderToDelete.value = reminderId;
  showDeleteReminderDialog.value = true;
};

const cancelDeleteReminder = () => {
  showDeleteReminderDialog.value = false;
  reminderToDelete.value = null;
};

const deleteReminder = async () => {
  if (!reminderToDelete.value) return;

  try {
    // Use router.delete which is Inertia's method for delete requests
    // This will follow Laravel's routes properly
    router.delete(route('service-reminders.destroy', reminderToDelete.value), {
      onSuccess: () => {
        showDeleteReminderDialog.value = false;
        reminderToDelete.value = null;

        // Show success message
        toastMessage.value = 'Serviceerinnerung wurde erfolgreich gelöscht';
        showToast.value = true;
        setTimeout(() => {
          showToast.value = false;
        }, 3000);
      }
    });
  } catch (error) {
    console.error('Error deleting reminder:', error);
    alert('Fehler beim Löschen der Serviceerinnerung');
  }
};

const typeLabelsQuickEntry: Record<string, string> = {
  'refueling': 'Tanken',
  'car_wash': 'Autowäsche',
  'parking_fee': 'Parkgebühren'
};

const openQuickEntryModal = (type: 'refueling' | 'car_wash' | 'parking_fee') => {
  // Use ref from QuickAccessTab component
  const quickAccessTabRef = document.querySelector('button[data-type="' + type + '"]');
  if (quickAccessTabRef) {
    (quickAccessTabRef as HTMLButtonElement).click();
  }
};

const createQuickEntry = (type: 'refueling' | 'carwash' | 'parking') => {
  // Map old types to new types
  const typeMap: Record<string, string> = {
    'refueling': 'refueling',
    'carwash': 'car_wash',
    'parking': 'parking_fee'
  };

  openQuickEntryModal(typeMap[type] as 'refueling' | 'car_wash' | 'parking_fee');
};

// Add the helper functions for calculating costs
interface MaintenanceLogPart {
  id: number;
  name: string;
  part_number: string | null;
  quantity: number;
  cost: number | null;
}

interface MaintenanceLog {
  id: number;
  title: string;
  type: string;
  date: string;
  mileage: number | null;
  cost: number | null;
  description: string | null;
  location: string | null;
  parts?: MaintenanceLogPart[];
}

// Add this computed property to inspect the first maintenance log
const firstLog = computed(() => {
  if (props.vehicle.maintenance_logs && props.vehicle.maintenance_logs.length > 0) {
    const log = props.vehicle.maintenance_logs[0];
    console.log('First maintenance log:', JSON.stringify(log, null, 2));
    return log;
  }
  return null;
});

// Update the calculation functions to include more detailed debugging
const calculatePartsCost = (log: MaintenanceLog): number => {
  console.log('Parts array:', log.parts);

  if (!log.parts || log.parts.length === 0) return 0;

  const partsCost = log.parts.reduce((total: number, part: MaintenanceLogPart) => {
    console.log('Part:', part.name, 'Cost:', part.cost, 'Quantity:', part.quantity);
    // Parse costs and quantities as numbers to ensure proper calculation
    const partCost = part.cost !== null ? Number(part.cost) : 0;
    const quantity = part.quantity !== null ? Number(part.quantity) : 1;
    const itemTotal = partCost * quantity;
    console.log('Item total:', itemTotal);
    return total + itemTotal;
  }, 0);

  console.log('Total parts cost:', partsCost);
  return partsCost;
};

const calculateTotalCost = (log: MaintenanceLog): number => {
  // Parse labor cost as number to ensure proper calculation
  const laborCost = log.cost !== null ? Number(log.cost) : 0;
  console.log('Labor cost:', laborCost);

  const partsCost = calculatePartsCost(log);
  console.log('Parts cost:', partsCost);

  // Calculate the total cost
  const totalCost = laborCost + partsCost;
  console.log('Total cost:', totalCost);

  return totalCost;
};

const triggerFileUpload = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!target.files || target.files.length === 0) return;

  const files = target.files;
  isUploading.value = true;

  try {
    const formData = new FormData();

    // Füge alle Dateien zum FormData hinzu
    for (let i = 0; i < files.length; i++) {
      formData.append(`attachments[${i}]`, files[i]);
      formData.append(`types[${i}]`, 'other');
      formData.append(`descriptions[${i}]`, '');
      formData.append(`file_sizes[${i}]`, String(files[i].size));
    }

    // Füge die User-ID einmal hinzu
    formData.append('user_id', String(currentUser.value.id));

    // Set desired tab in localStorage before page reload
    localStorage.setItem(`vehicle_${props.vehicle.id}_activeTab`, 'dokumente');

    // Verwende Axios.post mit der KORREKTEN Route aus der Route-Liste
    await axios.post(`/vehicles/${props.vehicle.id}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        'Accept': 'application/json'
      }
    });

    // Reset file input
    if (fileInputRef.value) {
      fileInputRef.value.value = '';
    }

    // Seite neu laden, um die neuen Dokumente anzuzeigen
    router.visit(route('vehicles.show', props.vehicle.id), {
      preserveState: false
    });

    isUploading.value = false;
  } catch (error: any) {
    console.error('Error uploading files:', error);
    console.error('File details:', {
      names: Array.from(files).map(file => file.name),
      sizes: Array.from(files).map(file => file.size),
      types: Array.from(files).map(file => file.type)
    });

    // Detaillierte Fehlerinformationen extrahieren
    let errorMessage = 'Fehler beim Hochladen der Dokumente';

    if (error.response) {
      // Der Request wurde gemacht und der Server antwortete mit einem Status-Code
      // der außerhalb des Bereichs von 2xx liegt
      console.error('Response error data:', error.response.data);
      console.error('Response error status:', error.response.status);
      console.error('Response error headers:', error.response.headers);

      if (error.response.data && error.response.data.message) {
        errorMessage = `Fehler: ${error.response.data.message}`;
      } else if (error.response.data && typeof error.response.data === 'string') {
        errorMessage = `Fehler: ${error.response.data}`;
      }
    } else if (error.request) {
      // Der Request wurde gemacht, aber keine Antwort erhalten
      console.error('Request was made but no response received:', error.request);
      errorMessage = 'Keine Antwort vom Server erhalten';
    } else {
      // Etwas ist bei der Request-Erstellung passiert
      console.error('Error during request setup:', error.message);
      errorMessage = `Fehler bei der Anfrage: ${error.message}`;
    }

    // Fehler anzeigen
    toastMessage.value = errorMessage;
    showToast.value = true;
    setTimeout(() => {
      showToast.value = false;
    }, 7000); // Längere Zeit, um den Fehler lesen zu können

    isUploading.value = false;
  }
};

const confirmDeleteAttachment = (id: number) => {
  attachmentToDelete.value = id;
  showDeleteAttachmentDialog.value = true;
};

const cancelDeleteAttachment = () => {
  showDeleteAttachmentDialog.value = false;
  attachmentToDelete.value = null;
};

const deleteAttachment = async () => {
  if (!attachmentToDelete.value) return;

  try {
    await axios.delete(`/vehicles/${props.vehicle.id}/attachments/${attachmentToDelete.value}`, {
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        'Accept': 'application/json'
      }
    });

    // Set desired tab in localStorage before page reload
    localStorage.setItem(`vehicle_${props.vehicle.id}_activeTab`, 'dokumente');

    // Refresh the page to show changes
    router.visit(route('vehicles.show', props.vehicle.id), {
      preserveState: false
    });
  } catch (error: any) {
    console.error('Error deleting attachment:', error);

    // Extract the error message from the response if available
    let errorMsg = 'Fehler beim Löschen des Dokuments';
    if (error.response && error.response.data && error.response.data.message) {
      errorMsg = error.response.data.message;
    }

    toastMessage.value = errorMsg;
    showToast.value = true;
    setTimeout(() => {
      showToast.value = false;
    }, 5000);
  } finally {
    showDeleteAttachmentDialog.value = false;
    attachmentToDelete.value = null;
  }
};

// Add these refs for the image viewer after the other refs
const showImageViewer = ref(false);
const currentImageIndex = ref(0);
const filteredAttachments = computed(() => {
  return props.vehicle.attachments || [];
});

// Get file type functions
const isImage = (mimeType: string) => {
  return mimeType.startsWith('image/');
};

const isPdf = (mimeType: string) => {
  return mimeType === 'application/pdf';
};

const getFileIcon = (mimeType: string) => {
  if (isImage(mimeType)) {
    return 'image';
  } else if (isPdf(mimeType)) {
    return 'pdf';
  } else if (mimeType.includes('word')) {
    return 'word';
  } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
    return 'excel';
  } else {
    return 'file';
  }
};

const openImageViewer = (index: number) => {
  currentImageIndex.value = index;
  showImageViewer.value = true;
};

const closeImageViewer = () => {
  showImageViewer.value = false;
};

const nextImage = () => {
  if (currentImageIndex.value < filteredAttachments.value.length - 1) {
    currentImageIndex.value++;
  } else {
    currentImageIndex.value = 0; // Loop back to the first image
  }
};

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
  } else {
    currentImageIndex.value = filteredAttachments.value.length - 1; // Loop to the last image
  }
};

// Add these methods for keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  if (showImageViewer.value) {
    switch (event.key) {
      case 'ArrowLeft':
        prevImage();
        break;
      case 'ArrowRight':
        nextImage();
        break;
      case 'Escape':
        closeImageViewer();
        break;
    }
  }
};

// Set up keyboard listeners
onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
});

// Add filter state for documents
const documentFilter = ref('all'); // 'all', 'images', 'documents'

// Add computed property for filtered attachments based on type
const displayedAttachments = computed(() => {
  if (documentFilter.value === 'all') {
    return filteredAttachments.value;
  } else if (documentFilter.value === 'images') {
    return filteredAttachments.value.filter(attachment => isImage(attachment.mime_type));
  } else if (documentFilter.value === 'documents') {
    return filteredAttachments.value.filter(attachment => !isImage(attachment.mime_type));
  }
  return filteredAttachments.value;
});

// Add this helper function to your script section
const getImagePath = (imagePath: string | null): string | undefined => {
  if (!imagePath) return undefined;

  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  if (imagePath.startsWith('storage/')) {
    return `/${imagePath}`;
  }

  return `/storage/${imagePath}`;
};

const getPartTypeLabel = (type: string | null): string => {
  if (!type) return "-";
  
  const partTypeLabels: Record<string, string> = {
    'filter': 'Filter',
    'oil': 'Öl',
    'brake': 'Bremsen',
    'spark_plug': 'Zündkerzen',
    'battery': 'Batterie',
    'tire': 'Reifen',
    'bulb': 'Lampen',
    'wiper': 'Scheibenwischer',
    'belt': 'Riemen',
    'coolant': 'Kühlmittel',
    'other': 'Sonstiges'
  };
  
  // Check if it's a custom type (doesn't match predefined types)
  if (type.startsWith('custom:')) {
    return type.substring(7); // Remove the 'custom:' prefix to show just the custom value
  }
  
  return partTypeLabels[type] || type;
};

const getPartTypeClass = (type: string | null): string => {
  if (!type) return "badge-neutral";
  
  const typeClasses: Record<string, string> = {
    'filter': 'badge-primary',
    'oil': 'badge-accent',
    'brake': 'badge-error',
    'spark_plug': 'badge-warning',
    'battery': 'badge-info',
    'tire': 'badge-secondary',
    'bulb': 'badge-warning',
    'wiper': 'badge-info',
    'belt': 'badge-error',
    'coolant': 'badge-primary',
    'other': 'badge-neutral'
  };
  
  if (type.startsWith('custom:')) {
    return 'badge-neutral';
  }
  
  return typeClasses[type] || 'badge-neutral';
};

const getTirePositionLabel = (position: string | null): string => {
  if (!position) return "Unbekannt";
  
  const positionLabels: Record<string, string> = {
    'front': 'Vorne',
    'rear': 'Hinten',
    'both': 'Vorne und Hinten'
  };
  
  return positionLabels[position] || position;
};

// Add function to get tire type label
const getTireTypeLabel = (type: string | null): string => {
  if (!type) return "Unbekannt";
  
  const typeLabels: Record<string, string> = {
    'summer': 'Sommerreifen',
    'winter': 'Winterreifen',
    'all_season': 'Ganzjahresreifen'
  };
  
  return typeLabels[type] || type;
};

// Get the most recent valid TÜV/HU inspection
const latestInspection = computed(() => {
  if (!props.vehicle.maintenance_logs || props.vehicle.maintenance_logs.length === 0) {
    return null;
  }
  
  // Filter for inspection entries with a next_inspection_date, sort by date in descending order
  const validInspections = props.vehicle.maintenance_logs
    .filter(log => log.type === 'inspection' && log.next_inspection_date)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  
  // Return the most recent one or null if none found
  return validInspections.length > 0 ? validInspections[0] : null;
});

</script>

<template>
  <div class="bg-gradient-to-b from-base-100 to-base-200 min-h-screen">
    <!-- Main header with vehicle image background -->
    <div class="relative">
      <div class="absolute inset-0 bg-gradient-to-b from-black/70 to-black/30 z-0"></div>
      <div v-if="vehicle.image" class="w-full h-72 md:h-96 object-cover bg-center bg-cover" :style="`background-image: url('${getImagePath(vehicle.image)}')`"></div>
      <div v-else class="w-full h-72 md:h-96 bg-gradient-to-r from-primary/10 to-secondary/10"></div>

      <!-- Navigation & Status -->
      <div class="absolute top-4 left-4 right-4 flex justify-between items-center z-20">
        <Link :href="route('vehicles.index')" class="btn btn-circle bg-white/20 hover:bg-white/40 backdrop-blur-md border-0 shadow-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>

        <div class="badge badge-lg py-3 px-4 text-white font-medium shadow-lg" :class="{ 'bg-success border-success': vehicle.is_active, 'bg-error border-error': !vehicle.is_active }">
          {{ vehicle.is_active ? 'Aktiv' : 'Inaktiv' }}
        </div>
      </div>

      <!-- Vehicle Info -->
      <div class="container mx-auto px-4 relative z-10">
        <div class="absolute bottom-8 left-0 right-0 px-6 md:px-8">
          <h1 class="text-4xl md:text-6xl font-bold text-white drop-shadow-lg">{{ vehicle.make }} {{ vehicle.model }}</h1>
          <div class="flex items-center mt-3">
            <!-- Moderneres Kennzeichen Design -->
            <div class="relative">
              <div class="px-6 py-2.5 bg-gradient-to-r from-gray-200 to-white rounded-xl shadow-lg border-2 border-gray-300 relative overflow-hidden">
                <!-- Blaue EU-Streifen an der Seite -->
                <div class="absolute left-0 top-0 bottom-0 w-6 bg-blue-600 flex flex-col items-center justify-center">
                  <div class="text-white text-xs font-bold">D</div>
                  <div class="mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 text-yellow-400">
                      <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>
                <!-- Stadt/Landkreiskürzel in fetterem Schwarz -->
                <div class="flex items-center">
                  <span v-if="vehicle.license_plate" class="ml-4 text-lg font-extrabold text-black tracking-wider">
                    {{ vehicle.license_plate.split(' ')[0] }}
                  </span>
                  <span v-else class="ml-4 text-lg font-extrabold text-black tracking-wider">XX</span>
                  
                  <!-- Prägesiegel-Effekt (gestrichelte Linie) -->
                  <span class="mx-2 border-l border-gray-400 h-6"></span>
                  
                  <!-- Kennzeichen-Hauptteil -->
                  <span v-if="vehicle.license_plate && vehicle.license_plate.includes(' ')" class="text-lg font-bold text-black tracking-wider">
                    {{ vehicle.license_plate.split(' ').slice(1).join(' ') }}
                  </span>
                  <span v-else-if="vehicle.license_plate" class="text-lg font-bold text-black tracking-wider">
                    {{ vehicle.license_plate }}
                  </span>
                  <span v-else class="text-lg font-bold text-black tracking-wider">XXXXX</span>
                </div>
                <!-- Reflektierender Effekt -->
                <div class="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent pointer-events-none"></div>
              </div>
            </div>

            <span class="mx-3 text-white/60">•</span>
            
            <!-- Moderneres Baujahr Design -->
            <div class="px-5 py-3 bg-gradient-to-br from-gray-700 to-gray-900 rounded-xl shadow-lg relative overflow-hidden">
              <!-- Obere Abschragung -->
              <div class="absolute top-0 right-0 w-4 h-4 bg-gray-900 shadow-inner transform rotate-45 translate-x-2 -translate-y-2"></div>
              <!-- Untere Abschragung -->
              <div class="absolute bottom-0 left-0 w-4 h-4 bg-gray-900 shadow-inner transform rotate-45 -translate-x-2 translate-y-2"></div>
              
              <!-- Jahr anzeigen mit "Fabrik"-Symbol -->
              <div class="flex flex-col items-center justify-center">
                <!-- Baujahr Label -->
                <div class="text-xs uppercase tracking-wider text-gray-300 font-semibold mb-0.5">Baujahr</div>
                
                <!-- Jahr mit Fabrik-Icon -->
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 text-yellow-400 mr-1.5">
                    <path d="M11.584 2.376a.75.75 0 01.832 0l9 6a.75.75 0 11-.832 1.248L12 3.901 3.416 9.624a.75.75 0 01-.832-1.248l9-6z" />
                    <path fill-rule="evenodd" d="M20.25 10.332v9.918H21a.75.75 0 010 1.5H3a.75.75 0 010-1.5h.75v-9.918a.75.75 0 01.634-.74A49.109 49.109 0 0112 9c2.59 0 5.134.202 7.616.592a.75.75 0 01.634.74zm-7.5 2.418a.75.75 0 00-1.5 0v6.75a.75.75 0 001.5 0v-6.75zm3-.75a.75.75 0 01.75.75v6.75a.75.75 0 01-1.5 0v-6.75a.75.75 0 01.75-.75zM9 12.75a.75.75 0 00-1.5 0v6.75a.75.75 0 001.5 0v-6.75z" clip-rule="evenodd" />
                  </svg>
                  <span class="text-xl font-bold text-white">{{ vehicle.year || "-" }}</span>
                </div>

                <!-- Fahrzeugalter anzeigen -->
                <div v-if="vehicle.year" class="mt-1 flex items-center justify-center bg-gray-800 rounded-md px-2 py-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3 text-amber-400 mr-1">
                    <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z" clip-rule="evenodd" />
                  </svg>
                  <span class="text-xs font-medium text-amber-300">{{ new Date().getFullYear() - vehicle.year }} Jahre</span>
                </div>
                
                <!-- Kleine Herstellerplakette-Imitation -->
                <div class="mt-0.5 text-[8px] text-gray-400 border-t border-gray-600 pt-0.5 text-center w-full">
                  PRODUCTION YEAR
                </div>
              </div>
              
              <!-- Reflektierender Effekt -->
              <div class="absolute inset-0 bg-gradient-to-bl from-white/10 to-transparent pointer-events-none"></div>
            </div>
            
            <!-- TÜV Badge - Display the most recent TÜV inspection -->
            <template v-if="latestInspection && latestInspection.next_inspection_date">
              <span class="mx-3 text-white/60">•</span>
              <div class="relative -mt-2">
                <!-- Outer circle with gradient -->
                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-xl">
                  <!-- Inner circle -->
                  <div class="w-20 h-20 rounded-full bg-white flex flex-col items-center justify-center border-4 border-white shadow-inner">
                    <div class="flex flex-col items-center">
                      <div class="text-base font-extrabold text-blue-700 tracking-wide leading-none">TÜV</div>
                      <div class="text-xl font-black text-blue-600 mt-0.5">
                        {{ new Date(latestInspection.next_inspection_date).getMonth() + 1 }}/{{ new Date(latestInspection.next_inspection_date).getFullYear().toString().substr(2, 2) }}
                      </div>
                    </div>
                  </div>
                  <!-- Small blue dot accent -->
                  <div class="absolute top-0 right-0 w-5 h-5 rounded-full bg-blue-400 border-2 border-white shadow-sm"></div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- Action buttons bar -->
    <div class="bg-base-100 shadow-lg sticky top-0 z-30">
      <div class="container mx-auto px-4 py-3 flex justify-end items-center gap-3">
        <button @click="generateReport" class="btn btn-primary gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
          PDF-Bericht
        </button>
        <Link :href="route('vehicles.edit', vehicle.id)" class="btn btn-outline gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
          Bearbeiten
        </Link>
        <button @click="confirmDelete" class="btn btn-error gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
          Löschen
        </button>
      </div>
    </div>

    <!-- Toast notification -->
    <div v-if="showToast" class="toast toast-top toast-center z-50">
      <div class="alert" :class="toastMessage.includes('erfolgreich') ? 'alert-success' : 'alert-error'">
        <span>{{ toastMessage }}</span>
      </div>
    </div>

    <!-- Versteckter File-Input für Dokument-Upload -->
    <input
      type="file"
      ref="fileInputRef"
      @change="handleFileUpload"
      class="hidden"
      multiple
      accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.svg,.zip,.rar,.txt"
    />

    <!-- Löschdialog -->
    <div v-if="showDeleteDialog" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 backdrop-blur-sm">
      <div class="bg-base-100 p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 class="text-lg font-bold mb-4">Fahrzeug löschen</h3>
        <p class="mb-6">
          Das Fahrzeug wird in den Papierkorb verschoben und kann innerhalb von 30 Tagen wiederhergestellt werden.
          Nach Ablauf dieser Frist wird es automatisch endgültig gelöscht.
        </p>
        <div class="flex justify-end gap-2">
          <button @click="cancelDelete" class="btn btn-outline">Abbrechen</button>
          <button @click="deleteVehicle" class="btn btn-error">Löschen</button>
        </div>
      </div>
    </div>

    <!-- Delete Reminder Dialog -->
    <div v-if="showDeleteReminderDialog" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 backdrop-blur-sm">
      <div class="bg-base-100 p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 class="text-lg font-bold mb-4">Serviceerinnerung löschen</h3>
        <p class="mb-6">Sind Sie sicher, dass Sie diese Serviceerinnerung löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.</p>
        <div class="flex justify-end gap-2">
          <button @click="cancelDeleteReminder" class="btn btn-outline">Abbrechen</button>
          <button @click="deleteReminder" class="btn btn-error">Löschen</button>
        </div>
      </div>
    </div>

    <!-- Delete Attachment Dialog -->
    <div v-if="showDeleteAttachmentDialog" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 backdrop-blur-sm">
      <div class="bg-base-100 p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 class="text-lg font-bold mb-4">Dokument löschen</h3>
        <p class="mb-6">Sind Sie sicher, dass Sie dieses Dokument löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.</p>
        <div class="flex justify-end gap-2">
          <button @click="cancelDeleteAttachment" class="btn btn-outline">Abbrechen</button>
          <button @click="deleteAttachment" class="btn btn-error">Löschen</button>
        </div>
      </div>
    </div>

    <div class="container mx-auto px-4 mb-12">
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Gesamtkosten Card -->
        <div class="bg-white rounded-2xl p-5 shadow-sm hover:shadow transition-all">
          <div class="flex items-start justify-between">
            <div class="w-full">
              <h3 class="text-sm font-medium text-gray-500">Gesamtkosten</h3>
              <p class="text-4xl font-bold text-indigo-600 mt-2">{{ formatCurrency(totalCosts) }}</p>
              <div class="mt-3 text-xs text-gray-500">
                <p>Wartung: {{ formatCurrency(maintenanceCosts) }}</p>
                <p>Laufende Kosten: {{ formatCurrency(quickAccessCosts) }}</p>
              </div>
            </div>
            <div class="text-indigo-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-9 w-9" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/></svg>
            </div>
          </div>
        </div>

        <!-- Wartungseinträge Card -->
        <div class="bg-white rounded-2xl p-5 shadow-sm hover:shadow transition-all">
          <div class="flex items-start justify-between">
            <div class="w-full">
              <h3 class="text-sm font-medium text-gray-500">Wartungseinträge</h3>
              <p class="text-4xl font-bold text-gray-800 mt-2">{{ vehicle.maintenance_logs.length }}</p>
              <div class="mt-3">
                <Link :href="route('vehicles.maintenance-logs.create', vehicle.id)" class="text-xs text-gray-600 font-medium hover:underline">Neue Wartung</Link>
              </div>
            </div>
            <div class="text-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-9 w-9" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
            </div>
          </div>
        </div>

        <!-- Kilometerstand Card -->
        <div class="bg-white rounded-2xl p-5 shadow-sm hover:shadow transition-all">
          <div class="flex items-start justify-between">
            <div class="w-full">
              <h3 class="text-sm font-medium text-gray-500">Kilometerstand</h3>
              <p class="text-4xl font-bold text-gray-800 mt-2">{{ vehicle.mileage.toLocaleString() }}</p>
              <div class="mt-3">
                <p class="text-xs text-gray-500">km</p>
              </div>
            </div>
            <div class="text-teal-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-9 w-9" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M18 7V4a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v3M6 7h12M9 7v12l-3-3M15 7v12l3-3M10 7h4M7 21l-3-3M20 21l-3-3"></path></svg>
            </div>
          </div>
        </div>

        <!-- Anstehende Services Card -->
        <div class="bg-white rounded-2xl p-5 shadow-sm hover:shadow transition-all">
          <div class="flex items-start justify-between">
            <div class="w-full">
              <h3 class="text-sm font-medium text-gray-500">Anstehende Services</h3>
              <p class="text-4xl font-bold text-gray-800 mt-2">{{ upcomingServices.length }}</p>
              <div class="mt-3">
                <Link :href="route('vehicles.service-reminders.create', vehicle.id)" class="text-xs text-amber-500 font-medium hover:underline">Neue Erinnerung</Link>
              </div>
            </div>
            <div class="text-amber-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-9 w-9" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Access Actions -->
      <div class="flex flex-wrap gap-3 mb-8">
        <button @click="openQuickEntryModal('refueling')" class="btn btn-primary btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 22h12"></path><path d="M19 22V9l-7-7H6a2 2 0 0 0-2 2v18M9 2v6h6"></path><path d="M17 16.5a3.5 3.5 0 1 0 0 7 3.5 3.5 0 1 0 0-7z"></path></svg>
          Tanken
        </button>
        <button @click="openQuickEntryModal('car_wash')" class="btn btn-info btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 7v9a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V7"></path><path d="M12 4h4l2 3H6l2-3h4z"></path><path d="M4 12h16"></path><line x1="15" y1="12" x2="15" y2="17"></line><line x1="9" y1="12" x2="9" y2="17"></line></svg>
          Autowäsche
        </button>
        <button @click="openQuickEntryModal('parking_fee')" class="btn btn-secondary btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M10 8v8"></path><path d="M14 8v8"></path><path d="M8 12h8"></path></svg>
          Parkgebühren
        </button>
        <Link :href="route('vehicles.maintenance-logs.create', vehicle.id)" class="btn btn-accent btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
          Wartung
        </Link>
        <Link :href="route('vehicles.service-reminders.create', vehicle.id)" class="btn btn-warning btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path></svg>
          Erinnerung
        </Link>
      </div>

      <!-- Main content grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Vehicle Details Card -->
        <div class="card bg-base-100 shadow-xl lg:col-span-2 overflow-hidden">
          <div class="card-body p-0">
            <div class="bg-primary text-primary-content p-4 flex justify-between items-center">
              <h2 class="text-xl font-bold">Fahrzeuginformationen</h2>
              <div class="flex rounded-lg overflow-hidden border-2 border-white/80">
                <button
                  @click="activeTab = 'fahrzeugdaten'"
                  class="px-4 py-2 text-sm transition-colors duration-200 font-medium"
                  :class="activeTab === 'fahrzeugdaten'
                    ? 'bg-white text-primary font-bold'
                    : 'bg-primary-focus/80 text-white hover:bg-primary-focus'"
                >
                  Fahrzeugdaten
                </button>
                <button
                  @click="activeTab = 'dokumente'"
                  class="px-4 py-2 text-sm transition-colors duration-200 font-medium"
                  :class="activeTab === 'dokumente'
                    ? 'bg-white text-primary font-bold'
                    : 'bg-primary-focus/80 text-white hover:bg-primary-focus'"
                >
                  Dokumente
                </button>
                <button
                  @click="activeTab = 'reifen_ersatzteile'"
                  class="px-4 py-2 text-sm transition-colors duration-200 font-medium"
                  :class="activeTab === 'reifen_ersatzteile'
                    ? 'bg-white text-primary font-bold'
                    : 'bg-primary-focus/80 text-white hover:bg-primary-focus'"
                >
                  Reifen & Ersatzteile
                </button>
              </div>
            </div>

            <!-- Fahrzeugdaten content -->
            <div v-if="activeTab === 'fahrzeugdaten'" class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Technische Daten -->
                <div class="md:col-span-1">
                  <h3 class="text-lg font-semibold mb-4 flex items-center text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line></svg>
                    Technische Daten
                  </h3>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Marke</span>
                      <span class="font-medium">{{ vehicle.make }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Modell</span>
                      <span class="font-medium">{{ vehicle.model }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Baujahr</span>
                      <span class="font-medium">{{ vehicle.year || "-" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Typ</span>
                      <span class="font-medium">{{ vehicle.type || "-" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Farbe</span>
                      <span class="font-medium">{{ vehicle.color || "-" }}</span>
                    </div>
                  </div>
                </div>

                <!-- Motor & Getriebe -->
                <div class="md:col-span-1">
                  <h3 class="text-lg font-semibold mb-4 flex items-center text-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
                    Motor & Getriebe
                  </h3>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Kraftstoffart</span>
                      <span class="font-medium">{{ getFuelTypeLabel(vehicle.fuel_type) }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Leistung</span>
                      <span class="font-medium">{{ vehicle.power ? vehicle.power + ' kW' : "-" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Hubraum</span>
                      <span class="font-medium">{{ vehicle.engine_size || "-" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Getriebe</span>
                      <span class="font-medium">{{ getTransmissionLabel(vehicle.transmission) }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Kilometerstand</span>
                      <span class="font-medium">{{ vehicle.mileage.toLocaleString() }} km</span>
                    </div>
                  </div>
                </div>

                <!-- Kauf & Identifikation -->
                <div class="md:col-span-1">
                  <h3 class="text-lg font-semibold mb-4 flex items-center text-accent">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2"></rect><path d="M7 15h0M2 9.5h20"></path></svg>
                    Kauf & Identifikation
                  </h3>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Kennzeichen</span>
                      <span class="font-medium badge badge-ghost">{{ vehicle.license_plate || "-" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Kaufdatum</span>
                      <span class="font-medium">{{ formatDate(vehicle.purchase_date) }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">Kaufpreis</span>
                      <span class="font-medium">{{ formatCurrency(vehicle.purchase_price) }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">HSN/TSN</span>
                      <span class="font-medium">{{ vehicle.hsn || "-" }}/{{ vehicle.tsn || "-" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500">VIN</span>
                      <span class="font-medium text-xs">{{ vehicle.vin || "-" }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="vehicle.notes" class="divider my-6"></div>

              <div v-if="vehicle.notes" class="bg-base-200 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Notizen</h3>
                <p class="whitespace-pre-line text-sm">{{ vehicle.notes }}</p>
              </div>
            </div>

            <!-- Dokumente content -->
            <div v-else-if="activeTab === 'dokumente'" class="p-6">
              <div v-if="vehicle.attachments && vehicle.attachments.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div v-for="attachment in vehicle.attachments" :key="attachment.id" class="card bg-base-200 hover:shadow-md transition-shadow">
                  <div class="card-body p-4">
                    <h3 class="card-title text-base flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline></svg>
                      {{ attachment.original_filename }}
                    </h3>
                    <p v-if="attachment.description" class="text-sm mt-2">{{ attachment.description }}</p>
                    <div class="card-actions justify-end mt-2">
                      <a :href="'/storage/' + attachment.file_path" target="_blank" class="btn btn-primary btn-sm">Öffnen</a>
                      <button @click="confirmDeleteAttachment(attachment.id)" class="btn btn-error btn-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="p-6 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300 mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                <p class="text-lg font-medium">Keine Dokumente</p>
                <p class="text-sm text-gray-500 mt-1">Fügen Sie Dokumente und Bilder zu diesem Fahrzeug hinzu</p>
                <button @click="triggerFileUpload" class="btn btn-primary mt-4" :class="{ 'loading': isUploading }" :disabled="isUploading">
                  <svg v-if="!isUploading" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                  {{ isUploading ? 'Wird hochgeladen...' : 'Dokumente hochladen' }}
                </button>
              </div>
            </div>

            <!-- Reifen & Ersatzteile content -->
            <div v-else-if="activeTab === 'reifen_ersatzteile'" class="p-6">
              <!-- Reifengrößen -->
              <div class="mb-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="3"></circle></svg>
                  Zugelassene Reifengrößen
                </h3>

                <div v-if="vehicle.tire_sizes && vehicle.tire_sizes.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-3">
                  <div 
                    v-for="(tireSize, index) in vehicle.tire_sizes" 
                    :key="index" 
                    class="badge badge-lg badge-outline p-4 flex flex-col items-start gap-1"
                    :class="{
                      'badge-primary': tireSize.position === 'both',
                      'badge-success': tireSize.position === 'front',
                      'badge-warning': tireSize.position === 'rear'
                    }"
                  >
                    <span class="text-sm font-semibold">{{ tireSize.size }}</span>
                    <div class="flex flex-col">
                      <span class="text-xs">
                        {{ getTirePositionLabel(tireSize.position) }}
                      </span>
                      <span v-if="tireSize.type" class="text-xs font-medium">
                        {{ getTireTypeLabel(tireSize.type) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div v-else class="p-4 bg-base-200 rounded-lg text-center">
                  <p class="text-gray-600">Keine Reifengrößen eingetragen</p>
                </div>
              </div>

              <!-- Ersatzteile & Verschleißteile -->
              <div>
                <h3 class="text-lg font-semibold mb-4 flex items-center text-secondary">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>
                  Ersatzteile & Verschleißteile
                </h3>
                
                <div v-if="vehicle.parts && vehicle.parts.length > 0" class="space-y-4">
                  <div v-for="(part, partIndex) in vehicle.parts" :key="partIndex" class="card bg-base-200 shadow-sm">
                    <div class="card-body p-4">
                      <h4 class="card-title text-base flex items-center justify-between">
                        <span>{{ part.name }}</span>
                        <span v-if="part.type" class="badge badge-sm py-1.5 px-3" :class="getPartTypeClass(part.type)">{{ getPartTypeLabel(part.type) }}</span>
                      </h4>
                      
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-2">
                        <div v-if="part.article_number" class="flex flex-col">
                          <span class="text-xs text-gray-500">Artikel-Nr.</span>
                          <span class="font-medium">{{ part.article_number }}</span>
                        </div>
                        <div v-if="part.oem_number" class="flex flex-col">
                          <span class="text-xs text-gray-500">OEM-Nr.</span>
                          <span class="font-medium">{{ part.oem_number }}</span>
                        </div>
                        <div v-if="part.manufacturer_number" class="flex flex-col">
                          <span class="text-xs text-gray-500">Hersteller-Nr.</span>
                          <span class="font-medium">{{ part.manufacturer_number }}</span>
                        </div>
                      </div>
                      
                      <div v-if="part.notes" class="mt-3 text-sm">
                        <span class="text-xs text-gray-500">Anmerkungen</span>
                        <p class="mt-1">{{ part.notes }}</p>
                      </div>
                      
                      <!-- Purchase Links -->
                      <div v-if="part.purchase_links && part.purchase_links.length > 0" class="mt-3">
                        <span class="text-xs text-gray-500">Bestelllinks</span>
                        <div class="flex flex-wrap gap-2 mt-1">
                          <a 
                            v-for="(link, linkIndex) in part.purchase_links" 
                            :key="linkIndex"
                            :href="link.url"
                            target="_blank"
                            rel="noopener noreferrer"
                            class="badge badge-outline badge-accent"
                          >
                            {{ link.label || link.url }}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="p-4 bg-base-200 rounded-lg text-center">
                  <p class="text-gray-600">Keine Ersatzteile eingetragen</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Service Reminders Card -->
        <div class="card bg-base-100 shadow-xl overflow-hidden">
          <div class="card-body p-0">
            <div class="bg-warning text-warning-content p-4 flex justify-between items-center">
              <h2 class="text-xl font-bold">Anstehende Services</h2>
              <Link :href="route('vehicles.service-reminders.create', vehicle.id)" class="btn btn-sm btn-circle btn-warning-content">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
              </Link>
            </div>

            <div v-if="!upcomingServices.length" class="p-6 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-success mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
              <p class="text-lg font-medium">Keine anstehenden Services</p>
              <p class="text-sm text-gray-500 mt-1">Ihr Fahrzeug ist bereit für die Straße!</p>
            </div>

            <div v-else class="divide-y divide-base-300">
              <div v-for="service in upcomingServices" :key="service.id" class="p-4 hover:bg-base-200 transition">
                <div class="flex justify-between items-start mb-2">
                  <div class="flex items-center gap-2">
                    <div class="badge badge-lg py-2 px-4" :class="getPriorityClass(service.priority)">
                      {{ service.priority === 'high' ? 'Hoch' : service.priority === 'medium' ? 'Mittel' : 'Niedrig' }}
                    </div>
                    <h3 class="font-medium">{{ service.title }}</h3>
                  </div>
                  <div class="flex gap-1">
                    <Link :href="route('service-reminders.edit', service.id)" class="btn btn-xs btn-ghost text-blue-600">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                    </Link>
                    <button @click="confirmDeleteReminder(service.id)" class="btn btn-xs btn-ghost text-red-600">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                    </button>
                  </div>
                </div>

                <div class="text-sm text-gray-500 flex flex-wrap gap-4">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                    {{ formatDate(service.due_date) || "Kein Datum" }}
                  </div>
                  <div v-if="service.due_mileage" class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M16.2 7.8l-2 6.3-6.4 2.1 2-6.3z"></path></svg>
                    {{ service.due_mileage.toLocaleString() }} km
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Access Tab -->
      <div class="mt-8">
        <QuickAccessTab
          :vehicleId="vehicle.id"
          :initialEntries="vehicle.quick_access_entries"
          :emptyStateTemplate="{
            icon: 'M3 22h12M19 22V9l-7-7H6a2 2 0 0 0-2 2v18M9 2v6h6M17 16.5a3.5 3.5 0 1 0 0 7 3.5 3.5 0 1 0 0-7z',
            title: 'Keine Einträge gefunden',
            subtitle: 'Erfassen Sie laufende Kosten wie Tanken, Autowäsche oder Parkgebühren'
          }"
          class="mb-6"
        />
      </div>

      <!-- Maintenance History -->
      <div class="card bg-base-100 shadow-xl mt-6">
        <div class="card-body p-0">
          <div class="p-4 bg-primary text-primary-content flex justify-between items-center">
            <h2 class="text-xl font-bold">Wartungshistorie</h2>
            <Link :href="route('vehicles.maintenance-logs.create', vehicle.id)" class="btn btn-sm btn-circle btn-primary-content">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            </Link>
          </div>

          <div v-if="!vehicle.maintenance_logs.length" class="p-6 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-primary/30 mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
            <p class="text-lg font-medium">Noch keine Wartungseinträge</p>
            <p class="text-sm text-gray-500 mt-1">Erfassen Sie Ihre erste Wartung, um die Historie zu beginnen</p>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th class="py-3">Titel</th>
                  <th class="py-3">Datum</th>
                  <th class="py-3">Typ</th>
                  <th class="py-3">km-Stand</th>
                  <th class="py-3">Kosten</th>
                  <th class="py-3"></th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="log in vehicle.maintenance_logs" :key="log.id" class="hover">
                  <td class="py-3 font-medium">{{ log.title }}</td>
                  <td class="py-3">{{ formatDate(log.date) }}</td>
                  <td class="py-3">
                    <div class="badge px-3 py-2" :class="getMaintenanceTypeClass(log.type)">
                      {{ typeLabels[log.type] || log.type }}
                    </div>
                  </td>
                  <td class="py-3">{{ log.mileage ? log.mileage.toLocaleString() + ' km' : '-' }}</td>
                  <td class="py-3 font-medium">{{ formatCurrency(calculateTotalCost(log)) }}</td>
                  <td class="py-3">
                    <Link :href="route('maintenance-logs.show', log.id)" class="btn btn-sm bg-primary/10 hover:bg-primary/20 text-primary border-none hover:shadow transition-all flex items-center gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                      Details
                    </Link>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Dokumente & Bilder -->
      <div v-if="vehicle.attachments.length > 0" class="card bg-base-100 shadow-xl mt-6">
        <div class="card-body p-0">
          <div class="p-4 bg-secondary text-secondary-content flex justify-between items-center">
            <h2 class="text-xl font-bold">Dokumente & Bilder</h2>
            <div class="flex items-center gap-2">
              <!-- Filter Controls -->
              <div class="join bg-black/30 rounded-lg overflow-hidden">
                <button
                  @click="documentFilter = 'all'"
                  class="join-item btn btn-sm py-2 px-3 border-0 font-medium transition-colors"
                  :class="documentFilter === 'all'
                    ? 'bg-white text-secondary'
                    : 'bg-transparent text-white hover:bg-white/10'"
                >
                  Alle
                </button>
                <button
                  @click="documentFilter = 'images'"
                  class="join-item btn btn-sm py-2 px-3 border-0 font-medium transition-colors"
                  :class="documentFilter === 'images'
                    ? 'bg-white text-secondary'
                    : 'bg-transparent text-white hover:bg-white/10'"
                >
                  Bilder
                </button>
                <button
                  @click="documentFilter = 'documents'"
                  class="join-item btn btn-sm py-2 px-3 border-0 font-medium transition-colors"
                  :class="documentFilter === 'documents'
                    ? 'bg-white text-secondary'
                    : 'bg-transparent text-white hover:bg-white/10'"
                >
                  Dokumente
                </button>
              </div>
              <button @click="triggerFileUpload" class="btn btn-sm btn-circle btn-secondary-content" :class="{ 'loading': isUploading }" :disabled="isUploading">
                <svg v-if="!isUploading" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
              </button>
            </div>
          </div>

          <div class="p-6">
            <!-- Empty State -->
            <div v-if="displayedAttachments.length === 0" class="text-center py-10">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300 mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
              <p class="text-lg font-medium">Keine {{ documentFilter === 'images' ? 'Bilder' : documentFilter === 'documents' ? 'Dokumente' : 'Dateien' }} gefunden</p>
              <p class="text-sm text-gray-500 mt-1">Fügen Sie {{ documentFilter === 'images' ? 'Bilder' : documentFilter === 'documents' ? 'Dokumente' : 'Dateien' }} zu diesem Fahrzeug hinzu</p>
            </div>

            <!-- Document Grid -->
            <div v-else class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <div
                v-for="(attachment, index) in displayedAttachments"
                :key="attachment.id"
                @click="(isImage(attachment.mime_type) || isPdf(attachment.mime_type)) ? openImageViewer(filteredAttachments.findIndex(a => a.id === attachment.id)) : null"
                class="group relative rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 aspect-square cursor-pointer"
              >
                <!-- Image Files -->
                <img
                  v-if="isImage(attachment.mime_type)"
                  :src="getImagePath(attachment.file_path)"
                  :alt="attachment.original_filename"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />

                <!-- PDF/Document Files -->
                <div
                  v-else
                  class="w-full h-full flex flex-col items-center justify-center bg-base-200 p-4 group-hover:bg-base-300 transition-colors duration-300"
                >
                  <!-- PDF Icon -->
                  <svg v-if="isPdf(attachment.mime_type)" xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-red-500 mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <path d="M9 15L15 15"></path>
                    <path d="M9 11L15 11"></path>
                  </svg>

                  <!-- Other Document Icon -->
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-500 mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>

                  <span class="text-xs font-medium text-center line-clamp-2 mt-1">{{ attachment.original_filename }}</span>
                </div>

                <!-- Document Type Badge -->
                <div class="absolute top-2 left-2 z-10">
                  <span v-if="isImage(attachment.mime_type)" class="badge badge-xs badge-accent text-xs py-1 px-2">Bild</span>
                  <span v-else-if="isPdf(attachment.mime_type)" class="badge badge-xs badge-error text-xs py-1 px-2">PDF</span>
                  <span v-else class="badge badge-xs badge-info text-xs py-1 px-2">Dokument</span>
                </div>

                <!-- Overlay -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-3">
                  <h3 class="text-sm font-medium text-white truncate">{{ attachment.original_filename }}</h3>

                  <div class="flex gap-2 mt-2">
                    <a
                      :href="`/storage/${attachment.file_path}`"
                      target="_blank"
                      class="btn btn-xs btn-primary p-1 h-8 w-8 rounded-full"
                      @click.stop
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                    </a>
                    <button
                      @click.stop="confirmDeleteAttachment(attachment.id)"
                      class="btn btn-xs btn-error p-1 h-8 w-8 rounded-full"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Image Viewer Modal -->
      <div v-if="showImageViewer" class="fixed inset-0 z-50 bg-black/90 flex items-center justify-center backdrop-blur-sm">
        <div class="relative w-full h-full flex flex-col items-center justify-center">
          <!-- Close button -->
          <button
            @click="closeImageViewer"
            class="absolute top-4 right-4 z-50 btn btn-circle btn-sm bg-white/20 hover:bg-white/40 border-0 text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>

          <!-- Navigation arrows -->
          <button
            @click="prevImage"
            class="absolute left-4 top-1/2 -translate-y-1/2 z-50 btn btn-circle bg-white/20 hover:bg-white/40 border-0 text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>

          <button
            @click="nextImage"
            class="absolute right-4 top-1/2 -translate-y-1/2 z-50 btn btn-circle bg-white/20 hover:bg-white/40 border-0 text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>

          <!-- Media display -->
          <div class="w-full h-full max-w-5xl max-h-[80vh] flex items-center justify-center px-16">
            <!-- Image preview -->
            <img
              v-if="filteredAttachments[currentImageIndex] && isImage(filteredAttachments[currentImageIndex].mime_type)"
              :src="getImagePath(filteredAttachments[currentImageIndex].file_path)"
              :alt="filteredAttachments[currentImageIndex].original_filename"
              class="max-w-full max-h-full object-contain shadow-2xl"
            />

            <!-- PDF preview -->
            <iframe
              v-else-if="filteredAttachments[currentImageIndex] && isPdf(filteredAttachments[currentImageIndex].mime_type)"
              :src="getImagePath(filteredAttachments[currentImageIndex].file_path)"
              class="w-full h-full bg-white rounded-lg shadow-2xl"
              frameborder="0"
            ></iframe>

            <!-- Other file types -->
            <div
              v-else
              class="w-full max-w-md p-8 bg-white rounded-xl shadow-2xl text-center flex flex-col items-center justify-center"
            >
              <!-- Document Icon -->
              <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-blue-500 mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>

              <h3 class="text-xl font-semibold text-gray-800 mb-3">
                {{ filteredAttachments[currentImageIndex]?.original_filename }}
              </h3>
              <p class="text-gray-600 mb-4">Diese Datei kann nicht direkt angezeigt werden.</p>
              <a
                :href="getImagePath(filteredAttachments[currentImageIndex]?.file_path)"
                download
                class="btn btn-primary gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                  <polyline points="15 3 21 3 21 9"></polyline>
                  <line x1="10" y1="14" x2="21" y2="3"></line>
                </svg>
                In neuem Tab öffnen
              </a>
            </div>
          </div>

          <!-- Image info -->
          <div class="absolute bottom-6 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent py-4 text-center text-white px-4">
            <h3 class="text-lg font-medium">
              {{ filteredAttachments[currentImageIndex]?.original_filename }}
            </h3>
            <div class="flex items-center justify-center gap-4 mt-2">
              <span>{{ currentImageIndex + 1 }} / {{ filteredAttachments.length }}</span>
              <a
                :href="getImagePath(filteredAttachments[currentImageIndex]?.file_path)"
                download
                class="btn btn-sm btn-primary gap-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Download
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
