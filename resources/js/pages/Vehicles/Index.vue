<script setup lang="ts">
import { Link, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import axios from 'axios';
import { useToast } from '@/components/ui/toast';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  vehicles: Array<{
    id: number;
    make: string;
    model: string;
    year: number;
    license_plate: string;
    mileage: number;
    image: string | null;
    is_active: boolean;
    maintenance_logs_count: number;
    service_reminders: Array<{ id: number; status: string }>;
  }>;
}>();

const search = ref('');

const filteredVehicles = computed(() => {
  if (!search.value) return props.vehicles;

  const searchTerm = search.value.toLowerCase();
  return props.vehicles.filter(vehicle => {
    return vehicle.make.toLowerCase().includes(searchTerm) ||
           vehicle.model.toLowerCase().includes(searchTerm) ||
           vehicle.license_plate?.toLowerCase().includes(searchTerm) ||
           `${vehicle.make} ${vehicle.model}`.toLowerCase().includes(searchTerm);
  });
});

const hasOverdueReminders = (vehicle: typeof props.vehicles[0]) => {
  return vehicle.service_reminders.some(reminder => reminder.status === 'overdue');
};

const hasPendingReminders = (vehicle: typeof props.vehicles[0]) => {
  return vehicle.service_reminders.some(reminder => reminder.status === 'pending');
};

// Schnellzugriffs-Dialog
const showQuickEntryDialog = ref(false);
const quickEntryType = ref<'refueling' | 'car_wash' | 'parking_fee'>('refueling');
const quickEntryTitle = ref('');
const selectedVehicleId = ref<number | null>(null);

const quickEntryForm = ref({
  date: new Date().toISOString().split('T')[0], // Heutiges Datum als Standardwert
  mileage: null,
  cost: null as number | null,
  description: ''
});

const typeLabelsQuickEntry: Record<'refueling' | 'car_wash' | 'parking_fee', string> = {
  'refueling': 'Tanken',
  'car_wash': 'Autowäsche',
  'parking_fee': 'Parkgebühren'
};

const createQuickEntry = (vehicleId: number, type: 'refueling' | 'car_wash' | 'parking_fee', vehicleMileage: number) => {
  selectedVehicleId.value = vehicleId;
  quickEntryType.value = type;
  quickEntryTitle.value = typeLabelsQuickEntry[type] + ' hinzufügen';
  quickEntryForm.value = {
    date: new Date().toISOString().split('T')[0],
    mileage: null,
    cost: null,
    description: ''
  };
  showQuickEntryDialog.value = true;
};

const cancelQuickEntry = () => {
  showQuickEntryDialog.value = false;
  selectedVehicleId.value = null;
};

// System-Dialog Variablen
const showSuccessMessage = ref(false);

// Toast Notification
const showToast = ref(false);
const toastMessage = ref('');

const { toast } = useToast();

const submitQuickEntry = async () => {
  if (!selectedVehicleId.value) return;

  try {
    // Formular zur Übermittlung vorbereiten
    const formData = {
      type: quickEntryType.value,
      date: quickEntryForm.value.date,
      mileage: quickEntryForm.value.mileage || null, // Ensure null if empty
      amount: quickEntryForm.value.cost,
      notes: quickEntryForm.value.description,
      location: '',
      quantity: null,
      price_per_unit: null
    };

    // API-Anfrage senden - use the quick access endpoint instead
    await axios.post(`/api/vehicles/${selectedVehicleId.value}/quick-access-entries`, formData);

    // Dialog schließen
    showQuickEntryDialog.value = false;

    // Toast-Benachrichtigung anzeigen
    toastMessage.value = 'Eintrag wurde erfolgreich gespeichert!';
    showToast.value = true;

    // Nach 3 Sekunden Toast ausblenden und Seite neu laden
    setTimeout(() => {
      showToast.value = false;
      window.location.reload();
    }, 3000);
  } catch (error: any) {
    console.error('Fehler beim Speichern:', error);
    alert('Fehler beim Speichern: ' + (error.response?.data?.message || 'Unbekannter Fehler'));
  }
};

const closeSuccessMessage = () => {
  showSuccessMessage.value = false;
  router.reload();
};

const getVehicleImagePath = (imagePath: string | null): string | undefined => {
  if (!imagePath) return undefined;

  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  if (imagePath.startsWith('storage/')) {
    return `/${imagePath}`;
  }

  return `/storage/${imagePath}`;
};
</script>

<template>
  <div class="p-6 md:p-8 bg-gray-100">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Meine Fahrzeuge</h1>
      <div class="flex space-x-3">
        <Link :href="route('vehicles.trash')" class="btn btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Papierkorb
        </Link>
        <Link :href="route('vehicles.create')" class="btn btn-primary">
          Fahrzeug hinzufügen
        </Link>
      </div>
    </div>

    <div class="mb-6">
      <input
        v-model="search"
        type="text"
        placeholder="Fahrzeug suchen..."
        class="input input-bordered w-full max-w-md"
      />
    </div>

    <!-- Toast-Benachrichtigung -->
    <transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div v-if="showToast" class="fixed top-4 right-4 z-50 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md max-w-md">
        <div class="flex items-center">
          <svg class="h-6 w-6 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <p>{{ toastMessage }}</p>
        </div>
      </div>
    </transition>

    <div v-if="!filteredVehicles.length" class="alert alert-info">
      <div>
        <span v-if="!vehicles.length">Sie haben noch keine Fahrzeuge angelegt. Fügen Sie Ihr erstes Fahrzeug hinzu.</span>
        <span v-else>Keine Fahrzeuge gefunden, die Ihren Suchkriterien entsprechen.</span>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="vehicle in filteredVehicles"
        :key="vehicle.id"
        class="card bg-base-100 shadow-xl h-full"
        :class="{ 'opacity-70': !vehicle.is_active }"
      >
        <figure v-if="vehicle.image">
          <img :src="getVehicleImagePath(vehicle.image)" :alt="`${vehicle.make} ${vehicle.model}`" class="h-48 w-full object-cover" />
        </figure>
        <figure v-else class="bg-gray-200 h-48 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
            <path d="M20 8v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V8" />
            <path d="M18 5H6a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2z" />
            <path d="M4 10h16" />
            <circle cx="9" cy="15" r="1" />
            <circle cx="15" cy="15" r="1" />
          </svg>
        </figure>
        <div class="card-body">
          <div class="flex justify-between">
            <h2 class="card-title">{{ vehicle.make }} {{ vehicle.model }}</h2>
            <div class="badge badge-outline" :class="{ 'badge-success': vehicle.is_active, 'badge-error': !vehicle.is_active }">
              {{ vehicle.is_active ? 'Aktiv' : 'Inaktiv' }}
            </div>
          </div>
          <p v-if="vehicle.year">Baujahr: {{ vehicle.year }}</p>
          <p v-if="vehicle.license_plate">Kennzeichen: {{ vehicle.license_plate }}</p>
          <p>Kilometerstand: {{ vehicle.mileage.toLocaleString() }} km</p>
          <div class="flex flex-wrap gap-2 mt-2">
            <div v-if="hasOverdueReminders(vehicle)" class="badge badge-error badge-lg py-3 px-3 whitespace-nowrap">Überfällige Termine</div>
            <div v-else-if="hasPendingReminders(vehicle)" class="badge badge-warning badge-lg py-3 px-3 whitespace-nowrap">Anstehende Termine</div>
            <div class="badge badge-secondary badge-lg py-3 px-3 whitespace-nowrap">{{ vehicle.maintenance_logs_count }} Einträge</div>
          </div>

          <!-- Schnellzugriffs-Icons -->
          <div class="flex gap-2 mt-3 mb-2">
            <button
              @click="createQuickEntry(vehicle.id, 'refueling', vehicle.mileage)"
              class="btn btn-sm btn-circle btn-outline"
              title="Tanken"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 22h12"></path><path d="M19 22V9l-3-3v4"></path><path d="M8 22V9"></path><path d="m12 6 4 3"></path><path d="M5 6v4"></path><path d="M3 2h18"></path><path d="M19 6a4 4 0 0 0-4-4"></path></svg>
            </button>
            <button
              @click="createQuickEntry(vehicle.id, 'car_wash', vehicle.mileage)"
              class="btn btn-sm btn-circle btn-outline"
              title="Autowäsche"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6M5 6V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2M3 6h18M10 14l2 2 4-4"></path></svg>
            </button>
            <button
              @click="createQuickEntry(vehicle.id, 'parking_fee', vehicle.mileage)"
              class="btn btn-sm btn-circle btn-outline"
              title="Parkgebühren"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2H5z"/><path d="M9 14V9h4a2 2 0 0 1 0 4H9"/></svg>
            </button>
            <Link
              :href="route('vehicles.maintenance-logs.create', vehicle.id)"
              class="btn btn-sm btn-primary btn-circle"
              title="Alle Optionen"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            </Link>
          </div>

          <div class="card-actions justify-end mt-2">
            <Link :href="route('vehicles.show', vehicle.id)" class="btn btn-primary btn-sm">
              Details
            </Link>
            <Link :href="route('vehicles.edit', vehicle.id)" class="btn btn-outline btn-sm">
              Bearbeiten
            </Link>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Schnellzugriff Modal -->
  <div v-if="showQuickEntryDialog" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
      <h3 class="text-2xl font-semibold mb-6">{{ quickEntryTitle }}</h3>

      <form @submit.prevent="submitQuickEntry" class="space-y-6">
        <div class="mb-6">
          <div class="text-base font-medium text-gray-700 mb-2">Datum</div>
          <input
            type="date"
            v-model="quickEntryForm.date"
            class="w-full h-12 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <div class="mb-6">
          <div class="text-base font-medium text-gray-700 mb-2">Kilometerstand</div>
          <input
            type="number"
            v-model="quickEntryForm.mileage"
            class="w-full h-12 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="Kilometerstand (optional)"
          />
        </div>

        <div class="mb-6">
          <div class="text-base font-medium text-gray-700 mb-2">Kosten (€)</div>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <span class="text-gray-500">€</span>
            </div>
            <input
              type="number"
              v-model="quickEntryForm.cost"
              class="w-full h-12 pl-8 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="0.00"
              step="0.01"
              required
            />
          </div>
        </div>

        <div class="mb-6">
          <div class="text-base font-medium text-gray-700 mb-2">Beschreibung (optional)</div>
          <textarea
            v-model="quickEntryForm.description"
            class="w-full h-24 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="Beschreibung"
          ></textarea>
        </div>

        <div class="flex justify-end space-x-3 mt-8">
          <button type="button" class="px-6 py-2 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50" @click="cancelQuickEntry">Abbrechen</button>
          <button type="submit" class="px-6 py-2 rounded-lg bg-indigo-600 text-white font-medium hover:bg-indigo-700">Speichern</button>
        </div>
      </form>
    </div>
  </div>
</template>
