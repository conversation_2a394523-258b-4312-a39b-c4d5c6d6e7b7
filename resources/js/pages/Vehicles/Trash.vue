<script setup lang="ts">
import { ref, computed } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import dayjs from 'dayjs';
import 'dayjs/locale/de';

defineOptions({
  layout: AppSidebarLayout,
});

// Definiere die Fahrzeug-Schnittstelle
interface Vehicle {
  id: number;
  user_id: number;
  make: string;
  model: string;
  type: string | null;
  license_plate: string | null;
  year: number | null;
  mileage: number | null;
  image: string | null;
  deleted_at: string;
  created_at: string;
  updated_at: string;
}

const props = defineProps<{
  trashedVehicles: Vehicle[];
}>();

// Berechnung der verbleibenden Tage bis zur endgültigen Löschung
const getRemainingDays = (deletedAt: string) => {
  const deleteDate = dayjs(deletedAt);
  const expirationDate = deleteDate.add(30, 'day');
  const today = dayjs();
  const remainingDays = expirationDate.diff(today, 'day');
  return Math.max(0, remainingDays);
};

// Verbleibende Zeit formatieren
const formatRemainingTime = (days: number) => {
  if (days <= 0) {
    return 'Wird bald endgültig gelöscht';
  } else if (days === 1) {
    return '1 Tag verbleibend';
  } else {
    return `${days} Tage verbleibend`;
  }
};

// Formatiere das Datum
const formatDate = (dateString: string) => {
  return dayjs(dateString).locale('de').format('DD.MM.YYYY HH:mm');
};

// Modal-States
const showRestoreModal = ref(false);
const showDeleteModal = ref(false);
const selectedVehicleId = ref<number | null>(null);
const selectedVehicle = ref<Vehicle | null>(null);

// Fahrzeug wiederherstellen Modal öffnen
const openRestoreModal = (vehicle: Vehicle) => {
  selectedVehicleId.value = vehicle.id;
  selectedVehicle.value = vehicle;
  showRestoreModal.value = true;
};

// Fahrzeug endgültig löschen Modal öffnen
const openDeleteModal = (vehicle: Vehicle) => {
  selectedVehicleId.value = vehicle.id;
  selectedVehicle.value = vehicle;
  showDeleteModal.value = true;
};

// Modals schließen
const closeModals = () => {
  showRestoreModal.value = false;
  showDeleteModal.value = false;
  selectedVehicleId.value = null;
  selectedVehicle.value = null;
};

// Fahrzeug wiederherstellen
const restoreVehicle = () => {
  if (selectedVehicleId.value) {
    router.post(route('vehicles.restore', selectedVehicleId.value));
    closeModals();
  }
};

// Fahrzeug endgültig löschen
const forceDeleteVehicle = () => {
  if (selectedVehicleId.value) {
    router.delete(route('vehicles.force-delete', selectedVehicleId.value));
    closeModals();
  }
};
</script>

<template>
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Papierkorb</h1>
        <p class="text-gray-600 mt-1">
          Hier finden Sie Ihre gelöschten Fahrzeuge. Gelöschte Fahrzeuge werden nach 30 Tagen automatisch endgültig entfernt.
        </p>
      </div>
      <Link
        :href="route('vehicles.index')"
        class="btn btn-primary"
      >
        Zurück zur Fahrzeugübersicht
      </Link>
    </div>

    <!-- Keine gelöschten Fahrzeuge -->
    <div v-if="trashedVehicles.length === 0" class="bg-white shadow rounded-lg p-8 text-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Keine gelöschten Fahrzeuge</h3>
      <p class="text-gray-500 mb-4">
        Ihr Papierkorb ist leer. Gelöschte Fahrzeuge würden hier für 30 Tage angezeigt werden.
      </p>
      <Link :href="route('vehicles.index')" class="btn btn-primary">
        Zurück zur Fahrzeugübersicht
      </Link>
    </div>

    <!-- Tabelle mit gelöschten Fahrzeugen -->
    <div v-else class="bg-white shadow rounded-lg overflow-hidden">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Fahrzeug
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Kennzeichen
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Gelöscht am
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Verbleibende Zeit
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Aktionen
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="vehicle in trashedVehicles" :key="vehicle.id">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div v-if="vehicle.image" class="flex-shrink-0 h-10 w-10 mr-3">
                  <img class="h-10 w-10 rounded-full object-cover" :src="`/storage/${vehicle.image}`" :alt="`${vehicle.make} ${vehicle.model}`">
                </div>
                <div v-else class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ vehicle.make }} {{ vehicle.model }}
                  </div>
                  <div v-if="vehicle.year" class="text-sm text-gray-500">
                    {{ vehicle.year }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ vehicle.license_plate || '-' }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ formatDate(vehicle.deleted_at) }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span 
                class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                :class="{
                  'bg-red-100 text-red-800': getRemainingDays(vehicle.deleted_at) <= 3,
                  'bg-yellow-100 text-yellow-800': getRemainingDays(vehicle.deleted_at) > 3 && getRemainingDays(vehicle.deleted_at) <= 10,
                  'bg-green-100 text-green-800': getRemainingDays(vehicle.deleted_at) > 10
                }"
              >
                {{ formatRemainingTime(getRemainingDays(vehicle.deleted_at)) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button 
                @click="openRestoreModal(vehicle)"
                class="text-indigo-600 hover:text-indigo-900 mr-4"
              >
                Wiederherstellen
              </button>
              <button 
                @click="openDeleteModal(vehicle)"
                class="text-red-600 hover:text-red-900"
              >
                Endgültig löschen
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Wiederherstellungs-Modal -->
  <div v-if="showRestoreModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
      <h3 class="text-lg font-semibold mb-4">Fahrzeug wiederherstellen</h3>
      <p class="mb-4">
        Möchten Sie das Fahrzeug "{{ selectedVehicle?.make }} {{ selectedVehicle?.model }}" wiederherstellen?
      </p>
      <div class="flex justify-end space-x-3">
        <button 
          @click="closeModals"
          class="px-4 py-2 rounded bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
        >
          Abbrechen
        </button>
        <button 
          @click="restoreVehicle"
          class="px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700 transition-colors"
        >
          Wiederherstellen
        </button>
      </div>
    </div>
  </div>

  <!-- Endgültig löschen Modal -->
  <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
      <h3 class="text-lg font-semibold mb-4 text-red-600">Fahrzeug endgültig löschen</h3>
      <p class="mb-4">
        <strong>ACHTUNG:</strong> Sie sind dabei, das Fahrzeug "{{ selectedVehicle?.make }} {{ selectedVehicle?.model }}" endgültig zu löschen. 
        Diese Aktion kann nicht rückgängig gemacht werden.
      </p>
      <div class="flex justify-end space-x-3">
        <button 
          @click="closeModals"
          class="px-4 py-2 rounded bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
        >
          Abbrechen
        </button>
        <button 
          @click="forceDeleteVehicle"
          class="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 transition-colors"
        >
          Endgültig löschen
        </button>
      </div>
    </div>
  </div>
</template> 