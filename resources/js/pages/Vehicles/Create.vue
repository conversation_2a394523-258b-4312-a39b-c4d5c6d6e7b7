<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { ref, watch, onMounted } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import axios from 'axios';

defineOptions({
  layout: AppSidebarLayout,
});

const form = useForm({
  make: '',
  model: '',
  type: '',
  hsn: '',
  tsn: '',
  vin: '',
  license_plate: '',
  year: null as number | null,
  mileage: 0,
  annual_mileage: null as number | null,
  purchase_date: null as string | null,
  purchase_price: null as number | null,
  notes: '',
  color: '',
  fuel_type: '',
  power: null as number | null,
  engine_size: '',
  transmission: '',
  image: null as File | null,
  tire_sizes: [] as Array<{
    size: string;
    position: 'front' | 'rear' | 'both';
    type: 'summer' | 'winter' | 'all_season';
  }>,
  parts: [] as Array<{
    name: string;
    type: string;
    article_number: string | null;
    oem_number: string | null;
    manufacturer_number: string | null;
    notes: string | null;
    purchase_links: Array<{
      label: string;
      url: string;
    }>;
    custom_type?: string;
  }>,
});

const fuelTypes = [
  { value: 'petrol', label: 'Benzin' },
  { value: 'diesel', label: 'Diesel' },
  { value: 'electric', label: 'Elektro' },
  { value: 'hybrid', label: 'Hybrid' },
  { value: 'plugin_hybrid', label: 'Plug-in-Hybrid' },
  { value: 'lpg', label: 'Autogas (LPG)' },
  { value: 'cng', label: 'Erdgas (CNG)' },
  { value: 'hydrogen', label: 'Wasserstoff' },
  { value: 'other', label: 'Sonstiges' },
];

const transmissionTypes = [
  { value: 'manual', label: 'Manuell' },
  { value: 'automatic', label: 'Automatik' },
  { value: 'semi_automatic', label: 'Halbautomatik' },
  { value: 'cvt', label: 'Stufenlos (CVT)' },
  { value: 'dual_clutch', label: 'Doppelkupplung (DSG)' },
];

// Add tire types
const tireTypes = [
  { value: 'summer', label: 'Sommerreifen' },
  { value: 'winter', label: 'Winterreifen' },
  { value: 'all_season', label: 'Ganzjahresreifen' }
];

const imagePreview = ref<string | null>(null);

// HSN/TSN Autovervollständigung
const isLoadingVehicleData = ref(false);
const vehicleSuggestions = ref<Array<{
  hsn: string;
  tsn: string;
  make: string;
  model: string;
  type: string;
  fuel_type: string;
  power: number;
  engine_size: string;
  year_from: number;
  year_to: number | null;
}>>([]);

// VIN-Decoder
const isLoadingVinData = ref(false);
const vinSuggestions = ref<Array<{
  vin: string;
  hsn: string | null;
  tsn: string | null;
  make: string;
  model: string;
  type: string;
  fuel_type: string;
  power: number | null;
  engine_size: string;
  year: number | null;
}>>([]);
const vinError = ref<string | null>(null);

// Funktion zur Validierung einer VIN
function isValidVin(vin: string): boolean {
  // Grundlegende Validierung: Länge 17, keine Sonderzeichen außer Buchstaben und Zahlen
  // Ausschließen von Zeichen, die in VINs nicht erlaubt sind: I, O, Q
  if (!vin || vin.length !== 17) return false;

  // Prüfen auf ungültige Zeichen (nur A-Z und 0-9 erlaubt, ohne I, O, Q)
  const vinRegex = /^[A-HJ-NPR-Z0-9]{17}$/;
  if (!vinRegex.test(vin)) return false;

  // Prüfen, ob die VIN mit http oder https beginnt (es wurde versehentlich eine URL eingegeben)
  if (vin.toLowerCase().startsWith('http')) return false;

  return true;
}

// API-Funktion für HSN/TSN-Suche
async function searchVehicleByHsnTsn() {
  if (!form.hsn || !form.tsn || form.hsn.length < 4) return;

  isLoadingVehicleData.value = true;
  try {
    // Direkt den Fallback-Endpunkt verwenden, da der API-Endpunkt nicht funktioniert
    const response = await axios.get('/api-fallback/vehicle-data', {
      params: {
        hsn: form.hsn,
        tsn: form.tsn
      }
    });

    if (response.data && response.data.length > 0) {
      vehicleSuggestions.value = response.data;
    } else {
      vehicleSuggestions.value = [];
    }
  } catch (error) {
    console.error('Fehler beim Abrufen der Fahrzeugdaten:', error);
    vehicleSuggestions.value = [];
  } finally {
    isLoadingVehicleData.value = false;
  }
}

// API-Funktion für VIN-Suche
async function searchVehicleByVin() {
  vinError.value = null;

  if (!form.vin || form.vin.length < 17) {
    vinError.value = "VIN muss 17 Zeichen lang sein";
    return;
  }

  // VIN-Validierung
  if (!isValidVin(form.vin)) {
    vinError.value = "Ungültige VIN. Bitte überprüfen Sie die Eingabe.";
    return;
  }

  isLoadingVinData.value = true;
  try {
    // Direkt den Fallback-Endpunkt verwenden, da der API-Endpunkt nicht funktioniert
    const response = await axios.get('/api-fallback/vehicle-data/vin', {
      params: {
        vin: form.vin
      }
    });

    if (response.data && response.data.length > 0) {
      vinSuggestions.value = response.data;
    } else {
      vinSuggestions.value = [];
      vinError.value = "Keine Fahrzeugdaten zu dieser VIN gefunden";
    }
  } catch (error) {
    console.error('Fehler beim Abrufen der Fahrzeugdaten via VIN:', error);
    vinSuggestions.value = [];
    vinError.value = "Fehler beim Abrufen der Fahrzeugdaten";
  } finally {
    isLoadingVinData.value = false;
  }
}

// Beobachten Sie Änderungen bei HSN/TSN für automatische Suche
watch(() => [form.hsn, form.tsn], ([newHsn, newTsn], [oldHsn, oldTsn]) => {
  if ((newHsn && newHsn !== oldHsn && newHsn.length >= 4) ||
      (newTsn && newTsn !== oldTsn && newTsn.length >= 3 && form.hsn.length >= 4)) {
    searchVehicleByHsnTsn();
  }
}, { immediate: false });

// Beobachten Sie Änderungen bei VIN für automatische Suche
watch(() => form.vin, (newVin, oldVin) => {
  if (newVin && newVin !== oldVin && newVin.length >= 17) {
    searchVehicleByVin();
  }

  // Wenn VIN geleert wird, leeren wir auch die Vorschläge
  if (!newVin || newVin.length < 17) {
    vinSuggestions.value = [];
  }
}, { immediate: false });

// Funktion zum Anwenden eines Fahrzeugvorschlags (HSN/TSN)
function applyVehicleSuggestion(suggestion: any) {
  form.make = suggestion.make;
  form.model = suggestion.model;
  form.type = suggestion.type;
  form.fuel_type = suggestion.fuel_type;
  form.power = suggestion.power;
  form.engine_size = suggestion.engine_size;

  // Wenn das Fahrzeug einen Bereich von Jahren hat, setzen wir das neueste Jahr
  if (suggestion.year_to) {
    form.year = suggestion.year_to;
  } else {
    form.year = suggestion.year_from;
  }

  vehicleSuggestions.value = [];
}

// Funktion zum Anwenden eines VIN-Vorschlags
function applyVinSuggestion(suggestion: any) {
  form.make = suggestion.make;
  form.model = suggestion.model;
  form.type = suggestion.type;
  form.fuel_type = suggestion.fuel_type;
  form.power = suggestion.power;
  form.engine_size = suggestion.engine_size;
  form.year = suggestion.year;

  // Wenn HSN/TSN verfügbar sind, auch diese setzen
  if (suggestion.hsn) form.hsn = suggestion.hsn;
  if (suggestion.tsn) form.tsn = suggestion.tsn;

  vinSuggestions.value = [];
}

function handleImageUpload(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0] || null;
  form.image = file;

  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  } else {
    imagePreview.value = null;
  }
}

function submit() {
  // Sicherstellen, dass engine_size als String gespeichert wird
  if (form.engine_size !== null && form.engine_size !== undefined && typeof form.engine_size !== 'string') {
    form.engine_size = String(form.engine_size);
  }

  // Process custom part types before submission
  form.parts.forEach(part => {
    if (part.type === 'other' && part.custom_type) {
      part.type = 'custom:' + part.custom_type;
    }
  });

  form.post(route('vehicles.store'), {
    preserveScroll: true,
    onSuccess: () => {
      form.reset();
      imagePreview.value = null;
      vehicleSuggestions.value = [];
    },
  });
}

// Wird aufgerufen, wenn der Nutzer HSN/TSN manuell eingibt
onMounted(() => {
  // Parse custom part types in case they're present in the initial form data
  form.parts.forEach(part => {
    if (part.type && part.type.startsWith('custom:')) {
      part.custom_type = part.type.substring(7); // Extract the custom value
      part.type = 'other'; // Set back to 'other' for the dropdown
    }
  });
});
</script>

<template>
  <div class="p-4 md:p-6 bg-gray-50">
    <!-- Header mit Stil -->
    <div class="mb-4">
      <h1 class="text-2xl font-bold text-gray-800">Neues Fahrzeug anlegen</h1>
      <div class="h-1 w-24 bg-blue-500 mt-1 rounded-full"></div>
    </div>

    <form @submit.prevent="submit" class="space-y-4 max-w-7xl">
      <!-- Hauptinformationen -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Grunddaten -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-2">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Fahrzeugdaten</h2>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Erste Spalte -->
            <div class="space-y-4">
              <!-- Hersteller -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Hersteller <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.make"
                  type="text"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.make }"
                />
                <div v-if="form.errors.make" class="text-red-500 text-sm mt-1">{{ form.errors.make }}</div>
              </div>

              <!-- Modell -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Modell <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.model"
                  type="text"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.model }"
                />
                <div v-if="form.errors.model" class="text-red-500 text-sm mt-1">{{ form.errors.model }}</div>
              </div>

              <!-- Kilometerstand -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Kilometerstand <span class="text-red-500">*</span>
                </label>
                <input
                  v-model.number="form.mileage"
                  type="number"
                  min="0"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.mileage }"
                />
                <div v-if="form.errors.mileage" class="text-red-500 text-sm mt-1">{{ form.errors.mileage }}</div>
              </div>
              
              <!-- Jährliche Fahrleistung -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Jährliche Fahrleistung (km)
                </label>
                <input
                  v-model.number="form.annual_mileage"
                  type="number"
                  min="0"
                  max="200000"
                  placeholder="z.B. 15000"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.annual_mileage }"
                />
                <div v-if="form.errors.annual_mileage" class="text-red-500 text-sm mt-1">{{ form.errors.annual_mileage }}</div>
                <div class="text-xs text-gray-500 mt-1">Hilft bei der täglichen Aktualisierung des Kilometerstands</div>
              </div>
            </div>

            <!-- Zweite Spalte -->
            <div class="space-y-4">
              <!-- Typ -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Fahrzeugtyp
                </label>
                <input
                  v-model="form.type"
                  type="text"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.type }"
                />
                <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
              </div>

              <!-- Baujahr -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Baujahr
                </label>
                <input
                  v-model.number="form.year"
                  type="number"
                  min="1900"
                  :max="new Date().getFullYear() + 1"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.year }"
                />
                <div v-if="form.errors.year" class="text-red-500 text-sm mt-1">{{ form.errors.year }}</div>
              </div>

              <!-- Farbe -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Farbe
                </label>
                <input
                  v-model="form.color"
                  type="text"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.color }"
                />
                <div v-if="form.errors.color" class="text-red-500 text-sm mt-1">{{ form.errors.color }}</div>
              </div>
            </div>
          </div>

          <!-- Kennzeichen -->
          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Kennzeichen
            </label>
            <input
              v-model="form.license_plate"
              type="text"
              placeholder="z.B. B-AB 1234"
              class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 uppercase"
              :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.license_plate }"
            />
            <div v-if="form.errors.license_plate" class="text-red-500 text-sm mt-1">{{ form.errors.license_plate }}</div>
          </div>
        </div>

        <!-- Bild hochladen -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Fahrzeugbild</h2>
            </div>
          </div>

          <div class="flex flex-col items-center">
            <div v-if="imagePreview" class="relative w-full rounded-lg overflow-hidden shadow-sm border border-gray-200 mb-3">
              <img :src="imagePreview" alt="Fahrzeugvorschau" class="h-48 w-full object-cover" />
              <button
                @click="imagePreview = null; form.image = null"
                type="button"
                class="absolute top-2 right-2 h-7 w-7 flex items-center justify-center rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 text-red-500 hover:text-red-600 shadow-sm transition-all duration-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div class="w-full">
              <label for="dropzone-file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-all duration-200">
                <div class="flex flex-col items-center justify-center pt-4 pb-5">
                  <svg class="w-7 h-7 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <p class="mb-1 text-sm text-gray-500">{{ imagePreview ? 'Bild ändern' : 'Bild auswählen' }}</p>
                  <p class="text-xs text-gray-500">PNG, JPG (max. 10MB)</p>
                </div>
                <input type="file" id="dropzone-file" @change="handleImageUpload" class="hidden" accept="image/*" />
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Akkordeonsektionen -->
      <div class="space-y-4">
        <!-- Fahrzeugidentifikation -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group">
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Fahrzeug-Identifikation</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- HSN -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    HSN (Herstellerschlüsselnummer)
                  </label>
                  <input
                    v-model="form.hsn"
                    type="text"
                    placeholder="z.B. 0600"
                    maxlength="4"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.hsn }"
                  />
                  <div v-if="form.errors.hsn" class="text-red-500 text-sm mt-1">{{ form.errors.hsn }}</div>
                </div>

                <!-- TSN -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    TSN (Typschlüsselnummer)
                  </label>
                  <input
                    v-model="form.tsn"
                    type="text"
                    placeholder="z.B. AAU"
                    maxlength="3"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.tsn }"
                  />
                  <div v-if="form.errors.tsn" class="text-red-500 text-sm mt-1">{{ form.errors.tsn }}</div>
                </div>
              </div>

              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Fahrzeug-Identifikationsnummer (VIN)
                </label>
                <input
                  v-model="form.vin"
                  type="text"
                  maxlength="17"
                  placeholder="z.B. WVWZZZ1KZAM123456"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 uppercase"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.vin }"
                />
                <div v-if="form.errors.vin" class="text-red-500 text-sm mt-1">{{ form.errors.vin }}</div>
              </div>

              <!-- Fahrzeugdaten automatisch abrufen -->
              <div class="mt-4">
                <div class="bg-gray-100 rounded-lg p-3 border border-gray-200 mb-3">
                  <p class="text-sm text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                    </svg>
                    Die automatische Fahrzeugdatenabfrage ist derzeit deaktiviert. Bitte geben Sie die Fahrzeugdaten manuell ein.
                  </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- VIN-Abfrage -->
                  <div class="bg-amber-50 rounded-lg p-3 border border-amber-100 opacity-60">
                    <h4 class="text-sm font-medium text-amber-800 mb-1">Fahrzeugdaten über VIN abrufen</h4>
                    <div class="flex space-x-2">
                      <button
                        type="button"
                        class="px-3 py-2 rounded-lg bg-amber-500 text-white font-medium transition-all duration-200 focus:ring focus:ring-amber-200 whitespace-nowrap text-sm w-full cursor-not-allowed opacity-70"
                        disabled
                      >
                        VIN-Daten abfragen
                      </button>
                    </div>
                  </div>

                  <!-- HSN/TSN-Abfrage -->
                  <div class="bg-blue-50 rounded-lg p-3 border border-blue-100 opacity-60">
                    <h4 class="text-sm font-medium text-blue-800 mb-1">Fahrzeugdaten über HSN/TSN abrufen</h4>
                    <div class="flex space-x-2">
                      <button
                        type="button"
                        class="px-3 py-2 rounded-lg bg-blue-500 text-white font-medium transition-all duration-200 focus:ring focus:ring-blue-200 text-sm w-full cursor-not-allowed opacity-70"
                        disabled
                      >
                        HSN/TSN-Daten abfragen
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- VIN-Vorschläge -->
              <div v-if="vinSuggestions.length > 0" class="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="p-2 bg-amber-100 text-amber-800 text-sm font-medium">
                  Fahrzeugvorschlag
                </div>
                <ul>
                  <li v-for="(suggestion, index) in vinSuggestions" :key="index" class="p-2 hover:bg-gray-50 cursor-pointer" @click="applyVinSuggestion(suggestion)">
                    <div class="text-sm font-medium text-gray-800">{{ suggestion.make }} {{ suggestion.model }} ({{ suggestion.year || 'Jahr unbekannt' }})</div>
                    <div class="text-xs text-gray-500">
                      {{ suggestion.type || 'Typ unbekannt' }} - {{ suggestion.fuel_type || 'Kraftstoff unbekannt' }}
                      <span v-if="suggestion.power">, {{ suggestion.power }} kW</span>
                      <span v-if="suggestion.engine_size">, {{ suggestion.engine_size }} L</span>
                    </div>
                  </li>
                </ul>
              </div>

              <!-- HSN/TSN-Vorschläge -->
              <div v-if="vehicleSuggestions.length > 0" class="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 max-h-48 overflow-y-auto">
                <ul class="divide-y divide-gray-200">
                  <li v-for="(suggestion, index) in vehicleSuggestions" :key="index" class="p-2 hover:bg-gray-50 cursor-pointer" @click="applyVehicleSuggestion(suggestion)">
                    <div class="text-sm font-medium text-gray-800">{{ suggestion.make }} {{ suggestion.model }}</div>
                    <div class="text-xs text-gray-500">
                      {{ suggestion.type }} - {{ suggestion.fuel_type }}, {{ suggestion.power }} kW,
                      {{ suggestion.year_from }}{{ suggestion.year_to ? (' - ' + suggestion.year_to) : '' }}
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </details>
        </div>

        <!-- Technische Details -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group">
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Technische Details</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Kraftstoffart
                  </label>
                  <select
                    v-model="form.fuel_type"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.fuel_type }"
                  >
                    <option value="">Bitte wählen</option>
                    <option v-for="option in fuelTypes" :key="option.value" :value="option.value">{{ option.label }}</option>
                  </select>
                  <div v-if="form.errors.fuel_type" class="text-red-500 text-sm mt-1">{{ form.errors.fuel_type }}</div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Leistung (kW)
                  </label>
                  <input
                    v-model.number="form.power"
                    type="number"
                    min="0"
                    placeholder="z.B. 110"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.power }"
                  />
                  <div v-if="form.errors.power" class="text-red-500 text-sm mt-1">{{ form.errors.power }}</div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Hubraum
                  </label>
                  <input
                    v-model="form.engine_size"
                    type="text"
                    placeholder="z.B. 2.0 oder 1997ccm"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.engine_size }"
                  />
                  <div v-if="form.errors.engine_size" class="text-red-500 text-sm mt-1">{{ form.errors.engine_size }}</div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Getriebe
                  </label>
                  <select
                    v-model="form.transmission"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.transmission }"
                  >
                    <option value="">Bitte wählen</option>
                    <option v-for="option in transmissionTypes" :key="option.value" :value="option.value">{{ option.label }}</option>
                  </select>
                  <div v-if="form.errors.transmission" class="text-red-500 text-sm mt-1">{{ form.errors.transmission }}</div>
                </div>
              </div>
            </div>
          </details>
        </div>

        <!-- Reifen & Ersatzteile -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group">
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Reifen & Ersatzteile</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <!-- Reifengrößen -->
              <div class="mb-6">
                <h3 class="text-md font-medium text-gray-700 mb-3">Zugelassene Reifengrößen</h3>
                
                <div class="grid grid-cols-1 gap-4 mb-3">
                  <div 
                    v-for="(tireSize, index) in form.tire_sizes" 
                    :key="index" 
                    class="flex items-center gap-2"
                  >
                    <input 
                      v-model="form.tire_sizes[index].size" 
                      type="text" 
                      class="flex-grow px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                      placeholder="z.B. 205/55 R16 91H"
                    />
                    <select
                      v-model="form.tire_sizes[index].type"
                      class="w-1/4 px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                    >
                      <option v-for="type in tireTypes" :key="type.value" :value="type.value">{{ type.label }}</option>
                    </select>
                    <select
                      v-model="form.tire_sizes[index].position"
                      class="w-1/4 px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                    >
                      <option value="front">Vorne</option>
                      <option value="rear">Hinten</option>
                      <option value="both">Vorne und Hinten</option>
                    </select>
                    <button 
                      @click="() => form.tire_sizes.splice(index, 1)" 
                      type="button" 
                      class="ml-2 p-1.5 text-red-500 hover:text-red-700 transition-colors duration-200 focus:outline-none"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                  </div>
                </div>
                
                <button 
                  @click="() => form.tire_sizes.push({ size: '', position: 'both', type: 'summer' })" 
                  type="button" 
                  class="btn btn-sm btn-outline btn-primary"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                  Reifengröße hinzufügen
                </button>
              </div>

              <!-- Ersatzteile & Verschleißteile -->
              <div>
                <h3 class="text-md font-medium text-gray-700 mb-3">Ersatzteile & Verschleißteile</h3>
                
                <div v-for="(part, partIndex) in form.parts" :key="partIndex" class="bg-gray-50 p-3 rounded-lg mb-4 border border-gray-200">
                  <div class="flex justify-between items-center mb-3">
                    <h4 class="font-medium text-gray-800">Ersatzteil #{{ partIndex + 1 }}</h4>
                    <button 
                      @click="() => form.parts.splice(partIndex, 1)" 
                      type="button" 
                      class="text-red-500 hover:text-red-700 transition-colors duration-200 focus:outline-none"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                  </div>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3 mb-3">
                    <div class="form-control">
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Bezeichnung <span class="text-red-500">*</span>
                      </label>
                      <input 
                        v-model="part.name" 
                        type="text" 
                        required
                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                        placeholder="z.B. Ölfilter"
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Typ
                      </label>
                      <select 
                        v-model="part.type" 
                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                      >
                        <option value="">Bitte auswählen</option>
                        <option value="filter">Filter</option>
                        <option value="oil">Öl</option>
                        <option value="brake">Bremsen</option>
                        <option value="spark_plug">Zündkerzen</option>
                        <option value="battery">Batterie</option>
                        <option value="tire">Reifen</option>
                        <option value="bulb">Lampen</option>
                        <option value="wiper">Scheibenwischer</option>
                        <option value="belt">Riemen</option>
                        <option value="coolant">Kühlmittel</option>
                        <option value="other">Sonstiges</option>
                      </select>
                    </div>
                    
                    <!-- Custom type input field that appears only when "Sonstiges" is selected -->
                    <div v-if="part.type === 'other'" class="form-control mt-2">
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Benutzerdefinierter Typ
                      </label>
                      <input 
                        v-model="part.custom_type" 
                        type="text" 
                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                        placeholder="z.B. Luftfilter, Klimaanlage, etc."
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Artikel-Nr.
                      </label>
                      <input 
                        v-model="part.article_number" 
                        type="text" 
                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                        placeholder="z.B. HU7181x"
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        OEM-Nr.
                      </label>
                      <input 
                        v-model="part.oem_number" 
                        type="text" 
                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                        placeholder="z.B. 04E115561H"
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Hersteller-Nr.
                      </label>
                      <input 
                        v-model="part.manufacturer_number" 
                        type="text" 
                        class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                        placeholder="z.B. OX339/2D"
                      />
                    </div>
                  </div>
                  
                  <div class="form-control">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      Anmerkungen
                    </label>
                    <textarea 
                      v-model="part.notes" 
                      class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 resize-none h-16"
                      placeholder="Zusätzliche Informationen..."
                    ></textarea>
                  </div>
                  
                  <!-- Purchase Links -->
                  <div class="mt-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      Bestelllinks
                    </label>
                    <div v-for="(link, linkIndex) in part.purchase_links" :key="linkIndex" class="flex items-center gap-2 mb-2">
                      <input 
                        v-model="link.label"
                        type="text"
                        class="w-1/3 px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                        placeholder="z.B. Amazon, eBay, etc."
                      />
                      <input 
                        v-model="link.url"
                        type="url"
                        class="flex-grow px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                        placeholder="https://..."
                      />
                      <button 
                        @click="() => part.purchase_links.splice(linkIndex, 1)"
                        type="button"
                        class="text-red-500 hover:text-red-700 transition-colors duration-200 focus:outline-none"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                      </button>
                    </div>
                    <button 
                      @click="() => part.purchase_links.push({ label: '', url: '' })"
                      type="button"
                      class="btn btn-xs btn-outline"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                      Link hinzufügen
                    </button>
                  </div>
                </div>
                
                <button 
                  @click="() => form.parts.push({
                    name: '',
                    type: '',
                    article_number: null,
                    oem_number: null,
                    manufacturer_number: null,
                    notes: null,
                    purchase_links: []
                  })" 
                  type="button" 
                  class="btn btn-sm btn-outline btn-primary"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                  Ersatzteil hinzufügen
                </button>
              </div>
            </div>
          </details>
        </div>

        <!-- Kaufdaten -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group">
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Kaufdaten & Notizen</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Kaufdatum
                  </label>
                  <input
                    v-model="form.purchase_date"
                    type="date"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.purchase_date }"
                  />
                  <div v-if="form.errors.purchase_date" class="text-red-500 text-sm mt-1">{{ form.errors.purchase_date }}</div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Kaufpreis (€)
                  </label>
                  <div class="relative">
                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
                    <input
                      v-model.number="form.purchase_price"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="z.B. 15000.00"
                      class="w-full pl-7 pr-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                      :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.purchase_price }"
                    />
                  </div>
                  <div v-if="form.errors.purchase_price" class="text-red-500 text-sm mt-1">{{ form.errors.purchase_price }}</div>
                </div>
              </div>

              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Notizen
                </label>
                <textarea
                  v-model="form.notes"
                  rows="3"
                  placeholder="Zusätzliche Informationen zum Fahrzeug..."
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 resize-none"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.notes }"
                ></textarea>
                <div v-if="form.errors.notes" class="text-red-500 text-sm mt-1">{{ form.errors.notes }}</div>
              </div>
            </div>
          </details>
        </div>
      </div>

      <!-- Submit-Bereich -->
      <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
        <div class="flex justify-end gap-3">
          <a href="/vehicles" class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-100 hover:border-gray-400 transition-all duration-200">Abbrechen</a>
          <button
            type="submit"
            class="px-4 py-2 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 shadow-sm hover:shadow transition-all duration-200 focus:ring focus:ring-blue-200"
            :disabled="form.processing"
          >
            <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Fahrzeug speichern
          </button>
        </div>
      </div>
    </form>
  </div>
</template>
