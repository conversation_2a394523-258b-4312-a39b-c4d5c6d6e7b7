<script setup lang="ts">
import { Link, router, useForm } from '@inertiajs/vue3';
import { onMounted, ref } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  vehicle: {
    id: number;
    make: string;
    model: string;
    type: string | null;
    hsn: string | null;
    tsn: string | null;
    vin: string | null;
    license_plate: string | null;
    year: number | null;
    mileage: number;
    annual_mileage: number;
    purchase_date: string | null;
    purchase_price: number | null;
    notes: string | null;
    color: string | null;
    fuel_type: string | null;
    power: number | null;
    engine_size: string | null;
    transmission: string | null;
    image: string | null;
    is_active: boolean;
    tire_sizes: Array<{
      size: string;
      position: string;
      type?: string;
    }> | null;
    parts: Array<{
      name: string;
      type: string;
      article_number: string | null;
      oem_number: string | null;
      manufacturer_number: string | null;
      notes: string | null;
      purchase_links: Array<{
        label: string;
        url: string;
      }>;
      custom_type?: string;
    }> | null;
  };
}>();

const form = useForm({
  make: props.vehicle.make,
  model: props.vehicle.model,
  type: props.vehicle.type,
  hsn: props.vehicle.hsn,
  tsn: props.vehicle.tsn,
  vin: props.vehicle.vin,
  license_plate: props.vehicle.license_plate,
  year: props.vehicle.year,
  mileage: props.vehicle.mileage,
  annual_mileage: props.vehicle.annual_mileage,
  purchase_date: props.vehicle.purchase_date,
  purchase_price: props.vehicle.purchase_price,
  notes: props.vehicle.notes,
  color: props.vehicle.color,
  fuel_type: props.vehicle.fuel_type,
  power: props.vehicle.power,
  engine_size: props.vehicle.engine_size,
  transmission: props.vehicle.transmission,
  is_active: props.vehicle.is_active,
  image: null as File | null,
  tire_sizes: (props.vehicle.tire_sizes || []).map(size => {
    // Convert string to object if it's a simple string
    if (typeof size === 'string') {
      return { size: size, position: 'both', type: 'summer' };
    }
    // Ensure type exists with default value if not set
    return {
      ...size,
      type: size.type || 'summer'
    };
  }),
  parts: (props.vehicle.parts || []).map(part => ({
    ...part,
    purchase_links: part.purchase_links || []
  })),
  _method: 'PUT'
});

// Format the purchase_date if it exists
// HTML date inputs require YYYY-MM-DD format
if (props.vehicle.purchase_date) {
  try {
    // Convert the date string to a Date object then format it as YYYY-MM-DD
    const date = new Date(props.vehicle.purchase_date);
    if (!isNaN(date.getTime())) {
      // Date is valid
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // months are 0-indexed
      const day = String(date.getDate()).padStart(2, '0');
      form.purchase_date = `${year}-${month}-${day}`;
    }
  } catch (error) {
    console.error('Error formatting purchase date:', error);
  }
}

// Add new helper function for image paths
const getImagePath = (imagePath: string | null): string | undefined => {
  if (!imagePath) return undefined;

  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  if (imagePath.startsWith('storage/')) {
    return `/${imagePath}`;
  }

  return `/storage/${imagePath}`;
};

// Update the initialization of imagePreview
const imagePreview = ref(props.vehicle.image ? getImagePath(props.vehicle.image) : null);
const imageInputRef = ref<HTMLInputElement | null>(null);

const fuelTypes = [
  { value: 'petrol', label: 'Benzin' },
  { value: 'diesel', label: 'Diesel' },
  { value: 'electric', label: 'Elektro' },
  { value: 'hybrid', label: 'Hybrid' },
  { value: 'plugin_hybrid', label: 'Plug-in-Hybrid' },
  { value: 'lpg', label: 'Autogas (LPG)' },
  { value: 'cng', label: 'Erdgas (CNG)' },
  { value: 'hydrogen', label: 'Wasserstoff' },
  { value: 'other', label: 'Sonstiges' }
];

const transmissionTypes = [
  { value: 'manual', label: 'Manuell' },
  { value: 'automatic', label: 'Automatik' },
  { value: 'semi_automatic', label: 'Halbautomatik' },
  { value: 'cvt', label: 'Stufenlos (CVT)' },
  { value: 'dual_clutch', label: 'Doppelkupplung (DSG)' }
];

// Add tire types
const tireTypes = [
  { value: 'summer', label: 'Sommerreifen' },
  { value: 'winter', label: 'Winterreifen' },
  { value: 'all_season', label: 'Ganzjahresreifen' }
];

const handleImageUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    form.image = file;

    // Bildvorschau erstellen
    const reader = new FileReader();
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }
};

const removeImage = () => {
  form.image = null;
  imagePreview.value = null;
  if (imageInputRef.value) {
    imageInputRef.value.value = '';
  }
};

const submit = () => {
  // Process custom part types before submission
  if (form.parts) {
    form.parts.forEach(part => {
      if (part.type === 'other' && part.custom_type) {
        part.type = 'custom:' + part.custom_type;
      }
    });
  }

  form.post(route('vehicles.update', props.vehicle.id), {
    forceFormData: true,
    preserveScroll: true,
    onSuccess: () => {
      // Reset nur das Bild, da der Rest als Editwerte erhalten bleiben soll
      form.image = null;
    },
  });
};

// Parse custom part types when form is loaded
onMounted(() => {
  if (form.parts) {
    form.parts.forEach(part => {
      if (part.type && part.type.startsWith('custom:')) {
        part.custom_type = part.type.substring(7); // Extract the custom value
        part.type = 'other'; // Set back to 'other' for the dropdown
      }
    });
  }
});

// Bild-Vorschau aktualisieren, wenn vorhanden
if (props.vehicle.image) {
  imagePreview.value = `/storage/${props.vehicle.image}`;
}
</script>

<template>
  <div class="p-6 md:p-8 bg-gray-50">
    <div class="flex justify-between items-center mb-8">
      <div class="flex items-center gap-3">
        <Link :href="route('vehicles.index')" class="btn btn-circle btn-outline btn-sm border-gray-300 hover:bg-gray-100 hover:border-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <h1 class="text-2xl font-bold text-gray-800">Fahrzeug bearbeiten</h1>
      </div>
    </div>

    <form @submit.prevent="submit">
      <!-- Grundinformationen -->
      <div class="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
        <div class="mb-6 border-b border-gray-100 pb-4">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
            <h2 class="text-xl font-semibold text-gray-800">Grundinformationen</h2>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Marke <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.make"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              required
            />
            <div v-if="form.errors.make" class="text-red-500 text-sm mt-1">{{ form.errors.make }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Modell <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.model"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              required
            />
            <div v-if="form.errors.model" class="text-red-500 text-sm mt-1">{{ form.errors.model }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Kennzeichen
            </label>
            <input
              v-model="form.license_plate"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.license_plate" class="text-red-500 text-sm mt-1">{{ form.errors.license_plate }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Baujahr
            </label>
            <input
              v-model="form.year"
              type="number"
              min="1900"
              :max="new Date().getFullYear()"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.year" class="text-red-500 text-sm mt-1">{{ form.errors.year }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Kilometerstand <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.mileage"
              type="number"
              min="0"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              required
            />
            <div v-if="form.errors.mileage" class="text-red-500 text-sm mt-1">{{ form.errors.mileage }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Jährliche Fahrleistung (km)
            </label>
            <input
              v-model="form.annual_mileage"
              type="number"
              min="0"
              max="200000"
              placeholder="z.B. 15000"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.annual_mileage" class="text-red-500 text-sm mt-1">{{ form.errors.annual_mileage }}</div>
            <div class="text-xs text-gray-500 mt-1">Hilft bei der täglichen Aktualisierung des Kilometerstands</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Farbe
            </label>
            <input
              v-model="form.color"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.color" class="text-red-500 text-sm mt-1">{{ form.errors.color }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Fahrzeugtyp
            </label>
            <input
              v-model="form.type"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
          </div>

          <div class="form-control py-2">
            <label class="inline-flex items-center cursor-pointer">
              <input
                v-model="form.is_active"
                type="checkbox"
                class="form-checkbox h-5 w-5 text-blue-500 rounded border-gray-300 focus:ring-blue-200"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">Fahrzeug ist aktiv</span>
            </label>
            <div v-if="form.errors.is_active" class="text-red-500 text-sm mt-1">{{ form.errors.is_active }}</div>
          </div>
        </div>
      </div>

      <!-- Technische Details -->
      <div class="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
        <div class="mb-6 border-b border-gray-100 pb-4">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
            <h2 class="text-xl font-semibold text-gray-800">Technische Details</h2>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              HSN (Herstellerschlüsselnummer)
            </label>
            <input
              v-model="form.hsn"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.hsn" class="text-red-500 text-sm mt-1">{{ form.errors.hsn }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              TSN (Typschlüsselnummer)
            </label>
            <input
              v-model="form.tsn"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.tsn" class="text-red-500 text-sm mt-1">{{ form.errors.tsn }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              VIN (Fahrgestellnummer)
            </label>
            <input
              v-model="form.vin"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.vin" class="text-red-500 text-sm mt-1">{{ form.errors.vin }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Kraftstoffart
            </label>
            <select
              v-model="form.fuel_type"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
            >
              <option value="">Bitte auswählen</option>
              <option v-for="type in fuelTypes" :key="type.value" :value="type.value">{{ type.label }}</option>
            </select>
            <div v-if="form.errors.fuel_type" class="text-red-500 text-sm mt-1">{{ form.errors.fuel_type }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Leistung (kW)
            </label>
            <input
              v-model="form.power"
              type="number"
              min="0"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.power" class="text-red-500 text-sm mt-1">{{ form.errors.power }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Hubraum (in cm³)
            </label>
            <input
              v-model="form.engine_size"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.engine_size" class="text-red-500 text-sm mt-1">{{ form.errors.engine_size }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Getriebe
            </label>
            <select
              v-model="form.transmission"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
            >
              <option value="">Bitte auswählen</option>
              <option v-for="type in transmissionTypes" :key="type.value" :value="type.value">{{ type.label }}</option>
            </select>
            <div v-if="form.errors.transmission" class="text-red-500 text-sm mt-1">{{ form.errors.transmission }}</div>
          </div>
        </div>
      </div>

      <!-- Kaufdaten -->
      <div class="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
        <div class="mb-6 border-b border-gray-100 pb-4">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
            <h2 class="text-xl font-semibold text-gray-800">Kaufdaten</h2>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Kaufdatum
            </label>
            <input
              v-model="form.purchase_date"
              type="date"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.purchase_date" class="text-red-500 text-sm mt-1">{{ form.errors.purchase_date }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Kaufpreis (€)
            </label>
            <div class="relative">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
              <input
                v-model="form.purchase_price"
                type="number"
                min="0"
                step="0.01"
                class="w-full pl-8 pr-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              />
            </div>
            <div v-if="form.errors.purchase_price" class="text-red-500 text-sm mt-1">{{ form.errors.purchase_price }}</div>
          </div>
        </div>
      </div>
<!-- Reifen & Ersatzteile -->
      <div class="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
        <div class="mb-6 border-b border-gray-100 pb-4">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
            <h2 class="text-xl font-semibold text-gray-800">Reifen & Ersatzteile</h2>
          </div>
        </div>

        <!-- Reifengrößen -->
        <div class="mb-8">
          <h3 class="text-lg font-medium text-gray-700 mb-4">Zugelassene Reifengrößen</h3>
          
          <div class="grid grid-cols-1 gap-4 mb-4">
            <div 
              v-for="(tireSize, index) in form.tire_sizes" 
              :key="index" 
              class="flex items-center gap-2"
            >
              <input 
                v-model="form.tire_sizes[index].size" 
                type="text" 
                class="flex-grow px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                placeholder="z.B. 205/55 R16 91H"
              />
              <select
                v-model="form.tire_sizes[index].type"
                class="w-1/4 px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              >
                <option v-for="type in tireTypes" :key="type.value" :value="type.value">{{ type.label }}</option>
              </select>
              <select
                v-model="form.tire_sizes[index].position"
                class="w-1/4 px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              >
                <option value="both">Vorne und hinten</option>
                <option value="front">Vorne</option>
                <option value="rear">Hinten</option>
              </select>
              <button 
                @click="() => form.tire_sizes.splice(index, 1)" 
                type="button" 
                class="ml-2 p-2 text-red-500 hover:text-red-700 transition-colors duration-200 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
              </button>
            </div>
          </div>
          
          <button 
            @click="() => form.tire_sizes.push({ size: '', position: 'both', type: 'summer' })" 
            type="button" 
            class="btn btn-sm btn-outline btn-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            Reifengröße hinzufügen
          </button>
        </div>

        <!-- Ersatzteile & Verschleißteile -->
        <div>
          <h3 class="text-lg font-medium text-gray-700 mb-4">Ersatzteile & Verschleißteile</h3>
          
          <div v-for="(part, partIndex) in form.parts" :key="partIndex" class="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
            <div class="flex justify-between items-center mb-4">
              <h4 class="font-medium text-gray-800">Ersatzteil #{{ partIndex + 1 }}</h4>
              <button 
                @click="() => form.parts.splice(partIndex, 1)" 
                type="button" 
                class="text-red-500 hover:text-red-700 transition-colors duration-200 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
              </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-4">
              <div class="form-control">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Bezeichnung <span class="text-red-500">*</span>
                </label>
                <input 
                  v-model="part.name" 
                  type="text" 
                  required
                  class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  placeholder="z.B. Ölfilter"
                />
              </div>
              
              <div class="form-control">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Typ
                </label>
                <select 
                  v-model="part.type" 
                  class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                >
                  <option value="">Bitte auswählen</option>
                  <option value="filter">Filter</option>
                  <option value="oil">Öl</option>
                  <option value="brake">Bremsen</option>
                  <option value="spark_plug">Zündkerzen</option>
                  <option value="battery">Batterie</option>
                  <option value="tire">Reifen</option>
                  <option value="bulb">Lampen</option>
                  <option value="wiper">Scheibenwischer</option>
                  <option value="belt">Riemen</option>
                  <option value="coolant">Kühlmittel</option>
                  <option value="other">Sonstiges</option>
                </select>
              </div>
              
              <!-- Custom type input field that appears only when "Sonstiges" is selected -->
              <div v-if="part.type === 'other'" class="form-control mt-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Benutzerdefinierter Typ
                </label>
                <input 
                  v-model="part.custom_type" 
                  type="text" 
                  class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  placeholder="z.B. Luftfilter, Klimaanlage, etc."
                />
              </div>
              
              <div class="form-control">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Artikel-Nr.
                </label>
                <input 
                  v-model="part.article_number" 
                  type="text" 
                  class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  placeholder="z.B. HU7181x"
                />
              </div>
              
              <div class="form-control">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  OEM-Nr.
                </label>
                <input 
                  v-model="part.oem_number" 
                  type="text" 
                  class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  placeholder="z.B. 04E115561H"
                />
              </div>
              
              <div class="form-control">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Hersteller-Nr.
                </label>
                <input 
                  v-model="part.manufacturer_number" 
                  type="text" 
                  class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  placeholder="z.B. OX339/2D"
                />
              </div>
            </div>
            
            <div class="form-control">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Anmerkungen
              </label>
              <textarea 
                v-model="part.notes" 
                class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 resize-none h-20"
                placeholder="Zusätzliche Informationen..."
              ></textarea>
            </div>
            
            <!-- Purchase Links -->
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Bestelllinks
              </label>
              <div v-for="(link, linkIndex) in part.purchase_links" :key="linkIndex" class="flex items-center gap-2 mb-2">
                <input 
                  v-model="link.label"
                  type="text"
                  class="w-1/3 px-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  placeholder="z.B. Amazon, eBay, etc."
                />
                <input 
                  v-model="link.url"
                  type="url"
                  class="flex-grow px-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  placeholder="https://..."
                />
                <button 
                  @click="() => part.purchase_links.splice(linkIndex, 1)"
                  type="button"
                  class="text-red-500 hover:text-red-700 transition-colors duration-200 focus:outline-none"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>
              </div>
              <button 
                @click="() => part.purchase_links.push({ label: '', url: '' })"
                type="button"
                class="btn btn-xs btn-outline"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                Link hinzufügen
              </button>
            </div>
          </div>
          
          <button 
            @click="() => form.parts.push({
              name: '',
              type: '',
              article_number: null,
              oem_number: null,
              manufacturer_number: null,
              notes: null,
              purchase_links: [],
              custom_type: undefined
            })" 
            type="button" 
            class="btn btn-sm btn-outline btn-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            Ersatzteil hinzufügen
          </button>
        </div>
      </div>
      <!-- Fahrzeugbild & Notizen -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <!-- Notizen -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 h-full">
          <div class="mb-6 border-b border-gray-100 pb-4">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
              <h2 class="text-xl font-semibold text-gray-800">Notizen</h2>
            </div>
          </div>

          <div>
            <div class="form-control">
              <textarea
                v-model="form.notes"
                class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 resize-none h-48"
                placeholder="Zusätzliche Informationen zum Fahrzeug..."
              ></textarea>
              <div v-if="form.errors.notes" class="text-red-500 text-sm mt-1">{{ form.errors.notes }}</div>
            </div>
          </div>
        </div>

        <!-- Fahrzeugbild -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 h-full">
          <div class="mb-6 border-b border-gray-100 pb-4">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
              <h2 class="text-xl font-semibold text-gray-800">Fahrzeugbild</h2>
            </div>
          </div>

          <div>
            <div v-if="imagePreview" class="mb-6">
              <div class="relative w-full rounded-lg overflow-hidden shadow-sm border border-gray-200">
                <img :src="imagePreview" alt="Fahrzeugvorschau" class="w-full object-cover max-h-48" />
                <button
                  @click="removeImage"
                  type="button"
                  class="absolute top-2 right-2 h-8 w-8 flex items-center justify-center rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 text-red-500 hover:text-red-600 shadow-sm transition-all duration-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>
              </div>
            </div>

            <div class="form-control">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Bild hochladen
              </label>
              <div class="flex items-center justify-center w-full">
                <label for="dropzone-file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-all duration-200">
                  <div class="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg class="w-8 h-8 mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <p class="mb-1 text-sm text-gray-500">Dateien hier ablegen oder klicken</p>
                    <p class="text-xs text-gray-500">PNG, JPG, GIF (max. 10MB)</p>
                  </div>
                  <input
                    id="dropzone-file"
                    ref="imageInputRef"
                    type="file"
                    @change="handleImageUpload"
                    accept="image/*"
                    class="hidden"
                  />
                </label>
              </div>
              <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">{{ form.errors.image }}</div>
            </div>
          </div>
        </div>
      </div>

        

      <!-- Submit-Bereich -->
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex justify-end gap-4">
          <Link :href="route('vehicles.show', props.vehicle.id)" class="px-6 py-2.5 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-100 hover:border-gray-400 transition-all duration-200">Abbrechen</Link>
          <button
            type="submit"
            class="px-6 py-2.5 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 shadow-sm hover:shadow transition-all duration-200 focus:ring focus:ring-blue-200"
            :disabled="form.processing"
          >
            <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Speichern
          </button>
        </div>
      </div>
    </form>
  </div>
</template>
