<script setup lang="ts">
import { Link, router, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  serviceReminder: {
    id: number;
    title: string;
    description: string | null;
    due_date: string | null;
    due_mileage: number | null;
    status: string;
    priority: string;
    repeat_interval: number | null;
    repeat_unit: string | null;
    vehicle_id: number;
  };
  vehicle: {
    id: number;
    make: string;
    model: string;
    license_plate: string | null;
    mileage: number;
  };
}>();

const form = useForm({
  title: props.serviceReminder.title,
  description: props.serviceReminder.description,
  due_date: props.serviceReminder.due_date,
  due_mileage: props.serviceReminder.due_mileage,
  status: props.serviceReminder.status,
  priority: props.serviceReminder.priority,
  repeat_interval: props.serviceReminder.repeat_interval,
  repeat_unit: props.serviceReminder.repeat_unit
});

const isFormSubmitted = ref(false);

const priorityOptions = [
  { value: 'high', label: 'Hoch' },
  { value: 'medium', label: 'Mittel' },
  { value: 'low', label: 'Niedrig' }
];

const statusOptions = [
  { value: 'pending', label: 'Ausstehend' },
  { value: 'completed', label: 'Abgeschlossen' },
  { value: 'overdue', label: 'Überfällig' }
];

const repeatUnitOptions = [
  { value: 'days', label: 'Tage' },
  { value: 'weeks', label: 'Wochen' },
  { value: 'months', label: 'Monate' },
  { value: 'years', label: 'Jahre' },
  { value: 'km', label: 'Kilometer' }
];

const showRepeatOptions = ref(!!props.serviceReminder.repeat_interval && !!props.serviceReminder.repeat_unit);

const toggleRepeatOptions = () => {
  showRepeatOptions.value = !showRepeatOptions.value;
  if (!showRepeatOptions.value) {
    form.repeat_interval = null;
    form.repeat_unit = null;
  }
};

const submit = () => {
  isFormSubmitted.value = true;
  console.log('Formular wird gesendet...', form);

  form.put(route('service-reminders.update', props.serviceReminder.id), {
    preserveScroll: true,
    onSuccess: () => {
      console.log('Erfolg: Serviceerinnerung wurde aktualisiert');
      // Zur Fahrzeugdetailseite zurückleiten
      router.visit(route('vehicles.show', props.vehicle.id));
    },
    onError: (errors) => {
      console.error('Fehler beim Aktualisieren der Serviceerinnerung:', errors);
      alert('Fehler beim Speichern: ' + Object.values(errors).join('\n'));
    },
    onFinish: () => {
      console.log('Anfrage abgeschlossen');
      isFormSubmitted.value = false;
    }
  });
};

// Formvalidierung
const validationErrors = ref<Record<string, string>>({});

const validateForm = () => {
  validationErrors.value = {};

  // Prüfen, ob entweder due_date oder due_mileage gesetzt ist
  if (!form.due_date && !form.due_mileage) {
    validationErrors.value.due_date = 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.';
    validationErrors.value.due_mileage = 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.';
  }

  // Wenn Wiederholung aktiviert ist, prüfen ob beide Felder gesetzt sind
  if (showRepeatOptions.value) {
    if (!form.repeat_interval) {
      validationErrors.value.repeat_interval = 'Bitte geben Sie ein Wiederholungsintervall an.';
    }
    if (!form.repeat_unit) {
      validationErrors.value.repeat_unit = 'Bitte wählen Sie eine Einheit für die Wiederholung.';
    }
  }

  return Object.keys(validationErrors.value).length === 0;
};

// Submit mit Validierung
const validateAndSubmit = () => {
  console.log('validateAndSubmit wurde aufgerufen');
  const isValid = validateForm();
  if (isValid) {
    console.log('Validierung erfolgreich, submit wird aufgerufen');
    submit();
  } else {
    console.error('Validierungsfehler:', validationErrors.value);
  }
};
</script>

<template>
  <div class="p-4 md:p-6 bg-gray-50">
    <!-- Header mit Stil -->
    <div class="mb-4">
      <div class="flex items-center gap-3 mb-1">
        <Link :href="route('vehicles.show', vehicle.id)" class="btn btn-circle btn-outline btn-sm border-gray-300 hover:bg-gray-100 hover:border-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <h1 class="text-2xl font-bold text-gray-800">Servicerinnerung bearbeiten</h1>
      </div>
      <div class="h-1 w-24 bg-blue-500 mt-1 rounded-full"></div>
    </div>

    <form @submit.prevent="validateAndSubmit" class="space-y-4 max-w-7xl">
      <!-- Hauptinfo-Bereich -->
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
        <!-- Hauptinformationen -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-8">
          <!-- Header Abschnitt -->
          <div class="mb-4 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Grundinformationen</h2>
            </div>
          </div>

          <div class="flex items-center gap-3 mb-3">
            <span class="text-sm font-medium text-gray-600">Fahrzeug:</span>
            <div class="badge badge-lg py-2.5 px-3 border border-gray-300 font-medium text-gray-700 bg-gray-50">{{ vehicle.make }} {{ vehicle.model }}</div>
            <div v-if="vehicle.license_plate" class="badge badge-lg py-2.5 px-3 bg-blue-100 text-blue-800 border-blue-200 font-medium">{{ vehicle.license_plate }}</div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Linke Spalte -->
            <div class="space-y-4">
              <!-- Titel -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Titel <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.title"
                  type="text"
                  placeholder="z.B. Ölwechsel, Reifenwechsel..."
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.title }"
                />
                <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
                <div v-else-if="validationErrors.title" class="text-red-500 text-sm mt-1">{{ validationErrors.title }}</div>
              </div>

              <!-- Fälligkeitsdatum -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Fällig am
                </label>
                <input
                  v-model="form.due_date"
                  type="date"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.due_date || validationErrors.due_date }"
                />
                <div v-if="form.errors.due_date" class="text-red-500 text-sm mt-1">{{ form.errors.due_date }}</div>
                <div v-else-if="validationErrors.due_date" class="text-red-500 text-sm mt-1">{{ validationErrors.due_date }}</div>
              </div>
            </div>

            <!-- Rechte Spalte -->
            <div class="space-y-4">
              <!-- Priorität -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Priorität <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="form.priority"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                >
                  <option v-for="option in priorityOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </option>
                </select>
                <div v-if="form.errors.priority" class="text-red-500 text-sm mt-1">{{ form.errors.priority }}</div>
              </div>

              <!-- Fällig bei Kilometerstand -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Fällig bei Kilometerstand
                </label>
                <div class="flex items-center">
                  <input
                    v-model="form.due_mileage"
                    type="number"
                    min="0"
                    placeholder="z.B. 100.000"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.due_mileage || validationErrors.due_mileage }"
                  />
                  <span class="ml-2 text-gray-500">km</span>
                </div>
                <div v-if="form.errors.due_mileage" class="text-red-500 text-sm mt-1">{{ form.errors.due_mileage }}</div>
                <div v-else-if="validationErrors.due_mileage" class="text-red-500 text-sm mt-1">{{ validationErrors.due_mileage }}</div>
                <div class="text-xs text-gray-500 mt-1">Aktueller Kilometerstand: {{ vehicle.mileage.toLocaleString() }} km</div>
              </div>
            </div>
          </div>

          <!-- Status -->
          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Status <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.status"
              class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              required
            >
              <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
            <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">{{ form.errors.status }}</div>
          </div>
        </div>

        <!-- Auslöser für Extras und Seitenleiste -->
        <div class="md:col-span-4 space-y-4">
          <!-- Speichern-Button -->
          <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
            <button
              type="submit"
              class="btn btn-primary w-full"
              :class="{ 'loading': isFormSubmitted }"
              :disabled="isFormSubmitted"
            >
              Änderungen speichern
            </button>
            <Link :href="route('vehicles.show', vehicle.id)" class="btn btn-outline w-full mt-2">
              Abbrechen
            </Link>
          </div>
        </div>
      </div>

      <!-- Beschreibung -->
      <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
        <div class="flex items-center justify-between mb-4 border-b border-gray-100 pb-2">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
            <h2 class="text-lg font-semibold text-gray-800">Beschreibung</h2>
          </div>
        </div>

        <div>
          <textarea
            v-model="form.description"
            rows="4"
            placeholder="Detaillierte Beschreibung der Servicerinnerung..."
            class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
          ></textarea>
          <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
        </div>
      </div>

      <!-- Wiederholung -->
      <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
        <div class="flex items-center justify-between mb-4 border-b border-gray-100 pb-2">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
            <h2 class="text-lg font-semibold text-gray-800">Wiederholung</h2>
          </div>
          <div class="form-control">
            <label class="cursor-pointer label flex gap-2">
              <span class="label-text">Wiederholung aktivieren</span>
              <input
                type="checkbox"
                class="toggle toggle-primary"
                :checked="showRepeatOptions"
                @change="toggleRepeatOptions"
              />
            </label>
          </div>
        </div>

        <div v-if="showRepeatOptions" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Intervall
            </label>
            <input
              v-model="form.repeat_interval"
              type="number"
              min="1"
              placeholder="z.B. 6"
              class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.repeat_interval || validationErrors.repeat_interval }"
            />
            <div v-if="form.errors.repeat_interval" class="text-red-500 text-sm mt-1">{{ form.errors.repeat_interval }}</div>
            <div v-else-if="validationErrors.repeat_interval" class="text-red-500 text-sm mt-1">{{ validationErrors.repeat_interval }}</div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Einheit
            </label>
            <select
              v-model="form.repeat_unit"
              class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.repeat_unit || validationErrors.repeat_unit }"
            >
              <option value="" disabled selected>Einheit wählen</option>
              <option v-for="option in repeatUnitOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
            <div v-if="form.errors.repeat_unit" class="text-red-500 text-sm mt-1">{{ form.errors.repeat_unit }}</div>
            <div v-else-if="validationErrors.repeat_unit" class="text-red-500 text-sm mt-1">{{ validationErrors.repeat_unit }}</div>
          </div>

          <div class="md:col-span-2">
            <div class="alert alert-info shadow-sm">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
              <div>
                <div class="font-medium">Wiederholende Erinnerung</div>
                <div class="text-xs">Nach Abschluss einer Servicerinnerung wird automatisch eine neue Erinnerung für den nächsten Service erstellt.</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>
