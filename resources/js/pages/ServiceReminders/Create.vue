<script setup lang="ts">
import { Link, router, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  vehicle: {
    id: number;
    make: string;
    model: string;
    license_plate: string | null;
    mileage: number;
  };
}>();

const form = useForm({
  vehicle_id: props.vehicle.id,
  title: '',
  description: null as string | null,
  due_date: null as string | null,
  due_mileage: null as number | null,
  status: 'pending',
  priority: 'medium',
  repeat_interval: null as number | null,
  repeat_unit: null as string | null
});

const isFormSubmitted = ref(false);

const priorityOptions = [
  { value: 'high', label: 'Hoch' },
  { value: 'medium', label: 'Mittel' },
  { value: 'low', label: 'Niedrig' }
];

const statusOptions = [
  { value: 'pending', label: 'Ausstehend' },
  { value: 'completed', label: 'Abgeschlossen' },
  { value: 'overdue', label: 'Überfällig' }
];

const repeatUnitOptions = [
  { value: 'days', label: 'Tage' },
  { value: 'weeks', label: 'Wochen' },
  { value: 'months', label: 'Monate' },
  { value: 'years', label: 'Jahre' },
  { value: 'km', label: 'Kilometer' }
];

const showRepeatOptions = ref(true);

const toggleRepeatOptions = () => {
  showRepeatOptions.value = !showRepeatOptions.value;
  if (!showRepeatOptions.value) {
    form.repeat_interval = null;
    form.repeat_unit = null;
  }
};

const submit = () => {
  isFormSubmitted.value = true;
  console.log('Formular wird gesendet...', form);

  form.post(route('vehicles.service-reminders.store', props.vehicle.id), {
    preserveScroll: true,
    onSuccess: () => {
      console.log('Erfolg: Serviceerinnerung wurde erstellt');
      // Zur Fahrzeugdetailseite zurückleiten
      router.visit(route('vehicles.show', props.vehicle.id));
    },
    onError: (errors) => {
      console.error('Fehler beim Erstellen der Serviceerinnerung:', errors);
      alert('Fehler beim Speichern: ' + Object.values(errors).join('\n'));
    },
    onFinish: () => {
      console.log('Anfrage abgeschlossen');
      isFormSubmitted.value = false;
    }
  });
};

// Formvalidierung
const validationErrors = ref<Record<string, string>>({});

const validateForm = () => {
  validationErrors.value = {};

  // Prüfen, ob entweder due_date oder due_mileage gesetzt ist
  if (!form.due_date && !form.due_mileage) {
    validationErrors.value.due_date = 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.';
    validationErrors.value.due_mileage = 'Bitte geben Sie entweder ein Fälligkeitsdatum oder einen Kilometerstand an.';
  }

  // Wenn Wiederholung aktiviert ist, prüfen ob beide Felder gesetzt sind
  if (showRepeatOptions.value) {
    if (!form.repeat_interval) {
      validationErrors.value.repeat_interval = 'Bitte geben Sie ein Wiederholungsintervall an.';
    }
    if (!form.repeat_unit) {
      validationErrors.value.repeat_unit = 'Bitte wählen Sie eine Einheit für die Wiederholung.';
    }
  }

  return Object.keys(validationErrors.value).length === 0;
};

// Submit mit Validierung
const validateAndSubmit = () => {
  console.log('validateAndSubmit wurde aufgerufen');
  const isValid = validateForm();
  if (isValid) {
    console.log('Validierung erfolgreich, submit wird aufgerufen');
    submit();
  } else {
    console.error('Validierungsfehler:', validationErrors.value);
  }
};
</script>

<template>
  <div class="p-4 md:p-6 bg-gray-50">
    <!-- Header mit Stil -->
    <div class="mb-4">
      <div class="flex items-center gap-3 mb-1">
        <Link :href="route('vehicles.show', vehicle.id)" class="btn btn-circle btn-outline btn-sm border-gray-300 hover:bg-gray-100 hover:border-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <h1 class="text-2xl font-bold text-gray-800">Servicerinnerung hinzufügen</h1>
      </div>
      <div class="h-1 w-24 bg-blue-500 mt-1 rounded-full"></div>
    </div>

    <form @submit.prevent="validateAndSubmit" class="space-y-4 max-w-7xl">
      <!-- Hauptinfo-Bereich -->
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
        <!-- Hauptinformationen -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-8">
          <!-- Header Abschnitt -->
          <div class="mb-4 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Grundinformationen</h2>
            </div>
          </div>

          <div class="flex items-center gap-3 mb-3">
            <span class="text-sm font-medium text-gray-600">Fahrzeug:</span>
            <div class="badge badge-lg py-2.5 px-3 border border-gray-300 font-medium text-gray-700 bg-gray-50">{{ vehicle.make }} {{ vehicle.model }}</div>
            <div v-if="vehicle.license_plate" class="badge badge-lg py-2.5 px-3 bg-blue-100 text-blue-800 border-blue-200 font-medium">{{ vehicle.license_plate }}</div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Linke Spalte -->
            <div class="space-y-4">
              <!-- Titel -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Titel <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.title"
                  type="text"
                  placeholder="z.B. Ölwechsel, Reifenwechsel..."
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.title }"
                />
                <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
                <div v-else-if="validationErrors.title" class="text-red-500 text-sm mt-1">{{ validationErrors.title }}</div>
              </div>

              <!-- Fälligkeitsdatum -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Fällig am
                </label>
                <input
                  v-model="form.due_date"
                  type="date"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.due_date || validationErrors.due_date }"
                />
                <div v-if="form.errors.due_date" class="text-red-500 text-sm mt-1">{{ form.errors.due_date }}</div>
                <div v-else-if="validationErrors.due_date" class="text-red-500 text-sm mt-1">{{ validationErrors.due_date }}</div>
              </div>
            </div>

            <!-- Rechte Spalte -->
            <div class="space-y-4">
              <!-- Priorität -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Priorität <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="form.priority"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.priority }"
                >
                  <option v-for="option in priorityOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                </select>
                <div v-if="form.errors.priority" class="text-red-500 text-sm mt-1">{{ form.errors.priority }}</div>
              </div>

              <!-- Status -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Status <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="form.status"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.status }"
                >
                  <option v-for="option in statusOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                </select>
                <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">{{ form.errors.status }}</div>
              </div>
            </div>
          </div>

          <!-- Zweite Zeile: Kilometer und Beschreibung -->
          <div class="mt-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Fällig bei Kilometerstand
              </label>
              <input
                v-model="form.due_mileage"
                type="number"
                min="0"
                placeholder="z.B. 50000"
                class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.due_mileage || validationErrors.due_mileage }"
              />
              <div v-if="form.errors.due_mileage" class="text-red-500 text-sm mt-1">{{ form.errors.due_mileage }}</div>
              <div v-else-if="validationErrors.due_mileage" class="text-red-500 text-sm mt-1">{{ validationErrors.due_mileage }}</div>
              <div class="text-xs text-gray-500 mt-1">Aktueller Kilometerstand: {{ vehicle.mileage.toLocaleString() }} km</div>
            </div>
          </div>
        </div>

        <!-- Hinweise Bereich -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-4">
          <div class="mb-4 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Hinweise</h2>
            </div>
          </div>

          <div class="space-y-3 p-2 bg-blue-50 rounded-lg text-sm text-blue-700">
            <p>
              <span class="font-medium">Tipp:</span> Geben Sie entweder ein Datum <strong>oder</strong> einen Kilometerstand an, bei dem die Erinnerung fällig sein soll.
            </p>
            <p>
              <span class="font-medium">Status:</span>
              <ul class="list-disc list-inside ml-2 mt-1">
                <li>Ausstehend: Noch zu erledigen</li>
                <li>Abgeschlossen: Bereits erledigt</li>
                <li>Überfällig: Nicht rechtzeitig erledigt</li>
              </ul>
            </p>
          </div>
        </div>
      </div>

      <!-- Akkordeonsektionen -->
      <div class="space-y-4">
        <!-- Beschreibung -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group" open>
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Beschreibung</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Detaillierte Beschreibung
              </label>
              <textarea
                v-model="form.description"
                class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 resize-none h-32"
                placeholder="Detaillierte Beschreibung der Servicerinnerung..."
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.description }"
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>
          </details>
        </div>

        <!-- Wiederholung -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group" open>
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Wiederholung</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <div class="mb-4">
                <label class="flex cursor-pointer justify-between p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                  <span class="text-sm font-medium text-gray-700">Erinnerung wiederholen</span>
                  <input
                    type="checkbox"
                    class="toggle toggle-primary"
                    :checked="showRepeatOptions"
                    @change="toggleRepeatOptions"
                  />
                </label>
              </div>

              <div v-if="showRepeatOptions" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 border-l-4 border-blue-200 pl-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Intervall <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.repeat_interval"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.repeat_interval || validationErrors.repeat_interval }"
                  />
                  <div v-if="form.errors.repeat_interval" class="text-red-500 text-sm mt-1">{{ form.errors.repeat_interval }}</div>
                  <div v-else-if="validationErrors.repeat_interval" class="text-red-500 text-sm mt-1">{{ validationErrors.repeat_interval }}</div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Einheit <span class="text-red-500">*</span>
                  </label>
                  <select
                    v-model="form.repeat_unit"
                    class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                    :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.repeat_unit || validationErrors.repeat_unit }"
                  >
                    <option value="">Bitte wählen</option>
                    <option v-for="option in repeatUnitOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                  </select>
                  <div v-if="form.errors.repeat_unit" class="text-red-500 text-sm mt-1">{{ form.errors.repeat_unit }}</div>
                  <div v-else-if="validationErrors.repeat_unit" class="text-red-500 text-sm mt-1">{{ validationErrors.repeat_unit }}</div>
                </div>
              </div>
            </div>
          </details>
        </div>
      </div>

      <!-- Submit-Bereich -->
      <div class="flex justify-end gap-3 mt-6 bg-white rounded-xl shadow-sm p-4 border border-gray-100">
        <Link :href="route('vehicles.show', props.vehicle.id)" class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 transition-colors duration-200">Abbrechen</Link>
        <button
          type="submit"
          class="px-6 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-200 flex items-center"
          :disabled="form.processing"
        >
          <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Speichern
        </button>
      </div>
    </form>
  </div>
</template>
