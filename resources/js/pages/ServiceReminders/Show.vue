<script setup lang="ts">
import { Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import axios from 'axios';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  serviceReminder: {
    id: number;
    title: string;
    description: string | null;
    due_date: string | null;
    due_mileage: number | null;
    status: string;
    priority: string;
    repeat_interval: number | null;
    repeat_unit: string | null;
    vehicle_id: number;
    vehicle: {
      id: number;
      make: string;
      model: string;
      license_plate: string | null;
      mileage: number;
    };
  };
}>();

// Status- und Prioritätslabels
const statusLabels: Record<string, string> = {
  'pending': 'Ausstehend',
  'completed': 'Abgeschlossen',
  'overdue': 'Überfällig'
};

const priorityLabels: Record<string, string> = {
  'high': 'Hoch',
  'medium': 'Mittel',
  'low': 'Niedrig'
};

const repeatUnitLabels: Record<string, string> = {
  'days': 'Tage',
  'weeks': 'Wochen',
  'months': 'Monate',
  'years': 'Jahre',
  'km': 'Kilometer'
};

// Format Helpers
const formatDate = (dateString: string | null) => {
  if (!dateString) return "-";

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

// CSS-Klassen basierend auf Status und Priorität
const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'badge-success';
    case 'overdue':
      return 'badge-error';
    case 'pending':
    default:
      return 'badge-warning';
  }
};

const getPriorityClass = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'badge-error';
    case 'medium':
      return 'badge-warning';
    case 'low':
    default:
      return 'badge-info';
  }
};

// Löschdialog
const showDeleteDialog = ref(false);

const confirmDelete = () => {
  showDeleteDialog.value = true;
};

const cancelDelete = () => {
  showDeleteDialog.value = false;
};

const deleteReminder = async () => {
  try {
    await axios.delete(route('service-reminders.destroy', props.serviceReminder.id));
    router.visit(route('vehicles.show', props.serviceReminder.vehicle_id));
  } catch (error) {
    alert('Fehler beim Löschen der Serviceerinnerung');
    console.error(error);
  }
};

const markAsCompleted = async () => {
  try {
    await axios.put(route('service-reminders.update', props.serviceReminder.id), {
      ...props.serviceReminder,
      status: 'completed'
    });
    router.reload();
  } catch (error) {
    alert('Fehler beim Aktualisieren des Status');
    console.error(error);
  }
};
</script>

<template>
  <div class="p-4 md:p-6 bg-gray-50">
    <!-- Header mit Stil -->
    <div class="mb-4">
      <div class="flex items-center gap-3 mb-1">
        <Link :href="route('vehicles.show', serviceReminder.vehicle_id)" class="btn btn-circle btn-outline btn-sm border-gray-300 hover:bg-gray-100 hover:border-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <h1 class="text-2xl font-bold text-gray-800">{{ serviceReminder.title }}</h1>
        <div class="badge" :class="getStatusClass(serviceReminder.status)">
          {{ statusLabels[serviceReminder.status] }}
        </div>
      </div>
      <div class="h-1 w-24 bg-blue-500 mt-1 rounded-full"></div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
      <!-- Hauptinformationen -->
      <div class="card bg-base-100 shadow-xl lg:col-span-8">
        <div class="card-body">
          <div class="flex justify-between mb-4">
            <h2 class="card-title">Details</h2>
            <div class="flex gap-2">
              <button v-if="serviceReminder.status !== 'completed'" @click="markAsCompleted" class="btn btn-sm btn-success">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"/></svg>
                Als erledigt markieren
              </button>
              <Link :href="route('service-reminders.edit', serviceReminder.id)" class="btn btn-sm btn-outline">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                Bearbeiten
              </Link>
              <button @click="confirmDelete" class="btn btn-sm btn-error">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                Löschen
              </button>
            </div>
          </div>

          <!-- Fahrzeuginformationen -->
          <div class="bg-gray-50 p-3 rounded-lg mb-4">
            <div class="flex items-center gap-3">
              <span class="text-sm font-medium text-gray-600">Fahrzeug:</span>
              <div class="badge badge-lg py-2.5 px-3 border border-gray-300 font-medium text-gray-700 bg-white">
                {{ serviceReminder.vehicle.make }} {{ serviceReminder.vehicle.model }}
              </div>
              <div v-if="serviceReminder.vehicle.license_plate" class="badge badge-lg py-2.5 px-3 bg-blue-100 text-blue-800 border-blue-200 font-medium">
                {{ serviceReminder.vehicle.license_plate }}
              </div>
              <Link :href="route('vehicles.show', serviceReminder.vehicle_id)" class="text-blue-500 hover:text-blue-700 text-sm ml-auto">
                <span class="flex items-center">
                  Zum Fahrzeug
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"></path><path d="M12 5l7 7-7 7"></path></svg>
                </span>
              </Link>
            </div>
          </div>

          <!-- Grunddaten -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="font-medium text-gray-700 mb-3">Grunddaten</h3>
              <div class="space-y-3">
                <div>
                  <div class="text-sm text-gray-500">Priorität</div>
                  <div class="badge mt-1" :class="getPriorityClass(serviceReminder.priority)">
                    {{ priorityLabels[serviceReminder.priority] }}
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Status</div>
                  <div class="badge mt-1" :class="getStatusClass(serviceReminder.status)">
                    {{ statusLabels[serviceReminder.status] }}
                  </div>
                </div>
                <div v-if="serviceReminder.description">
                  <div class="text-sm text-gray-500">Beschreibung</div>
                  <div class="mt-1 whitespace-pre-line text-gray-800">{{ serviceReminder.description }}</div>
                </div>
              </div>
            </div>

            <div>
              <h3 class="font-medium text-gray-700 mb-3">Fälligkeit</h3>
              <div class="space-y-3">
                <div v-if="serviceReminder.due_date">
                  <div class="text-sm text-gray-500">Fällig am</div>
                  <div class="mt-1 font-medium" :class="{ 'text-red-600': serviceReminder.status === 'overdue' }">
                    {{ formatDate(serviceReminder.due_date) }}
                  </div>
                </div>
                <div v-if="serviceReminder.due_mileage">
                  <div class="text-sm text-gray-500">Fällig bei Kilometerstand</div>
                  <div class="mt-1 font-medium" :class="{ 'text-red-600': serviceReminder.status === 'overdue' }">
                    {{ serviceReminder.due_mileage.toLocaleString() }} km
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    Aktueller Kilometerstand: {{ serviceReminder.vehicle.mileage.toLocaleString() }} km
                    <span v-if="serviceReminder.due_mileage > serviceReminder.vehicle.mileage" class="ml-1">
                      (noch {{ (serviceReminder.due_mileage - serviceReminder.vehicle.mileage).toLocaleString() }} km)
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Wiederholung -->
          <div v-if="serviceReminder.repeat_interval && serviceReminder.repeat_unit" class="mt-6">
            <div class="divider"></div>
            <h3 class="font-medium text-gray-700 mb-3">Wiederholung</h3>
            <div class="flex items-center gap-2">
              <div class="badge badge-primary">
                Alle {{ serviceReminder.repeat_interval }} {{ repeatUnitLabels[serviceReminder.repeat_unit] }}
              </div>
              <span class="text-sm text-gray-600">
                Nach Abschluss wird automatisch eine neue Erinnerung für den nächsten Service erstellt.
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Seitenleiste -->
      <div class="lg:col-span-4 space-y-4">
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title mb-4">Zeitlinie</h2>
            <ul class="steps steps-vertical">
              <li class="step step-primary">Erinnerung erstellt</li>
              <li class="step" :class="{ 'step-primary': serviceReminder.status !== 'pending' }">
                Service fällig
              </li>
              <li class="step" :class="{ 'step-primary': serviceReminder.status === 'completed' }">
                Service durchgeführt
              </li>
            </ul>
          </div>
        </div>

        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title mb-4">Aktionen</h2>
            <div class="space-y-2">
              <Link :href="route('service-reminders.edit', serviceReminder.id)" class="btn btn-outline w-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                Bearbeiten
              </Link>
              <button v-if="serviceReminder.status !== 'completed'" @click="markAsCompleted" class="btn btn-success w-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"/></svg>
                Als erledigt markieren
              </button>
              <button @click="confirmDelete" class="btn btn-error w-full">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                Löschen
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Löschdialog -->
    <div v-if="showDeleteDialog" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 class="text-lg font-bold mb-4">Serviceerinnerung löschen</h3>
        <p class="mb-6">Sind Sie sicher, dass Sie diese Serviceerinnerung löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.</p>
        <div class="flex justify-end gap-2">
          <button @click="cancelDelete" class="btn btn-outline">Abbrechen</button>
          <button @click="deleteReminder" class="btn btn-error">Löschen</button>
        </div>
      </div>
    </div>
  </div>
</template>
