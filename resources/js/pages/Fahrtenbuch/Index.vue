<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import { Plus, Download, Filter, Search, FileText, AlertTriangle, Eye, Pencil, Trash, ChevronDown } from 'lucide-vue-next';
import AppLayout from '@/layouts/AppLayout.vue';
import PageHeading from '@/components/PageHeading.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { 
  Calendar as CalendarComponent 
} from '@/components/ui/calendar';
import { useToast } from '@/components/ui/toast/use-toast'; // Importiere useToast
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const props = defineProps({
  trips: Array,
  vehicles: Array,
  months: Array
});

const page = usePage();

const searchQuery = ref('');
const selectedVehicle = ref('all');
const selectedTripType = ref('all');
const selectedMonth = ref(format(new Date(), 'yyyy-MM'));
const selectedTab = ref('all');
const showAddTripDialog = ref(false);
const vehicles = ref(props.vehicles || []);
const localTrips = ref([]);
const itemsPerPage = ref(10);
const currentPage = ref(1);

const { toast } = useToast(); // Initialisiere toast

const date = ref(new Date());
const showDatePicker = ref(false);
const showEndDatePicker = ref(false);
const showEditDatePicker = ref(false);
const showEditEndDatePicker = ref(false);

// Watcher für Flash-Nachrichten
watch(() => page.props.flash, (flash) => {
  if (flash) {
    if (flash.success) {
      toast({
        title: 'Erfolg',
        description: flash.success,
        variant: 'default', // Oder 'success', falls definiert
      });
    }
    if (flash.error) {
      toast({
        title: 'Fehler',
        description: flash.error,
        variant: 'destructive',
      });
    }
    // Setze die Flash-Nachrichten zurück, um sie nicht erneut anzuzeigen
    // Dies ist wichtig, wenn Inertia die Props nicht automatisch löscht
    page.props.flash = null;
  }
}, { deep: true });

// Validierungsfehler
const formErrors = ref({
  date: '',
  vehicleId: '',
  startOdometer: '',
  endOdometer: '',
  startLocation: '',
  endLocation: '',
  purpose: '',
  type: '',
  recurrenceEndDate: ''
});

// Definiere die Fahrtarten
const tripTypes = ref([
  { id: 'all', name: 'Alle Fahrten' },
  { id: 'business', name: 'Geschäftlich', badgeColor: 'blue' },
  { id: 'private', name: 'Privat', badgeColor: 'sky' },
  { id: 'commute', name: 'Arbeitsweg', badgeColor: 'green' }
]);

// Formular für eine neue Fahrt
const newTrip = reactive({
  date: format(new Date(), 'yyyy-MM-dd'),
  vehicleId: '',
  startOdometer: '',
  endOdometer: '',
  startLocation: '',
  endLocation: '',
  purpose: '',
  type: 'business',
  notes: '',
  isRecurring: false,
  recurrenceType: 'daily',
  recurrenceEndDate: format(new Date(new Date().setMonth(new Date().getMonth() + 1)), 'yyyy-MM-dd'),
  weekdays: {
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: false,
    sunday: false
  }
});

// Hilfsfunktion zum Konvertieren von String zu Date
const parseDate = (dateString) => {
  if (!dateString) return new Date();
  const parsedDate = new Date(dateString);
  return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
};

// Hilfsfunktion zum Formatieren des Datums für die Anzeige
const formatDateForDisplay = (dateString) => {
  try {
    const date = parseDate(dateString);
    return format(date, 'dd.MM.yyyy', { locale: de });
  } catch (e) {
    console.error('Fehler beim Formatieren des Datums:', e);
    return format(new Date(), 'dd.MM.yyyy', { locale: de });
  }
};

// Ersetze die vorhandene formatDate Funktion
const formatDate = formatDateForDisplay;

// Update die Datums-Handler
const handleDateSelect = (selectedDate, field, trip = null) => {
  try {
    const formattedDate = format(selectedDate, 'yyyy-MM-dd');
    if (trip) {
      trip[field] = formattedDate;
    } else {
      newTrip[field] = formattedDate;
    }
    
    // Close the appropriate date picker
    if (field === 'date') {
      if (trip) {
        showEditDatePicker.value = false;
      } else {
        showDatePicker.value = false;
      }
    } else if (field === 'recurrenceEndDate') {
      if (trip) {
        showEditEndDatePicker.value = false;
      } else {
        showEndDatePicker.value = false;
      }
    }
  } catch (e) {
    console.error('Fehler beim Aktualisieren des Datums:', e);
  }
};

// Einträge pro Seite Optionen
const availablePageSizes = [
  { value: 10, label: '10 Einträge' },
  { value: 25, label: '25 Einträge' },
  { value: 50, label: '50 Einträge' },
  { value: 100, label: 'Alle Einträge' }
];

// Pagination-Hilfsfunktionen
const totalItems = computed(() => {
  const total = filteredTrips.value?.length || 0;
  return Number.isInteger(total) ? total : 0;
});

const totalPages = computed(() => Math.max(1, Math.ceil(totalItems.value / itemsPerPage.value)));

const paginationText = computed(() => {
  if (totalItems.value === 0) return 'Keine Einträge';
  
  const start = ((currentPage.value - 1) * itemsPerPage.value) + 1;
  const end = Math.min(currentPage.value * itemsPerPage.value, totalItems.value);
  
  return `Zeige ${start} bis ${end} von ${totalItems.value} Einträgen`;
});

const currentPageItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return (filteredTrips.value || []).slice(start, end);
});

// Gefilterte Fahrten mit Sicherheitscheck
const filteredTrips = computed(() => {
  const tripsToFilter = localTrips.value || [];
  return tripsToFilter.filter(trip => {
    if (!trip) return false;
    
    const searchTerms = searchQuery.value.toLowerCase();
    const matchesSearch = !searchQuery.value || 
      (trip.startLocation && trip.startLocation.toLowerCase().includes(searchTerms)) ||
      (trip.endLocation && trip.endLocation.toLowerCase().includes(searchTerms)) ||
      (trip.purpose && trip.purpose.toLowerCase().includes(searchTerms));
    
    const matchesVehicle = selectedVehicle.value === 'all' || 
      Number(trip.vehicleId) === Number(selectedVehicle.value);
    
    const tripMonth = trip.date ? trip.date.substring(0, 7) : '';
    const matchesMonth = selectedMonth.value === 'all' || 
      tripMonth === selectedMonth.value;

    const matchesTripType = selectedTripType.value === 'all' || 
      trip.type === selectedTripType.value;
    
    return matchesSearch && matchesVehicle && matchesMonth && matchesTripType;
  }).sort((a, b) => {
    // First compare by ID in descending order (assuming higher ID = more recently added)
    if (a.id !== b.id) {
      return b.id - a.id;
    }
    // If IDs are equal (unlikely), fall back to date comparison
    return new Date(b.date) - new Date(a.date);
  });
});

// Gefilterte und paginierte Fahrten mit Sicherheitscheck
const getFilteredTripsByType = (type) => {
  let trips = filteredTrips.value;
  
  // Wenn nicht "all", dann nach Typ filtern
  if (type !== 'all') {
    trips = trips.filter(trip => trip.type === type);
  }

  if (!trips || trips.length === 0) return [];
  
  if (itemsPerPage.value === 100) {
    return trips;
  }

  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return trips.slice(start, end);
};

// Gesamtanzahl der gefilterten Fahrten mit Sicherheitscheck
const getTotalFilteredTrips = (type) => {
  if (!filteredTrips.value) return 0;
  return type === 'all' 
    ? filteredTrips.value.length 
    : filteredTrips.value.filter(trip => trip?.type === type).length;
};

// Gesamtanzahl der Seiten für den aktuellen Filter
const getTotalPages = (type) => {
  const totalTrips = getTotalFilteredTrips(type);
  if (itemsPerPage.value === 100 || totalTrips === 0) return 1;
  return Math.ceil(totalTrips / itemsPerPage.value);
};

// Funktion zum Ändern der Seite
const changePage = (newPage) => {
  currentPage.value = newPage;
};

// Funktion zum Ändern der Einträge pro Seite
const changeItemsPerPage = (value) => {
  itemsPerPage.value = value;
  currentPage.value = 1;
};

// Watch für itemsPerPage
watch(itemsPerPage, (newValue) => {
  changeItemsPerPage(newValue);
});

// Kilometerdistanz einer Fahrt berechnen
const getDistance = (trip) => {
  return Number(trip.endOdometer) - Number(trip.startOdometer);
};

// Distanz formatieren
const formatDistance = (distance) => {
  if (typeof distance !== 'number') return '0';
  return distance.toLocaleString('de-DE') + ' km';
};

// Gesamtdistanz berechnen
const calculateTotalDistance = (trips) => {
  if (!Array.isArray(trips)) return 0;
  return trips.reduce((total, trip) => total + getDistance(trip), 0);
};

// Badge für Fahrttyp abrufen
const getTripTypeBadge = (type) => {
  const tripType = tripTypes.value.find(t => t.id === type);
  return tripType ? tripType.badgeColor : 'gray';
};

// Fahrttyp-Name abrufen
const getTripTypeName = (type) => {
  const tripType = tripTypes.value.find(t => t.id === type);
  return tripType ? tripType.name : type;
};

// Fahrzeugname abrufen
const getVehicleName = (vehicleId) => {
  const vehicle = vehicles.value.find(v => v.id === vehicleId);
  return vehicle ? vehicle.name : 'Unbekannt';
};

// Statistiken für ausgewählte Fahrten berechnen
const statistics = computed(() => {
  const businessTrips = filteredTrips.value.filter(t => t.type === 'business');
  const privateTrips = filteredTrips.value.filter(t => t.type === 'private');
  const commuteTrips = filteredTrips.value.filter(t => t.type === 'commute');
  
  const totalBusinessKm = businessTrips.reduce((sum, t) => sum + getDistance(t), 0);
  const totalPrivateKm = privateTrips.reduce((sum, t) => sum + getDistance(t), 0);
  const totalCommuteKm = commuteTrips.reduce((sum, t) => sum + getDistance(t), 0);
  
  return {
    totalTrips: filteredTrips.value.length,
    businessTrips: businessTrips.length,
    privateTrips: privateTrips.length,
    commuteTrips: commuteTrips.length,
    totalKm: totalBusinessKm + totalPrivateKm + totalCommuteKm,
    businessKm: totalBusinessKm,
    privateKm: totalPrivateKm,
    commuteKm: totalCommuteKm
  };
});

// Tab wechseln - Methode, die sicherstellt, dass der richtige Tab aktiviert wird
const switchToTab = (tabId) => {
  console.log('Tab-Wechsel zu:', tabId);
  selectedTab.value = tabId;
  
  // URL-Aktualisierung entfernt, damit es nicht dauerhaft gespeichert wird
  // Die URL-Manipulation führt zu dauerhafter Filterung
};

// Beobachte Änderungen in selectedTab
watch(() => selectedTab.value, (newTab) => {
  console.log('Tab wurde gewechselt zu:', newTab);
  currentPage.value = 1; // Reset pagination when changing tabs
}, { immediate: true });

// Lade die Daten aus den Props
onMounted(() => {
  console.log('Component mounted');
  
  // Starte mit den Daten aus den Props
  if (props.trips && Array.isArray(props.trips)) {
    console.log('Initializing trips:', props.trips.length);
    localTrips.value = JSON.parse(JSON.stringify(props.trips));
  }
  
  if (props.vehicles && props.vehicles.length > 0) {
    vehicles.value = JSON.parse(JSON.stringify(props.vehicles));
  }
  
  // Setze alle Filter zurück
  resetFilters();
  
  // Immer mit "Alle Fahrten" Tab starten
  selectedTab.value = 'all';
  
  nextTick(() => {
    console.log('After nextTick - Selected tab:', selectedTab.value);
    console.log('Filtered trips:', filteredTrips.value.length);
  });
});

// Beobachte Änderungen in den Props
watch(() => props.trips, (newTrips) => {
  if (newTrips && Array.isArray(newTrips)) {
    console.log('Props haben sich geändert, aktualisiere lokale Daten:', newTrips.length);
    // Tiefenkopie erstellen, um Referenzprobleme zu vermeiden
    localTrips.value = JSON.parse(JSON.stringify(newTrips));
  }
}, { deep: true });

// Zusätzliche Funktion zum expliziten Zurücksetzen aller Filter
const resetFilters = () => {
  searchQuery.value = '';
  selectedVehicle.value = 'all';
  selectedTripType.value = 'all';
  selectedMonth.value = 'all'; // Änderung: Zeige alle Monate an, nicht nur den aktuellen
  itemsPerPage.value = 10;
  currentPage.value = 1;
};

// Neue Fahrt einreichen
const submitTrip = () => {
  if (!validateForm()) return;
  
  router.post('/fahrtenbuch/trips', {
    date: newTrip.date,
    vehicleId: newTrip.vehicleId,
    startOdometer: parseInt(newTrip.startOdometer),
    endOdometer: parseInt(newTrip.endOdometer),
    startLocation: newTrip.startLocation,
    endLocation: newTrip.endLocation,
    purpose: newTrip.purpose,
    type: newTrip.type,
    notes: newTrip.notes,
    isRecurring: newTrip.isRecurring,
    recurrenceType: newTrip.recurrenceType,
    recurrenceEndDate: newTrip.recurrenceEndDate,
    weekdays: newTrip.isRecurring && newTrip.recurrenceType === 'weekly' ? newTrip.weekdays : null
  }, {
    preserveScroll: true,
    onSuccess: (page) => {
      // Dialog schließen
      showAddTripDialog.value = false;
      
      // Setze alle Filter zurück
      resetFilters();
      
      // Zum "Alle Fahrten" Tab wechseln
      selectedTab.value = 'all';
      
      // Aktualisiere die Daten
      if (page.props.trips && Array.isArray(page.props.trips)) {
        localTrips.value = JSON.parse(JSON.stringify(page.props.trips));
      }
      
      // Erfolgsmeldung anzeigen
      toast({
        title: 'Erfolg',
        description: 'Fahrt wurde erfolgreich erstellt.',
        variant: 'success',
      });
      
      // Formular zurücksetzen
      Object.keys(newTrip).forEach(key => {
        if (key === 'date') {
          // Datum bleibt auf aktuellem Datum
          newTrip[key] = format(new Date(), 'yyyy-MM-dd');
        } else if (key === 'type') {
          // Typ bleibt 'business'
          newTrip[key] = 'business';
        } else if (key === 'isRecurring') {
          // Regelmäßige Fahrt zurücksetzen
          newTrip[key] = false;
        } else if (key === 'recurrenceType') {
          // Wiederholungstyp zurücksetzen
          newTrip[key] = 'daily';
        } else if (key === 'recurrenceEndDate') {
          // Enddatum auf einen Monat ab heute setzen
          newTrip[key] = format(new Date(new Date().setMonth(new Date().getMonth() + 1)), 'yyyy-MM-dd');
        } else {
          // Alle anderen Felder leeren
          newTrip[key] = '';
        }
      });
      
      nextTick(() => {
        console.log('After submit - Selected tab:', selectedTab.value);
        console.log('Filtered trips:', filteredTrips.value.length);
      });
    },
    onError: (errors) => {
      console.error('Fehler beim Speichern:', errors);
      if (errors) {
        Object.keys(errors).forEach(key => {
          if (formErrors.value.hasOwnProperty(key)) {
            formErrors.value[key] = errors[key];
          }
        });
      }
    }
  });
};

// Export zu CSV/PDF
const exportFahrtenbuch = (format) => {
  // Sammeln der aktuellen Filterkriterien
  const params = new URLSearchParams();
  
  // Füge Filterkriterien als Query-Parameter hinzu
  if (selectedVehicle.value !== 'all') {
    params.append('vehicleId', selectedVehicle.value);
  }
  
  if (selectedMonth.value !== 'all') {
    // Format: yyyy-MM → yyyy-MM-01 für Startdatum und yyyy-MM-LastDay für Enddatum
    const year = selectedMonth.value.split('-')[0];
    const month = selectedMonth.value.split('-')[1];
    const startDate = `${selectedMonth.value}-01`;
    
    // Berechne den letzten Tag des Monats
    const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();
    const endDate = `${selectedMonth.value}-${lastDay}`;
    
    params.append('startDate', startDate);
    params.append('endDate', endDate);
  } else {
    // Wenn "Alle Monate" ausgewählt ist, setze explizit exportAll=true
    params.append('exportAll', 'true');
  }
  
  // Toast Benachrichtigung anzeigen
  toast({
    title: 'Export gestartet',
    description: `Fahrtenbuch wird als ${format.toUpperCase()} exportiert...`,
    variant: 'default',
  });
  
  // Download des Exports starten
  window.location.href = `/fahrtenbuch/export/${format}?${params.toString()}`;
};

// Daten aktualisieren, wenn die Props sich ändern
watch(() => props.vehicles, (newVehicles) => {
  if (newVehicles && newVehicles.length > 0) {
    vehicles.value = JSON.parse(JSON.stringify(newVehicles));
  }
}, { deep: true });

// Neue Funktion zur Kilometerberechnung nach Fahrttyp
const getKilometersByType = (type) => {
  const trips = getFilteredTripsByType(type);
  return trips.reduce((sum, t) => sum + getDistance(t), 0);
};

// Neue Funktion zur Tab-Wechsel-Verarbeitung
const handleTabChange = (newTab) => {
  console.log('Tab wurde gewechselt zu:', newTab);
  selectedTab.value = newTab;
};

// Validiere das Formular
const validateForm = () => {
  let isValid = true;
  
  // Setze alle Fehler zurück
  Object.keys(formErrors.value).forEach(key => {
    formErrors.value[key] = '';
  });

  // Pflichtfelder prüfen
  if (!newTrip.date) {
    formErrors.value.date = 'Datum ist erforderlich';
    isValid = false;
  }

  if (!newTrip.vehicleId) {
    formErrors.value.vehicleId = 'Fahrzeug ist erforderlich';
    isValid = false;
  }

  if (!newTrip.startOdometer) {
    formErrors.value.startOdometer = 'Kilometerstand Beginn ist erforderlich';
    isValid = false;
  } else if (isNaN(parseInt(newTrip.startOdometer))) {
    formErrors.value.startOdometer = 'Bitte geben Sie eine gültige Zahl ein';
    isValid = false;
  }

  if (!newTrip.endOdometer) {
    formErrors.value.endOdometer = 'Kilometerstand Ende ist erforderlich';
    isValid = false;
  } else if (isNaN(parseInt(newTrip.endOdometer))) {
    formErrors.value.endOdometer = 'Bitte geben Sie eine gültige Zahl ein';
    isValid = false;
  } else if (parseInt(newTrip.endOdometer) <= parseInt(newTrip.startOdometer)) {
    formErrors.value.endOdometer = 'Kilometerstand Ende muss größer als Beginn sein';
    isValid = false;
  }

  if (!newTrip.startLocation) {
    formErrors.value.startLocation = 'Startort ist erforderlich';
    isValid = false;
  }

  if (!newTrip.endLocation) {
    formErrors.value.endLocation = 'Zielort ist erforderlich';
    isValid = false;
  }

  if (!newTrip.purpose) {
    formErrors.value.purpose = 'Zweck der Fahrt ist erforderlich';
    isValid = false;
  }

  if (!newTrip.type) {
    formErrors.value.type = 'Art der Fahrt ist erforderlich';
    isValid = false;
  }

  // Validierung für regelmäßige Fahrten
  if (newTrip.isRecurring) {
    if (!newTrip.recurrenceEndDate) {
      formErrors.value.recurrenceEndDate = 'Enddatum für regelmäßige Fahrt ist erforderlich';
      isValid = false;
    } else {
      const startDate = new Date(newTrip.date);
      const endDate = new Date(newTrip.recurrenceEndDate);
      
      if (endDate <= startDate) {
        formErrors.value.recurrenceEndDate = 'Enddatum muss nach dem Startdatum liegen';
        isValid = false;
      }
    }
  }

  return isValid;
};

// Neue Refs am Anfang des Script-Bereichs nach den bestehenden refs
const showEditTripDialog = ref(false);
const showDetailsTripDialog = ref(false);
const showDeleteDialog = ref(false);
const selectedTrip = ref(null);

// Neue Methoden vor dem Template
const editTrip = (trip) => {
  const tripCopy = JSON.parse(JSON.stringify(trip));
  // Initialisiere Wochentage, falls sie nicht existieren
  if (!tripCopy.weekdays) {
    tripCopy.weekdays = {
      monday: true,
      tuesday: true,
      wednesday: true,
      thursday: true,
      friday: true,
      saturday: false,
      sunday: false
    };
  }
  selectedTrip.value = tripCopy;
  showEditTripDialog.value = true;
};

const showDetails = (trip) => {
  selectedTrip.value = trip;
  showDetailsTripDialog.value = true;
};

const confirmDelete = (trip) => {
  selectedTrip.value = trip;
  showDeleteDialog.value = true;
};

const deleteTrip = (deleteAll = false) => {
  if (!selectedTrip.value) return;
  
  const url = deleteAll 
    ? `/fahrtenbuch/trips/${selectedTrip.value.id}/delete-recurring`
    : `/fahrtenbuch/trips/${selectedTrip.value.id}`;
  
  router.delete(url, {
    preserveScroll: true,
    onSuccess: () => {
      showDeleteDialog.value = false;
      selectedTrip.value = null;
      toast({
        title: 'Erfolg',
        description: deleteAll 
          ? 'Alle wiederholten Fahrten wurden erfolgreich gelöscht.'
          : 'Fahrt wurde erfolgreich gelöscht.',
        variant: 'success',
      });
    },
    onError: () => {
      toast({
        title: 'Fehler',
        description: 'Fehler beim Löschen der Fahrt.',
        variant: 'destructive',
      });
    }
  });
};

// Add this function near the validateForm function
const validateEditForm = (trip) => {
  let isValid = true;
  
  // Reset all errors
  Object.keys(formErrors.value).forEach(key => {
    formErrors.value[key] = '';
  });

  // Check required fields for edit form
  if (!trip.date) {
    formErrors.value.date = 'Datum ist erforderlich';
    isValid = false;
  }

  if (!trip.vehicleId) {
    formErrors.value.vehicleId = 'Fahrzeug ist erforderlich';
    isValid = false;
  }

  if (!trip.startOdometer && trip.startOdometer !== 0) {
    formErrors.value.startOdometer = 'Kilometerstand Beginn ist erforderlich';
    isValid = false;
  } else if (isNaN(parseInt(trip.startOdometer))) {
    formErrors.value.startOdometer = 'Bitte geben Sie eine gültige Zahl ein';
    isValid = false;
  }

  if (!trip.endOdometer && trip.endOdometer !== 0) {
    formErrors.value.endOdometer = 'Kilometerstand Ende ist erforderlich';
    isValid = false;
  } else if (isNaN(parseInt(trip.endOdometer))) {
    formErrors.value.endOdometer = 'Bitte geben Sie eine gültige Zahl ein';
    isValid = false;
  } else if (parseInt(trip.endOdometer) <= parseInt(trip.startOdometer)) {
    formErrors.value.endOdometer = 'Kilometerstand Ende muss größer als Beginn sein';
    isValid = false;
  }

  if (!trip.startLocation) {
    formErrors.value.startLocation = 'Startort ist erforderlich';
    isValid = false;
  }

  if (!trip.endLocation) {
    formErrors.value.endLocation = 'Zielort ist erforderlich';
    isValid = false;
  }

  if (!trip.purpose) {
    formErrors.value.purpose = 'Zweck der Fahrt ist erforderlich';
    isValid = false;
  }

  if (!trip.type) {
    formErrors.value.type = 'Art der Fahrt ist erforderlich';
    isValid = false;
  }

  return isValid;
};

// Update the updateTrip function to use a specific update route instead of PUT
const updateTrip = () => {
  if (!selectedTrip.value) return;
  
  // Use the specialized edit form validation
  if (!validateEditForm(selectedTrip.value)) return;
  
  // Use a specific update route instead of PUT
  router.post(`/fahrtenbuch/trips/${selectedTrip.value.id}/update`, {
    date: selectedTrip.value.date,
    vehicleId: selectedTrip.value.vehicleId,
    startOdometer: parseInt(selectedTrip.value.startOdometer),
    endOdometer: parseInt(selectedTrip.value.endOdometer),
    startLocation: selectedTrip.value.startLocation,
    endLocation: selectedTrip.value.endLocation,
    purpose: selectedTrip.value.purpose,
    type: selectedTrip.value.type,
    notes: selectedTrip.value.notes || '',
    isRecurring: selectedTrip.value.isRecurring,
    recurrenceType: selectedTrip.value.recurrenceType,
    recurrenceEndDate: selectedTrip.value.recurrenceEndDate,
    weekdays: selectedTrip.value.isRecurring && selectedTrip.value.recurrenceType === 'weekly' 
      ? selectedTrip.value.weekdays 
      : null
  }, {
    preserveScroll: true,
    onSuccess: () => {
      showEditTripDialog.value = false;
      selectedTrip.value = null;
      
      // Erfolgsmeldung anzeigen
      toast({
        title: 'Erfolg',
        description: 'Fahrt wurde erfolgreich aktualisiert.',
        variant: 'success',
      });
      
      // Delay the page reload to give the toast time to be seen
      setTimeout(() => {
        router.reload();
      }, 3000);
    },
    onError: (errors) => {
      console.error('Fehler beim Aktualisieren:', errors);
      if (errors) {
        Object.keys(errors).forEach(key => {
          if (formErrors.value.hasOwnProperty(key)) {
            formErrors.value[key] = errors[key];
          }
        });
      }
    }
  });
};
</script>

<template>
  <AppLayout>
    <Head title="Fahrtenbuch" />
    
    <div class="container mx-auto px-6 py-6">
    <PageHeading
      title="Fahrtenbuch"
      subtitle="Dokumentieren Sie alle Fahrten nach steuerlichen Vorgaben"
    >
      <template #actions>
          <div class="flex items-center gap-2">
            <Button 
              variant="default" 
              size="sm" 
              class="bg-primary text-white hover:bg-primary/90"
              @click="showAddTripDialog = true"
            >
              <Plus class="h-4 w-4 mr-2" />
              Fahrt hinzufügen
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm"
                >
                  <Download class="h-4 w-4 mr-2" />
                  Exportieren
                  <ChevronDown class="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem @click="exportFahrtenbuch('pdf')">
                  <FileText class="h-4 w-4 mr-2" />
                  Als PDF exportieren
                </DropdownMenuItem>
                <DropdownMenuItem @click="exportFahrtenbuch('csv')">
                  <FileText class="h-4 w-4 mr-2" />
                  Als CSV exportieren
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </template>
      </PageHeading>

      <!-- Stats Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium text-gray-600">Gesamtkilometer</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="flex items-baseline">
              <div class="text-2xl font-bold text-primary">{{ statistics.totalKm }}</div>
              <div class="ml-1 text-sm text-gray-600">km</div>
            </div>
            <p class="text-xs text-gray-500 mt-1">
              Insgesamt {{ statistics.totalTrips }} Fahrten
            </p>
          </CardContent>
        </Card>

        <Card class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium text-gray-600">Geschäftlich</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="flex items-baseline">
              <div class="text-2xl font-bold text-blue-600">{{ statistics.businessKm }}</div>
              <div class="ml-1 text-sm text-gray-600">km</div>
            </div>
            <p class="text-xs text-gray-500 mt-1">
              {{ statistics.businessTrips }} geschäftliche Fahrten
            </p>
          </CardContent>
        </Card>

        <Card class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium text-gray-600">Privat</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="flex items-baseline">
              <div class="text-2xl font-bold text-gray-600">{{ statistics.privateKm }}</div>
              <div class="ml-1 text-sm text-gray-600">km</div>
            </div>
            <p class="text-xs text-gray-500 mt-1">
              {{ statistics.privateTrips }} private Fahrten
            </p>
          </CardContent>
        </Card>

        <Card class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <CardHeader class="pb-2">
            <CardTitle class="text-sm font-medium text-gray-600">Arbeitsweg</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="flex items-baseline">
              <div class="text-2xl font-bold text-green-600">{{ statistics.commuteKm }}</div>
              <div class="ml-1 text-sm text-gray-600">km</div>
            </div>
            <p class="text-xs text-gray-500 mt-1">
              {{ statistics.commuteTrips }} Fahrten zur Arbeit
            </p>
          </CardContent>
        </Card>
      </div>

      <!-- Filters -->
      <Card class="mb-6">
        <CardContent class="p-4">
          <div class="flex flex-col md:flex-row gap-4">
            <!-- Search Input -->
            <div class="w-full md:w-1/3">
              <div class="relative">
                <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input 
                  v-model="searchQuery"
                  placeholder="Suche nach Start, Ziel oder Zweck..." 
                  class="pl-10 w-full"
                />
              </div>
            </div>
            
            <!-- Filter Dropdowns -->
            <div class="flex flex-wrap gap-4 w-full md:w-2/3 justify-end">
              <!-- Vehicle Filter -->
              <div class="w-[200px]">
                <select
                  v-model="selectedVehicle"
                  class="w-full h-10 rounded-md border border-input bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="all">Alle Fahrzeuge</option>
                  <option 
                    v-for="vehicle in vehicles" 
                    :key="vehicle.id" 
                    :value="vehicle.id"
                  >
                    {{ vehicle.name }}
                  </option>
                </select>
              </div>

              <!-- Trip Type Filter -->
              <div class="w-[200px]">
                <select
                  v-model="selectedTripType"
                  class="w-full h-10 rounded-md border border-input bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option 
                    v-for="type in tripTypes" 
                    :key="type.id" 
                    :value="type.id"
                  >
                    {{ type.name }}
                  </option>
                </select>
              </div>

              <!-- Month Filter -->
              <div class="w-[150px]">
                <select
                  v-model="selectedMonth"
                  class="w-full h-10 rounded-md border border-input bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="all">Alle Monate</option>
                  <option 
                    v-for="month in months" 
                    :key="month"
                    :value="month"
                  >
                    {{ formatDate(month + '-01').split('.').slice(1).join('.') }}
                  </option>
                </select>
              </div>

              <!-- Entries per Page -->
              <div class="w-[150px]">
                <select
                  v-model="itemsPerPage"
                  class="w-full h-10 rounded-md border border-input bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option 
                    v-for="size in availablePageSizes" 
                    :key="size.value" 
                    :value="size.value"
                  >
                    {{ size.label }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Tabellen-Container -->
      <div v-if="currentPageItems.length > 0" class="space-y-4">
        <!-- Tabelle -->
        <div class="overflow-x-auto">
          <table class="w-full table-auto">
            <thead>
              <tr class="bg-gray-50">
                <th class="p-2 text-left">Datum</th>
                <th class="p-2 text-left">Start</th>
                <th class="p-2 text-left">Ziel</th>
                <th class="p-2 text-left">Zweck</th>
                <th class="p-2 text-left">Art</th>
                <th class="p-2 text-left">Regelmäßig</th>
                <th class="p-2 text-right">Kilometer</th>
                <th class="p-2 text-left">Notizen</th>
                <th class="p-2 text-right">Aktionen</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="trip in currentPageItems" 
                :key="trip.id"
                class="border-b hover:bg-gray-50"
              >
                <td class="p-2">{{ formatDate(trip.date) }}</td>
                <td class="p-2">{{ trip.startLocation }}</td>
                <td class="p-2">{{ trip.endLocation }}</td>
                <td class="p-2">{{ trip.purpose }}</td>
                <td class="p-2">
                  <Badge :variant="getTripTypeBadge(trip.type)">
                    {{ getTripTypeName(trip.type) }}
                  </Badge>
                </td>
                <td class="p-2">
                  <Badge v-if="trip.isRecurring" variant="purple" class="bg-purple-500 text-white">
                    {{ trip.recurrenceType === 'daily' ? 'Täglich' : 'Wöchentlich' }}
                    bis {{ formatDate(trip.recurrenceEndDate) }}
                  </Badge>
                </td>
                <td class="p-2 text-right">{{ formatDistance(getDistance(trip)) }}</td>
                <td class="p-2">
                  <div v-if="trip.notes" class="relative group">
                    <div class="cursor-help truncate max-w-[200px]">
                      {{ trip.notes }}
                    </div>
                    <div v-if="trip.notes.length > 30" 
                         class="absolute z-50 hidden group-hover:block bg-white border rounded-md shadow-lg p-3 min-w-[200px] max-w-[400px] whitespace-normal">
                      {{ trip.notes }}
                    </div>
                  </div>
                </td>
                <td class="p-2 text-right">
                  <div class="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="showDetails(trip)"
                      class="text-gray-600 hover:text-gray-900"
                    >
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="editTrip(trip)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      <Pencil class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="confirmDelete(trip)"
                      class="text-red-600 hover:text-red-900"
                    >
                      <Trash class="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr class="font-bold bg-gray-50">
                <td colspan="4" class="p-2 text-right">Gesamtkilometer:</td>
                <td colspan="2"></td>
                <td class="p-2 text-right">
                  {{ formatDistance(calculateTotalDistance(currentPageItems)) }}
                </td>
                <td colspan="2"></td>
              </tr>
            </tfoot>
          </table>
        </div>

        <!-- Pagination -->
        <div class="flex justify-between items-center mt-4">
          <div class="text-sm text-gray-500">
            {{ paginationText }}
          </div>
          <div class="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = 1"
              :disabled="currentPage <= 1"
            >
              «
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage--"
              :disabled="currentPage <= 1"
            >
              ‹
            </Button>
            <span class="px-3 py-1 text-sm bg-gray-50 border rounded-md">
              Seite {{ currentPage }} von {{ totalPages.value }}
            </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage++"
              :disabled="currentPage >= totalPages.value"
            >
              ›
            </Button>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = totalPages.value"
              :disabled="currentPage >= totalPages.value"
            >
              »
            </Button>
          </div>
        </div>
      </div>

      <!-- Keine Daten Anzeige -->
      <div 
        v-else
        class="text-center py-8 text-gray-500"
      >
        Keine Fahrten gefunden.
      </div>

      <!-- Tax Info Card with increased margin -->
      <Card class="border-amber-200 bg-amber-50 mt-8">
        <CardContent class="p-4 flex items-start gap-3">
          <AlertTriangle class="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 class="font-medium text-amber-900">Hinweis zur steuerlichen Anerkennung</h3>
            <p class="text-sm mt-1 text-amber-800">
              Für die steuerliche Anerkennung des Fahrtenbuchs müssen alle Eintragungen zeitnah, vollständig und fortlaufend erfolgen. 
              Achten Sie auf die korrekte Angabe von Datum, Kilometerständen, Start-/Zielorten sowie dem Zweck der Fahrt.
              Dieses digitale Fahrtenbuch entspricht den Anforderungen des Finanzamts, da es alle notwendigen Angaben lückenlos und unveränderbar dokumentiert. 
              Die PDF-Exporte enthalten steuerlich anerkannte Nachweise und können bei einer Prüfung vorgelegt werden.
            </p>
          </div>
        </CardContent>
      </Card>

      <!-- Add/Edit Trip Dialog -->
      <Dialog v-model:open="showAddTripDialog">
          <DialogContent class="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Neue Fahrt erfassen</DialogTitle>
              <DialogDescription>
                Erfassen Sie alle notwendigen Informationen für eine steuerlich anerkannte Fahrt.
              </DialogDescription>
            </DialogHeader>
            
            <div class="grid gap-4 py-4">
              <div class="grid grid-cols-2 gap-4">
                <!-- Date -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Datum <span class="text-red-500">*</span></label>
                  <div class="relative">
                    <Button
                      variant="outline"
                      class="w-full justify-start text-left font-normal"
                      :class="{ 'border-red-500': formErrors.date }"
                      @click="showDatePicker = !showDatePicker"
                    >
                      <span>{{ formatDateForDisplay(newTrip.date) }}</span>
                    </Button>
                    
                    <div v-show="showDatePicker" class="absolute z-50 mt-1 bg-white border rounded-md shadow-lg">
                      <div class="p-2">
                        <CalendarComponent
                          v-model="date"
                          mode="single"
                          class="border-0"
                        />
                        <div class="flex justify-end mt-2 gap-2">
                          <Button size="sm" variant="outline" @click="showDatePicker = false">
                            Abbrechen
                          </Button>
                          <Button 
                            size="sm" 
                            class="bg-primary text-white"
                            @click="handleDateSelect(date, 'date')"
                          >
                            Übernehmen
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="formErrors.date" class="text-xs text-red-500">{{ formErrors.date }}</div>
                </div>
                
                <!-- Vehicle -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Fahrzeug <span class="text-red-500">*</span></label>
                  <select 
                    v-model="newTrip.vehicleId" 
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    :class="{ 'border-red-500': formErrors.vehicleId }"
                  >
                    <option value="" disabled selected>Fahrzeug auswählen</option>
                    <option 
                      v-for="vehicle in vehicles" 
                      :key="vehicle.id" 
                      :value="vehicle.id"
                    >
                      {{ vehicle.name }} ({{ vehicle.licensePlate }})
                    </option>
                  </select>
                  <div v-if="formErrors.vehicleId" class="text-xs text-red-500">{{ formErrors.vehicleId }}</div>
                </div>
              </div>
              
              <div class="grid grid-cols-2 gap-4">
                <!-- Start Odometer -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Kilometerstand Beginn <span class="text-red-500">*</span></label>
                  <Input 
                    v-model="newTrip.startOdometer" 
                    type="number"
                    placeholder="z.B. 12350"
                    :class="{ 'border-red-500': formErrors.startOdometer }"
                  />
                  <div v-if="formErrors.startOdometer" class="text-xs text-red-500">{{ formErrors.startOdometer }}</div>
                </div>
                
                <!-- End Odometer -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Kilometerstand Ende <span class="text-red-500">*</span></label>
                  <Input 
                    v-model="newTrip.endOdometer" 
                    type="number"
                    placeholder="z.B. 12372"
                    :class="{ 'border-red-500': formErrors.endOdometer }"
                  />
                  <div v-if="formErrors.endOdometer" class="text-xs text-red-500">{{ formErrors.endOdometer }}</div>
                </div>
              </div>
              
            <div class="grid grid-cols-2 gap-4">
                <!-- Start Location -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Startort <span class="text-red-500">*</span></label>
                  <Input 
                    v-model="newTrip.startLocation" 
                    placeholder="z.B. Lonsee, Firmensitz"
                    :class="{ 'border-red-500': formErrors.startLocation }"
                  />
                  <div v-if="formErrors.startLocation" class="text-xs text-red-500">{{ formErrors.startLocation }}</div>
                </div>
                
                <!-- End Location -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Zielort <span class="text-red-500">*</span></label>
                  <Input 
                    v-model="newTrip.endLocation" 
                    placeholder="z.B. Ulm, Kunde XYZ"
                    :class="{ 'border-red-500': formErrors.endLocation }"
                  />
                  <div v-if="formErrors.endLocation" class="text-xs text-red-500">{{ formErrors.endLocation }}</div>
                </div>
              </div>
              
              <div class="grid grid-cols-2 gap-4">
                <!-- Purpose -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Zweck der Fahrt <span class="text-red-500">*</span></label>
                  <Input 
                    v-model="newTrip.purpose" 
                    placeholder="z.B. Kundentermin"
                    :class="{ 'border-red-500': formErrors.purpose }"
                  />
                  <div v-if="formErrors.purpose" class="text-xs text-red-500">{{ formErrors.purpose }}</div>
                </div>
                
                <!-- Trip Type -->
                <div class="space-y-2">
                <label class="text-sm font-medium">Art der Fahrt <span class="text-red-500">*</span></label>
                  <select 
                    v-model="newTrip.type" 
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    :class="{ 'border-red-500': formErrors.type }"
                  >
                    <option value="" disabled>Art auswählen</option>
                    <option 
                      v-for="type in tripTypes" 
                      :key="type.id" 
                      :value="type.id"
                    >
                      {{ type.name }}
                    </option>
                  </select>
                  <div v-if="formErrors.type" class="text-xs text-red-500">{{ formErrors.type }}</div>
                </div>
              </div>
              
              <!-- Notes -->
              <div class="space-y-2">
              <label class="text-sm font-medium">Notizen</label>
                <Input 
                  v-model="newTrip.notes" 
                  placeholder="Zusätzliche Anmerkungen (optional)"
                />
              </div>
              
              <!-- Recurring Trip Options -->
              <div class="space-y-4 border-t pt-4 mt-2">
                <div class="flex items-center space-x-2">
                  <input 
                    type="checkbox" 
                    id="isRecurring" 
                    v-model="newTrip.isRecurring"
                    class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label for="isRecurring" class="text-sm font-medium">Regelmäßige Fahrt</label>
                </div>
                
                <div v-if="newTrip.isRecurring" class="space-y-2">
                  <label class="text-sm font-medium">Wiederholungstyp</label>
                  <div class="flex gap-4 my-2">
                    <div class="flex items-center">
                      <input 
                        type="radio" 
                        id="daily" 
                        value="daily"
                        v-model="newTrip.recurrenceType"
                        class="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="daily" class="ml-2 text-sm">Täglich</label>
                    </div>
                    <div class="flex items-center">
                      <input 
                        type="radio" 
                        id="weekly" 
                        value="weekly"
                        v-model="newTrip.recurrenceType"
                        class="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="weekly" class="ml-2 text-sm">Wöchentlich</label>
                    </div>
                  </div>
                  
                  <label class="text-sm font-medium">Enddatum <span class="text-red-500">*</span></label>
                  <div class="relative">
                    <Button
                      variant="outline"
                      class="w-full justify-start text-left font-normal"
                      :class="{ 'border-red-500': formErrors.recurrenceEndDate }"
                      @click="showEndDatePicker = !showEndDatePicker"
                    >
                      <span>{{ formatDateForDisplay(newTrip.recurrenceEndDate) }}</span>
                    </Button>
                    
                    <div v-show="showEndDatePicker" class="absolute bottom-full mb-1 z-50 bg-white border rounded-md shadow-lg">
                      <div class="p-2">
                        <CalendarComponent
                          v-model="date"
                          mode="single"
                          class="border-0"
                        />
                        <div class="flex justify-end mt-2 gap-2">
                          <Button size="sm" variant="outline" @click="showEndDatePicker = false">
                            Abbrechen
                          </Button>
                          <Button 
                            size="sm" 
                            class="bg-primary text-white"
                            @click="handleDateSelect(date, 'recurrenceEndDate')"
                          >
                            Übernehmen
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="formErrors.recurrenceEndDate" class="text-xs text-red-500">{{ formErrors.recurrenceEndDate }}</div>
                  
                  <!-- Wochentage Auswahl -->
                  <div v-if="newTrip.recurrenceType === 'weekly'" class="mt-4">
                    <label class="text-sm font-medium mb-2 block">Wochentage auswählen</label>
                    <div class="grid grid-cols-7 gap-2">
                      <div class="flex flex-col items-center">
                        <input 
                          type="checkbox" 
                          id="monday" 
                          v-model="newTrip.weekdays.monday"
                          class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label for="monday" class="text-xs mt-1">Mo</label>
                      </div>
                      <div class="flex flex-col items-center">
                        <input 
                          type="checkbox" 
                          id="tuesday" 
                          v-model="newTrip.weekdays.tuesday"
                          class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label for="tuesday" class="text-xs mt-1">Di</label>
                      </div>
                      <div class="flex flex-col items-center">
                        <input 
                          type="checkbox" 
                          id="wednesday" 
                          v-model="newTrip.weekdays.wednesday"
                          class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label for="wednesday" class="text-xs mt-1">Mi</label>
                      </div>
                      <div class="flex flex-col items-center">
                        <input 
                          type="checkbox" 
                          id="thursday" 
                          v-model="newTrip.weekdays.thursday"
                          class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label for="thursday" class="text-xs mt-1">Do</label>
                      </div>
                      <div class="flex flex-col items-center">
                        <input 
                          type="checkbox" 
                          id="friday" 
                          v-model="newTrip.weekdays.friday"
                          class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label for="friday" class="text-xs mt-1">Fr</label>
                      </div>
                      <div class="flex flex-col items-center">
                        <input 
                          type="checkbox" 
                          id="saturday" 
                          v-model="newTrip.weekdays.saturday"
                          class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label for="saturday" class="text-xs mt-1">Sa</label>
                      </div>
                      <div class="flex flex-col items-center">
                        <input 
                          type="checkbox" 
                          id="sunday" 
                          v-model="newTrip.weekdays.sunday"
                          class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label for="sunday" class="text-xs mt-1">So</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" @click="showAddTripDialog = false">
                Abbrechen
              </Button>
            <Button 
              type="button" 
              @click="submitTrip"
              class="bg-primary text-white hover:bg-primary/90"
            >
                Fahrt speichern
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
      <!-- Details Dialog -->
      <Dialog v-model:open="showDetailsTripDialog">
        <DialogContent class="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle class="text-2xl font-bold text-gray-900">Fahrtdetails</DialogTitle>
          </DialogHeader>
          
          <div class="grid gap-4 py-4" v-if="selectedTrip">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-base font-semibold text-gray-700">Datum</label>
                <p class="mt-1 text-lg">{{ formatDate(selectedTrip.date) }}</p>
              </div>
              <div>
                <label class="text-base font-semibold text-gray-700">Fahrzeug</label>
                <p class="mt-1 text-lg">{{ getVehicleName(selectedTrip.vehicleId) }}</p>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-base font-semibold text-gray-700">Kilometerstand Beginn</label>
                <p class="mt-1 text-lg">{{ selectedTrip.startOdometer }} km</p>
              </div>
              <div>
                <label class="text-base font-semibold text-gray-700">Kilometerstand Ende</label>
                <p class="mt-1 text-lg">{{ selectedTrip.endOdometer }} km</p>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-base font-semibold text-gray-700">Startort</label>
                <p class="mt-1 text-lg">{{ selectedTrip.startLocation }}</p>
              </div>
              <div>
                <label class="text-base font-semibold text-gray-700">Zielort</label>
                <p class="mt-1 text-lg">{{ selectedTrip.endLocation }}</p>
              </div>
            </div>
            
            <div>
              <label class="text-base font-semibold text-gray-700">Zweck der Fahrt</label>
              <p class="mt-1 text-lg">{{ selectedTrip.purpose }}</p>
            </div>
            
            <div>
              <label class="text-base font-semibold text-gray-700">Art der Fahrt</label>
              <p class="mt-1">
                <Badge :variant="getTripTypeBadge(selectedTrip.type)" class="text-base px-3 py-1">
                  {{ getTripTypeName(selectedTrip.type) }}
                </Badge>
              </p>
            </div>
            
            <div v-if="selectedTrip.notes">
              <label class="text-base font-semibold text-gray-700">Notizen</label>
              <p class="mt-1 text-lg">{{ selectedTrip.notes }}</p>
            </div>

            <!-- Regelmäßige Fahrt Details -->
            <div v-if="selectedTrip.isRecurring" class="border-t pt-4">
              <div class="space-y-4">
                <div>
                  <label class="text-base font-semibold text-gray-700">Regelmäßige Fahrt</label>
                  <p class="mt-1">
                    <Badge variant="purple" class="text-base">
                      {{ selectedTrip.recurrenceType === 'daily' ? 'Täglich' : 'Wöchentlich' }}
                    </Badge>
                  </p>
                </div>
                <div v-if="selectedTrip.recurrenceType === 'weekly' && selectedTrip.weekdays">
                  <label class="text-base font-semibold text-gray-700">Wochentage</label>
                  <div class="flex flex-wrap gap-2 mt-1">
                    <Badge v-if="selectedTrip.weekdays.monday" variant="purple" class="text-xs">Mo</Badge>
                    <Badge v-if="selectedTrip.weekdays.tuesday" variant="purple" class="text-xs">Di</Badge>
                    <Badge v-if="selectedTrip.weekdays.wednesday" variant="purple" class="text-xs">Mi</Badge>
                    <Badge v-if="selectedTrip.weekdays.thursday" variant="purple" class="text-xs">Do</Badge>
                    <Badge v-if="selectedTrip.weekdays.friday" variant="purple" class="text-xs">Fr</Badge>
                    <Badge v-if="selectedTrip.weekdays.saturday" variant="purple" class="text-xs">Sa</Badge>
                    <Badge v-if="selectedTrip.weekdays.sunday" variant="purple" class="text-xs">So</Badge>
                  </div>
                </div>
                <div>
                  <label class="text-base font-semibold text-gray-700">Enddatum</label>
                  <p class="mt-1 text-lg">{{ formatDate(selectedTrip.recurrenceEndDate) }}</p>
                </div>
              </div>
            </div>
            
            <div class="border-t pt-4 mt-2">
              <label class="text-base font-semibold text-gray-700">Gefahrene Kilometer</label>
              <p class="mt-1 text-xl font-bold text-primary">
                {{ formatDistance(getDistance(selectedTrip)) }}
              </p>
            </div>
          </div>
      
          <DialogFooter>
            <Button 
              @click="showDetailsTripDialog = false"
              class="bg-primary hover:bg-primary/90 text-white font-semibold px-8 py-2 text-lg rounded-lg"
            >
              Schließen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <!-- Edit Dialog -->
      <Dialog v-model:open="showEditTripDialog">
        <DialogContent class="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Fahrt bearbeiten</DialogTitle>
          </DialogHeader>
          
          <div class="grid gap-4 py-4" v-if="selectedTrip">
            <div class="grid grid-cols-2 gap-4">
              <!-- Date -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Datum <span class="text-red-500">*</span></label>
                <div class="relative">
                  <Button
                    variant="outline"
                    class="w-full justify-start text-left font-normal"
                    :class="{ 'border-red-500': formErrors.date }"
                    @click="showEditDatePicker = !showEditDatePicker"
                  >
                    <span>{{ formatDateForDisplay(selectedTrip.date) }}</span>
                  </Button>
                  
                  <div v-show="showEditDatePicker" class="absolute z-50 mt-1 bg-white border rounded-md shadow-lg">
                    <div class="p-2">
                      <CalendarComponent
                        v-model="date"
                        mode="single"
                        class="border-0"
                      />
                      <div class="flex justify-end mt-2 gap-2">
                        <Button size="sm" variant="outline" @click="showEditDatePicker = false">
                          Abbrechen
                        </Button>
                        <Button 
                          size="sm" 
                          class="bg-primary text-white"
                          @click="handleDateSelect(date, 'date', selectedTrip)"
                        >
                          Übernehmen
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="formErrors.date" class="text-xs text-red-500">{{ formErrors.date }}</div>
              </div>
              
              <!-- Vehicle -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Fahrzeug <span class="text-red-500">*</span></label>
                <select 
                  v-model="selectedTrip.vehicleId"
                  class="w-full h-10 rounded-md border border-input bg-white px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  :class="{ 'border-red-500': formErrors.vehicleId }"
                >
                  <option value="" disabled>Fahrzeug auswählen</option>
                  <option 
                    v-for="vehicle in vehicles" 
                    :key="vehicle.id" 
                    :value="vehicle.id"
                  >
                    {{ vehicle.name }} ({{ vehicle.licensePlate }})
                  </option>
                </select>
                <div v-if="formErrors.vehicleId" class="text-xs text-red-500">{{ formErrors.vehicleId }}</div>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <!-- Start Odometer -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Kilometerstand Beginn <span class="text-red-500">*</span></label>
                <Input 
                  v-model="selectedTrip.startOdometer"
                  type="number"
                  :class="{ 'border-red-500': formErrors.startOdometer }"
                />
                <div v-if="formErrors.startOdometer" class="text-xs text-red-500">{{ formErrors.startOdometer }}</div>
              </div>
              
              <!-- End Odometer -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Kilometerstand Ende <span class="text-red-500">*</span></label>
                <Input 
                  v-model="selectedTrip.endOdometer"
                  type="number"
                  :class="{ 'border-red-500': formErrors.endOdometer }"
                />
                <div v-if="formErrors.endOdometer" class="text-xs text-red-500">{{ formErrors.endOdometer }}</div>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <!-- Start Location -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Startort <span class="text-red-500">*</span></label>
                <Input 
                  v-model="selectedTrip.startLocation"
                  :class="{ 'border-red-500': formErrors.startLocation }"
                />
                <div v-if="formErrors.startLocation" class="text-xs text-red-500">{{ formErrors.startLocation }}</div>
              </div>
              
              <!-- End Location -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Zielort <span class="text-red-500">*</span></label>
                <Input 
                  v-model="selectedTrip.endLocation"
                  :class="{ 'border-red-500': formErrors.endLocation }"
                />
                <div v-if="formErrors.endLocation" class="text-xs text-red-500">{{ formErrors.endLocation }}</div>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <!-- Purpose -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Zweck der Fahrt <span class="text-red-500">*</span></label>
                <Input 
                  v-model="selectedTrip.purpose"
                  :class="{ 'border-red-500': formErrors.purpose }"
                />
                <div v-if="formErrors.purpose" class="text-xs text-red-500">{{ formErrors.purpose }}</div>
              </div>
              
              <!-- Trip Type -->
              <div class="space-y-2">
                <label class="text-sm font-medium">Art der Fahrt <span class="text-red-500">*</span></label>
                <select 
                  v-model="selectedTrip.type"
                  class="w-full h-10 rounded-md border border-input bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  :class="{ 'border-red-500': formErrors.type }"
                >
                  <option value="" disabled>Art auswählen</option>
                  <option 
                    v-for="type in tripTypes.filter(t => t.id !== 'all')" 
                    :key="type.id" 
                    :value="type.id"
                  >
                    {{ type.name }}
                  </option>
                </select>
                <div v-if="formErrors.type" class="text-xs text-red-500">{{ formErrors.type }}</div>
              </div>
            </div>
            
            <!-- Notes -->
            <div class="space-y-2">
              <label class="text-sm font-medium">Notizen</label>
              <Input 
                v-model="selectedTrip.notes"
                placeholder="Zusätzliche Anmerkungen (optional)"
              />
            </div>

            <!-- Regelmäßige Fahrt Optionen -->
            <div class="space-y-4 border-t pt-4 mt-2">
              <div class="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="editIsRecurring" 
                  v-model="selectedTrip.isRecurring"
                  class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <label for="editIsRecurring" class="text-sm font-medium">Regelmäßige Fahrt</label>
              </div>
              
              <div v-if="selectedTrip.isRecurring" class="space-y-2">
                <!-- Wiederholungstyp -->
                <label class="text-sm font-medium">Wiederholungstyp</label>
                <div class="flex gap-4 my-2">
                  <div class="flex items-center">
                    <input 
                      type="radio" 
                      id="editDaily" 
                      value="daily"
                      v-model="selectedTrip.recurrenceType"
                      class="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                    />
                    <label for="editDaily" class="ml-2 text-sm">Täglich</label>
                  </div>
                  <div class="flex items-center">
                    <input 
                      type="radio" 
                      id="editWeekly" 
                      value="weekly"
                      v-model="selectedTrip.recurrenceType"
                      class="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                    />
                    <label for="editWeekly" class="ml-2 text-sm">Wöchentlich</label>
                  </div>
                </div>
                
                <!-- Wochentage Auswahl -->
                <div v-if="selectedTrip.recurrenceType === 'weekly'" class="mt-4">
                  <label class="text-sm font-medium mb-2 block">Wochentage auswählen</label>
                  <div class="grid grid-cols-7 gap-2">
                    <div class="flex flex-col items-center">
                      <input 
                        type="checkbox" 
                        id="editMonday" 
                        v-model="selectedTrip.weekdays.monday"
                        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="editMonday" class="text-xs mt-1">Mo</label>
                    </div>
                    <div class="flex flex-col items-center">
                      <input 
                        type="checkbox" 
                        id="editTuesday" 
                        v-model="selectedTrip.weekdays.tuesday"
                        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="editTuesday" class="text-xs mt-1">Di</label>
                    </div>
                    <div class="flex flex-col items-center">
                      <input 
                        type="checkbox" 
                        id="editWednesday" 
                        v-model="selectedTrip.weekdays.wednesday"
                        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="editWednesday" class="text-xs mt-1">Mi</label>
                    </div>
                    <div class="flex flex-col items-center">
                      <input 
                        type="checkbox" 
                        id="editThursday" 
                        v-model="selectedTrip.weekdays.thursday"
                        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="editThursday" class="text-xs mt-1">Do</label>
                    </div>
                    <div class="flex flex-col items-center">
                      <input 
                        type="checkbox" 
                        id="editFriday" 
                        v-model="selectedTrip.weekdays.friday"
                        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="editFriday" class="text-xs mt-1">Fr</label>
                    </div>
                    <div class="flex flex-col items-center">
                      <input 
                        type="checkbox" 
                        id="editSaturday" 
                        v-model="selectedTrip.weekdays.saturday"
                        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="editSaturday" class="text-xs mt-1">Sa</label>
                    </div>
                    <div class="flex flex-col items-center">
                      <input 
                        type="checkbox" 
                        id="editSunday" 
                        v-model="selectedTrip.weekdays.sunday"
                        class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label for="editSunday" class="text-xs mt-1">So</label>
                    </div>
                  </div>
                </div>
                
                <label class="text-sm font-medium">Enddatum <span class="text-red-500">*</span></label>
                <div class="relative">
                  <Button
                    variant="outline"
                    class="w-full justify-start text-left font-normal"
                    :class="{ 'border-red-500': formErrors.recurrenceEndDate }"
                    @click="showEditEndDatePicker = !showEditEndDatePicker"
                  >
                    <span>{{ formatDateForDisplay(selectedTrip.recurrenceEndDate) }}</span>
                  </Button>
                  
                  <div v-show="showEditEndDatePicker" class="absolute bottom-full mb-1 z-50 bg-white border rounded-md shadow-lg">
                    <div class="p-2">
                      <CalendarComponent
                        v-model="date"
                        mode="single"
                        class="border-0"
                      />
                      <div class="flex justify-end mt-2 gap-2">
                        <Button size="sm" variant="outline" @click="showEditEndDatePicker = false">
                          Abbrechen
                        </Button>
                        <Button 
                          size="sm" 
                          class="bg-primary text-white"
                          @click="handleDateSelect(date, 'recurrenceEndDate', selectedTrip)"
                        >
                          Übernehmen
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="formErrors.recurrenceEndDate" class="text-xs text-red-500">{{ formErrors.recurrenceEndDate }}</div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" @click="showEditTripDialog = false">
              Abbrechen
              </Button>
            <Button 
              @click="updateTrip"
              class="bg-primary text-white hover:bg-primary/90"
            >
              Speichern
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <!-- Delete Confirmation Dialog -->
      <Dialog v-model:open="showDeleteDialog">
        <DialogContent class="sm:max-w-[600px] p-4">
          <DialogHeader class="mb-4">
            <DialogTitle class="text-xl font-semibold">Fahrt löschen</DialogTitle>
            <DialogDescription class="text-sm text-gray-600 mt-2">
              <template v-if="selectedTrip?.isRecurring">
                Möchten Sie diese regelmäßige Fahrt löschen? Sie können entweder nur diesen Eintrag oder alle wiederholten Einträge löschen.
              </template>
              <template v-else>
                Möchten Sie diese Fahrt wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.
              </template>
            </DialogDescription>
          </DialogHeader>
          
          <div class="py-2" v-if="selectedTrip">
            <div class="space-y-2">
              <div class="grid gap-1.5">
                <div class="flex items-center gap-2">
                  <span class="font-medium min-w-[100px]">Datum:</span>
                  <span>{{ formatDate(selectedTrip.date) }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="font-medium min-w-[100px]">Strecke:</span>
                  <span class="truncate">{{ selectedTrip.startLocation }} → {{ selectedTrip.endLocation }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="font-medium min-w-[100px]">Kilometer:</span>
                  <span class="truncate">{{ formatDistance(getDistance(selectedTrip)) }}</span>
                </div>
              </div>
              
              <div v-if="selectedTrip.isRecurring" class="mt-3">
                <Badge variant="purple" class="bg-purple-500 text-white px-3 py-1">
                  {{ selectedTrip.recurrenceType === 'daily' ? 'Täglich' : 'Wöchentlich' }} 
                  bis {{ formatDate(selectedTrip.recurrenceEndDate) }}
                </Badge>
              </div>
            </div>
          </div>
          
          <DialogFooter class="mt-4 flex-col sm:flex-row gap-2">
            <div class="flex flex-col sm:flex-row gap-2 w-full">
              <Button 
                variant="outline" 
                class="sm:flex-1"
                @click="showDeleteDialog = false"
              >
                Abbrechen
              </Button>
              <div v-if="selectedTrip?.isRecurring" class="flex flex-col sm:flex-row gap-2 sm:flex-1">
                <Button 
                  variant="destructive" 
                  class="sm:flex-1 bg-red-500 hover:bg-red-600 whitespace-normal h-auto py-2"
                  @click="deleteTrip(false)"
                >
                  Nur diese Fahrt löschen
                </Button>
                <Button 
                  variant="destructive" 
                  class="sm:flex-1 bg-red-500 hover:bg-red-600 whitespace-normal h-auto py-2"
                  @click="deleteTrip(true)"
                >
                  Alle Wiederholungen löschen
                </Button>
              </div>
              <Button 
                v-else
                variant="destructive" 
                class="sm:flex-1 bg-red-500 hover:bg-red-600"
                @click="deleteTrip(false)"
              >
                Löschen
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  </AppLayout>
</template>