<script setup lang="ts">
import { Link, router } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import axios from 'axios';

defineOptions({
  layout: AppSidebarLayout,
});

// Props von der <PERSON>-Seite
const props = defineProps<{
  maintenanceLog: {
    id: number;
    title: string;
    type: string;
    date: string;
    mileage: number | null;
    cost: number | null;
    description: string | null;
    location: string | null;
    next_inspection_date: string | null;
    vehicle_id: number;
    created_at: string;
    updated_at: string;
    user_id: number;
    vehicle?: {
      id: number;
      make: string;
      model: string;
      license_plate: string | null;
      year: number | null;
    };
    attachments?: Array<{
      id: number;
      file_name: string;
      file_path: string;
      file_type: string;
      file_size: number;
    }>;
    parts?: Array<{
      id: number;
      name: string;
      part_number: string | null;
      quantity: number;
      cost: number | null;
      purchase_url: string | null;
    }>;
  };
}>();

// Check if maintenance type is inspection
const isInspectionType = computed(() => {
  return props.maintenanceLog.type === 'inspection';
});

// State for the latest TÜV data
const latestTuvData = ref<{date: string, next_date: string} | null>(null);
const isLoadingTuv = ref(false);

// Get the latest valid TÜV/HU inspection date for the vehicle
const latestVehicleTuv = computed(() => {
  // If we already fetched the TÜV data, use it
  if (latestTuvData.value?.next_date) {
    return latestTuvData.value.next_date;
  }
  
  // If current log is an inspection with next_inspection_date, use it as fallback
  if (isInspectionType.value && props.maintenanceLog.next_inspection_date) {
    return props.maintenanceLog.next_inspection_date;
  }
  
  return null;
});

// Fetch the latest TÜV data for the vehicle
async function fetchLatestTuvData() {
  if (!props.maintenanceLog.vehicle_id || isLoadingTuv.value) return;
  
  isLoadingTuv.value = true;
  try {
    const response = await axios.get(`/api/vehicles/${props.maintenanceLog.vehicle_id}/latest-inspection`);
    if (response.data && response.data.next_inspection_date) {
      latestTuvData.value = {
        date: response.data.date,
        next_date: response.data.next_inspection_date
      };
    }
  } catch (error) {
    console.error('Error fetching TÜV data:', error);
  } finally {
    isLoadingTuv.value = false;
  }
}

// Fetch TÜV data when component mounts
onMounted(() => {
  fetchLatestTuvData();
});

// Formatierungsfunktionen
function formatDate(dateString: string): string {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
}

function formatCurrency(value: number | null): string {
  if (value === null) return '-';
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(value);
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Mapping für Typen
const typeLabels = {
  'service': 'Wartung',
  'repair': 'Reparatur',
  'modification': 'Modifikation',
  'purchase': 'Kauf',
  'sale': 'Verkauf',
  'insurance': 'Versicherung',
  'inspection': 'TÜV/HU',
  'carwash': 'Autowäsche',
  'refueling': 'Tanken',
  'parking': 'Parkgebühren',
  'tires': 'Reifen',
  'other': 'Sonstiges'
};

// Löschfunktionalität mit Bestätigung
const showDeleteConfirmation = ref(false);

function confirmDelete() {
  showDeleteConfirmation.value = true;
}

function cancelDelete() {
  showDeleteConfirmation.value = false;
}

function deleteMaintenanceLog() {
  router.delete(route('maintenance-logs.destroy', props.maintenanceLog.id), {
    onSuccess: () => {
      router.visit(route('vehicles.show', props.maintenanceLog.vehicle_id));
    }
  });
}

// Berechnung der Gesamtkosten (Teile + Wartungskosten)
const totalPartsCost = computed(() => {
  if (!props.maintenanceLog.parts || props.maintenanceLog.parts.length === 0) return 0;

  return props.maintenanceLog.parts.reduce((total, part) => {
    // Parse costs as numbers and handle null values
    const partCost = part.cost !== null ? Number(part.cost) : 0;
    const quantity = part.quantity !== null ? Number(part.quantity) : 1;

    return total + (partCost * quantity);
  }, 0);
});

const totalCost = computed(() => {
  // Ensure we're working with numbers
  const laborCost = props.maintenanceLog.cost !== null ? Number(props.maintenanceLog.cost) : 0;
  const partsCost = Number(totalPartsCost.value);

  return laborCost + partsCost;
});
</script>

<template>
  <div class="p-4 md:p-6 bg-gray-50">
    <!-- Header -->
    <div class="mb-5 flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div class="flex items-center gap-3 mb-4 sm:mb-0">
        <Link :href="route('vehicles.show', maintenanceLog.vehicle_id)" class="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <div>
          <h1 class="text-2xl font-bold text-gray-800">{{ maintenanceLog.title }}</h1>
          <div class="h-1 w-16 bg-blue-500 mt-1 rounded-full"></div>
        </div>
      </div>

      <div class="flex space-x-2">
        <Link :href="route('maintenance-logs.edit', maintenanceLog.id)" class="px-3 py-2 rounded-lg bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors flex items-center gap-1 text-sm font-medium">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Bearbeiten
        </Link>
        <button @click="confirmDelete" class="px-3 py-2 rounded-lg bg-white border border-red-300 text-red-600 hover:bg-red-50 transition-colors flex items-center gap-1 text-sm font-medium">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Löschen
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-4">
      <!-- Hauptinhalt -->
      <div class="lg:col-span-9 space-y-4">
        <!-- Fahrzeugdaten und Wartungsbasis -->
        <div v-if="maintenanceLog.vehicle" class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Fahrzeugdaten</h2>
            </div>
          </div>

          <!-- TÜV badge -->
          <div v-if="latestVehicleTuv || isLoadingTuv" class="mb-3 flex items-center bg-gray-50/50 p-3 rounded-lg border border-gray-200">
            <div class="mr-4 relative">
              <!-- Outer circle with gradient -->
              <div class="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                <!-- Inner circle -->
                <div class="w-16 h-16 rounded-full bg-white flex flex-col items-center justify-center border-4 border-white shadow-inner">
                  <div v-if="!isLoadingTuv" class="flex flex-col items-center">
                    <div class="text-sm font-extrabold text-blue-700 tracking-wide leading-none">TÜV</div>
                    <div class="text-lg font-black text-blue-600 mt-0.5">
                      {{ new Date(latestVehicleTuv).getMonth() + 1 }}/{{ new Date(latestVehicleTuv).getFullYear().toString().substr(2, 2) }}
                    </div>
                  </div>
                  <div v-else class="animate-pulse">
                    <svg class="w-8 h-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                </div>
                <!-- Small blue dot accent -->
                <div class="absolute top-0 right-0 w-4 h-4 rounded-full bg-blue-400 border-2 border-white shadow-sm"></div>
              </div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-600">Nächste HU gültig bis:</div>
              <div v-if="!isLoadingTuv" class="text-lg font-bold text-blue-700">
                {{ new Date(latestVehicleTuv).toLocaleDateString('de-DE', { year: 'numeric', month: 'long' }) }}
              </div>
              <div v-else class="h-6 bg-gray-200 animate-pulse rounded w-36"></div>
              <div v-if="!isLoadingTuv" class="mt-1 text-xs text-gray-500">
                Verbleibende Zeit: 
                <span class="font-medium text-green-600">
                  {{ Math.ceil((new Date(latestVehicleTuv).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24 * 30)) }} Monate
                </span>
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex flex-wrap items-center gap-2">
              <span class="text-sm font-medium text-gray-600">Fahrzeug:</span>
              <div class="badge py-2 px-3 border border-gray-300 font-medium text-gray-700 bg-gray-50">
                {{ maintenanceLog.vehicle.make }} {{ maintenanceLog.vehicle.model }}
              </div>
              <Link :href="route('vehicles.show', maintenanceLog.vehicle_id)" class="text-xs px-2 py-1 rounded bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors">
                <span class="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  Details
                </span>
              </Link>
            </div>

            <div v-if="maintenanceLog.vehicle.license_plate" class="flex items-center gap-2">
              <span class="text-sm font-medium text-gray-600">Kennzeichen:</span>
              <div class="badge py-2 px-3 bg-blue-100 text-blue-800 border-blue-200 font-medium">
                {{ maintenanceLog.vehicle.license_plate }}
              </div>
            </div>

            <div v-if="maintenanceLog.vehicle.year" class="flex items-center gap-2">
              <span class="text-sm font-medium text-gray-600">Baujahr:</span>
              <span class="text-gray-800">{{ maintenanceLog.vehicle.year }}</span>
            </div>
            
            <!-- Nächster TÜV Termin (für Inspection-Typ) -->
            <div v-if="isInspectionType && maintenanceLog.next_inspection_date" class="flex items-center gap-2">
              <span class="text-sm font-medium text-gray-600">Nächste HU fällig:</span>
              <span class="font-medium text-blue-800">{{ formatDate(maintenanceLog.next_inspection_date) }}</span>
            </div>
          </div>
        </div>

        <!-- Wartungsdetails -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Wartungsdetails</h2>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Typ</div>
              <div class="flex items-center">
                <span class="px-2 py-1 rounded text-xs font-medium" :class="{
                  'bg-blue-100 text-blue-800': maintenanceLog.type === 'service',
                  'bg-amber-100 text-amber-800': maintenanceLog.type === 'repair',
                  'bg-green-100 text-green-800': maintenanceLog.type === 'modification',
                  'bg-violet-100 text-violet-800': maintenanceLog.type === 'purchase',
                  'bg-indigo-100 text-indigo-800': maintenanceLog.type === 'sale',
                  'bg-teal-100 text-teal-800': maintenanceLog.type === 'insurance',
                  'bg-gray-100 text-gray-800': maintenanceLog.type === 'inspection',
                  'bg-sky-100 text-sky-800': maintenanceLog.type === 'carwash',
                  'bg-emerald-100 text-emerald-800': maintenanceLog.type === 'refueling',
                  'bg-zinc-100 text-zinc-800': maintenanceLog.type === 'parking',
                  'bg-rose-100 text-rose-800': maintenanceLog.type === 'tires',
                  'bg-slate-100 text-slate-800': maintenanceLog.type === 'other'
                }">{{ typeLabels[maintenanceLog.type as keyof typeof typeLabels] }}</span>
              </div>
            </div>

            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Datum</div>
              <div class="text-sm font-medium text-gray-800">{{ formatDate(maintenanceLog.date) }}</div>
            </div>

            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Kilometerstand</div>
              <div class="text-sm font-medium text-gray-800">{{ maintenanceLog.mileage?.toLocaleString('de-DE') || '-' }} km</div>
            </div>

            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Hauptkosten</div>
              <div class="text-sm font-medium text-gray-800">{{ formatCurrency(maintenanceLog.cost) }}</div>
            </div>

            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Teilekosten</div>
              <div class="text-sm font-medium text-gray-800">{{ formatCurrency(totalPartsCost) }}</div>
            </div>

            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Gesamtkosten</div>
              <div class="text-sm font-bold text-blue-600">{{ formatCurrency(totalCost) }}</div>
            </div>

            <div v-if="maintenanceLog.location" class="p-3 bg-gray-50 rounded-lg md:col-span-2 lg:col-span-3">
              <div class="text-xs font-medium text-gray-500 mb-1">Ort/Werkstatt</div>
              <div class="text-sm font-medium text-gray-800">{{ maintenanceLog.location }}</div>
            </div>
          </div>
        </div>

        <!-- Beschreibung -->
        <div v-if="maintenanceLog.description" class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Beschreibung</h2>
            </div>
          </div>

          <div class="p-3 bg-gray-50 rounded-lg">
            <p class="text-sm text-gray-800 whitespace-pre-line">{{ maintenanceLog.description }}</p>
          </div>
        </div>

        <!-- Verwendete Teile -->
        <div v-if="maintenanceLog.parts && maintenanceLog.parts.length > 0" class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Verwendete Teile</h2>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table class="w-full text-sm">
              <thead class="bg-gray-50 text-gray-700">
                <tr>
                  <th class="p-2 text-left">Teil</th>
                  <th class="p-2 text-left">Teilenummer</th>
                  <th class="p-2 text-left">Kauflink</th>
                  <th class="p-2 text-right">Anzahl</th>
                  <th class="p-2 text-right">Einzelpreis</th>
                  <th class="p-2 text-right">Gesamtpreis</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-100">
                <tr v-for="part in maintenanceLog.parts" :key="part.id" class="hover:bg-gray-50">
                  <td class="p-2 text-gray-800 font-medium">{{ part.name }}</td>
                  <td class="p-2 text-gray-600">{{ part.part_number || '-' }}</td>
                  <td class="p-2 text-gray-600">
                    <a v-if="part.purchase_url" :href="part.purchase_url" target="_blank" class="text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-1 truncate max-w-xs group relative">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                        <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5z" />
                      </svg>
                      <span class="truncate">Shop-Link</span>
                      <!-- Tooltip mit vollständiger URL beim Hover -->
                      <div class="absolute left-0 bottom-full mb-2 hidden group-hover:block bg-gray-800 text-white text-xs py-1 px-2 rounded shadow-lg max-w-xs z-10">
                        {{ part.purchase_url }}
                        <div class="absolute left-2 bottom-0 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800"></div>
                      </div>
                    </a>
                    <span v-else>-</span>
                  </td>
                  <td class="p-2 text-right text-gray-800">{{ part.quantity }}</td>
                  <td class="p-2 text-right text-gray-800">{{ formatCurrency(part.cost) }}</td>
                  <td class="p-2 text-right text-gray-800 font-medium">{{ formatCurrency((part.cost || 0) * part.quantity) }}</td>
                </tr>
              </tbody>
              <tfoot class="bg-gray-50">
                <tr>
                  <td colspan="5" class="p-2 text-right font-medium text-gray-700">Gesamtkosten Teile:</td>
                  <td class="p-2 text-right font-bold text-blue-600">{{ formatCurrency(totalPartsCost) }}</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        <!-- Dokumente & Bilder -->
        <div v-if="maintenanceLog.attachments && maintenanceLog.attachments.length > 0" class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Dokumente & Bilder</h2>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div v-for="attachment in maintenanceLog.attachments" :key="attachment.id"
                class="p-3 rounded-lg border border-gray-200 flex items-center gap-3 hover:border-blue-200 transition-colors">
              <div v-if="attachment.file_type.startsWith('image')" class="h-12 w-12 flex-shrink-0 rounded overflow-hidden">
                <img :src="attachment.file_path" alt="Bild Vorschau" class="h-full w-full object-cover" />
              </div>
              <div v-else class="h-12 w-12 flex-shrink-0 bg-gray-100 rounded flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V8.414l-4-4H4zm0 2h10v4h4v4H4V6z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-800 truncate">{{ attachment.file_name }}</p>
                <p class="text-xs text-gray-500">{{ formatFileSize(attachment.file_size) }}</p>
              </div>
              <a :href="attachment.file_path" target="_blank" class="p-1.5 rounded-full hover:bg-gray-100 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                  <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="lg:col-span-3 space-y-4">
        <!-- Metadaten -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
          <div class="mb-3 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Metadaten</h2>
            </div>
          </div>

          <div class="space-y-3">
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Erstellt am</div>
              <div class="text-sm text-gray-800">{{ formatDate(maintenanceLog.created_at) }}</div>
            </div>

            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="text-xs font-medium text-gray-500 mb-1">Zuletzt aktualisiert</div>
              <div class="text-sm text-gray-800">{{ formatDate(maintenanceLog.updated_at) }}</div>
            </div>

            <Link :href="route('vehicles.show', maintenanceLog.vehicle_id)" class="mt-2 w-full flex justify-center items-center px-4 py-2 rounded-lg border border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors font-medium text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Zurück zum Fahrzeug
            </Link>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Löschbestätigung Modal -->
  <div v-if="showDeleteConfirmation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white p-5 rounded-xl shadow-xl max-w-md w-full">
      <h3 class="text-lg font-bold mb-3 text-gray-800">Wartungseintrag löschen</h3>
      <p class="mb-5 text-gray-600 text-sm">Sind Sie sicher, dass Sie diesen Wartungseintrag löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.</p>
      <div class="flex justify-end gap-3">
        <button @click="cancelDelete" class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors text-sm font-medium">
          Abbrechen
        </button>
        <button @click="deleteMaintenanceLog" class="px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700 transition-colors text-sm font-medium">
          Löschen
        </button>
      </div>
    </div>
  </div>
</template>
