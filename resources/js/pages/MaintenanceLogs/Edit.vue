<script setup lang="ts">
import { useForm, Link } from '@inertiajs/vue3';
import { ref, watch, computed } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';

defineOptions({
  layout: AppSidebarLayout,
});

// Props <PERSON>-Seite
const props = defineProps<{
  maintenanceLog: {
    id: number;
    title: string;
    type: string;
    date: string;
    mileage: number | null;
    cost: number | null;
    description: string | null;
    location: string | null;
    next_inspection_date: string | null;
    vehicle_id: number;
    parts?: Array<{
      id: number;
      name: string;
      part_number: string | null;
      quantity: number;
      cost: number | null;
      purchase_url?: string | null;
    }>;
  };
  vehicle: {
    id: number;
    make: string;
    model: string;
    license_plate: string | null;
  };
}>();

// Wartungstypen
const typeOptions = [
  { value: 'service', label: 'Wartung' },
  { value: 'repair', label: 'Reparatur' },
  { value: 'modification', label: 'Modifikation' },
  { value: 'purchase', label: 'Kauf' },
  { value: 'sale', label: 'Verkauf' },
  { value: 'insurance', label: 'Versicherung' },
  { value: 'inspection', label: 'TÜV/HU' },
  { value: 'carwash', label: 'Autowäsche' },
  { value: 'refueling', label: 'Tanken' },
  { value: 'parking', label: 'Parkgebühren' },
  { value: 'tires', label: 'Reifen' },
  { value: 'other', label: 'Sonstiges' }
].sort((a, b) => a.label.localeCompare(b.label, 'de-DE'));

// Konvertiere die vorhandenen Teile in das Format für das Formular
const existingParts = props.maintenanceLog.parts?.map(part => ({
  id: part.id,
  name: part.name,
  part_number: part.part_number,
  quantity: part.quantity,
  cost: part.cost,
  purchase_url: part.purchase_url || null
})) || [];

// Form-Handling
const form = useForm({
  title: props.maintenanceLog.title,
  type: props.maintenanceLog.type,
  date: props.maintenanceLog.date ? props.maintenanceLog.date.substring(0, 10) : null,
  mileage: props.maintenanceLog.mileage,
  cost: props.maintenanceLog.cost,
  description: props.maintenanceLog.description,
  location: props.maintenanceLog.location,
  next_inspection_date: props.maintenanceLog.next_inspection_date 
    ? props.maintenanceLog.next_inspection_date.substring(0, 10) 
    : null,
  parts: existingParts
});

// Check if inspection type is selected
const isInspectionType = computed(() => {
  return form.type === 'inspection';
});

// Set default next inspection date when type changes to 'inspection'
watch(() => form.type, (newType) => {
  if (newType === 'inspection' && !form.next_inspection_date && form.date) {
    // Set next inspection date to 2 years from current inspection date
    const inspectionDate = new Date(form.date);
    inspectionDate.setFullYear(inspectionDate.getFullYear() + 2);
    form.next_inspection_date = inspectionDate.toISOString().substr(0, 10);
  }
});

// Funktionen zum Verwalten der Teile
const addPart = () => {
  form.parts.push({
    id: null as unknown as number,
    name: '',
    part_number: null,
    quantity: 1,
    cost: null,
    purchase_url: null
  });
};

const removePart = (index: number) => {
  form.parts.splice(index, 1);
};

// Berechnung der Ersatzteilkosten
const partsTotal = computed(() => {
  return form.parts.reduce((total, part) => {
    return total + (part.cost || 0) * part.quantity;
  }, 0);
});

// Berechnung der Gesamtkosten (Wartungskosten + Ersatzteilkosten)
const totalCost = computed(() => {
  return (form.cost || 0) + partsTotal.value;
});

// Formatieren von Währungsbeträgen
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
};

// Flag für Formular-Submission (für Validierungsfehler)
const isFormSubmitted = ref(false);

function submit() {
  isFormSubmitted.value = true;
  form.put(route('maintenance-logs.update', props.maintenanceLog.id), {
    preserveScroll: true,
    onSuccess: () => {
      isFormSubmitted.value = false;
    },
  });
}
</script>

<template>
  <div class="p-4 md:p-6 bg-gray-50">
    <!-- Header -->
    <div class="mb-5 flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div class="flex items-center gap-3 mb-4 sm:mb-0">
        <Link :href="route('maintenance-logs.show', maintenanceLog.id)" class="flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 bg-white hover:bg-gray-100 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <div>
          <h1 class="text-2xl font-bold text-gray-800">Wartung bearbeiten</h1>
          <div class="h-1 w-16 bg-blue-500 mt-1 rounded-full"></div>
        </div>
      </div>
    </div>

    <form @submit.prevent="submit" class="space-y-4 max-w-7xl">
      <!-- Hauptinfo-Bereich -->
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
        <!-- Fahrzeugdaten und Wartungsbasis -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-8">
          <!-- Fahrzeugdaten Abschnitt -->
          <div class="mb-4 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Wartungsdaten</h2>
            </div>
          </div>

          <div class="flex items-center gap-3 mb-3">
            <span class="text-sm font-medium text-gray-600">Fahrzeug:</span>
            <div class="badge badge-lg py-2.5 px-3 border border-gray-300 font-medium text-gray-700 bg-gray-50">{{ vehicle.make }} {{ vehicle.model }}</div>
            <div v-if="vehicle.license_plate" class="badge badge-lg py-2.5 px-3 bg-blue-100 text-blue-800 border-blue-200 font-medium">{{ vehicle.license_plate }}</div>
          </div>

          <!-- TÜV badge when inspection type is selected -->
          <div v-if="isInspectionType && form.next_inspection_date" class="mb-3 flex items-center">
            <div class="mr-3 flex items-center justify-center w-16 h-16 rounded-full border-4 border-blue-500 p-1 bg-white shadow-sm">
              <div class="text-center">
                <div class="text-xs font-bold text-blue-800">TÜV</div>
                <div class="text-sm font-bold text-blue-600">{{ new Date(form.next_inspection_date).getMonth() + 1 }}/{{ new Date(form.next_inspection_date).getFullYear().toString().substr(2, 2) }}</div>
              </div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-600">Nächste HU gültig bis:</div>
              <div class="text-base font-bold text-blue-800">{{ new Date(form.next_inspection_date).toLocaleDateString('de-DE', { year: 'numeric', month: 'long' }) }}</div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Erste Spalte -->
            <div class="space-y-4">
              <!-- Titel -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Titel <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.title"
                  type="text"
                  placeholder="z.B. Ölwechsel, Inspektion, Reifenwechsel..."
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.title && isFormSubmitted }"
                />
                <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
              </div>

              <!-- Datum -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Datum <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.date"
                  type="date"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.date && isFormSubmitted }"
                />
                <div v-if="form.errors.date" class="text-red-500 text-sm mt-1">{{ form.errors.date }}</div>
              </div>

              <!-- Next Inspection Date (visible only if type is 'inspection') -->
              <div v-if="isInspectionType">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Nächste HU fällig am <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.next_inspection_date"
                  type="date"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.next_inspection_date && isFormSubmitted }"
                />
                <div v-if="form.errors.next_inspection_date" class="text-red-500 text-sm mt-1">{{ form.errors.next_inspection_date }}</div>
              </div>
            </div>

            <!-- Zweite Spalte -->
            <div class="space-y-4">
              <!-- Typ -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Typ <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="form.type"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.type && isFormSubmitted }"
                >
                  <option v-for="option in typeOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                </select>
                <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
              </div>

              <!-- Kilometerstand -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Kilometerstand <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.mileage"
                  type="number"
                  min="0"
                  placeholder="z.B. 50000"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.mileage && isFormSubmitted }"
                />
                <div v-if="form.errors.mileage" class="text-red-500 text-sm mt-1">{{ form.errors.mileage }}</div>
              </div>
            </div>
          </div>

          <!-- Kosten und Ort -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Kosten (€) <span class="text-xs text-gray-500 font-normal">(Arbeitskosten, Versicherung, TÜV, etc.)</span>
              </label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
                <input
                  v-model="form.cost"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="z.B. 150.00"
                  class="w-full pl-7 pr-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.cost && isFormSubmitted }"
                />
              </div>
              <div v-if="form.errors.cost" class="text-red-500 text-sm mt-1">{{ form.errors.cost }}</div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Ort/Werkstatt
              </label>
              <input
                v-model="form.location"
                type="text"
                placeholder="z.B. Autohaus Müller"
                class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.location && isFormSubmitted }"
              />
              <div v-if="form.errors.location" class="text-red-500 text-sm mt-1">{{ form.errors.location }}</div>
            </div>
          </div>
        </div>

        <!-- Kostenübersicht -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-4">
          <div class="mb-4 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Kostenübersicht</h2>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">Gesamtkosten (ohne Teile):</span>
              <div class="font-medium">{{ formatCurrency(form.cost || 0) }}</div>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">Ersatzteilkosten:</span>
              <div class="font-medium">{{ formatCurrency(partsTotal) }}</div>
            </div>
            <div class="h-px w-full bg-gray-200 my-1"></div>
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-700">Gesamtkosten:</span>
              <div class="font-bold text-blue-600 text-lg">{{ formatCurrency(totalCost) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Akkordeonsektionen -->
      <div class="space-y-4">
        <!-- Beschreibung -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group">
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Beschreibung</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Detaillierte Beschreibung
              </label>
              <textarea
                v-model="form.description"
                class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 resize-none h-32"
                placeholder="Detaillierte Beschreibung der durchgeführten Arbeiten..."
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.description && isFormSubmitted }"
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>
          </details>
        </div>

        <!-- Verwendete Ersatzteile -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group" open>
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Verwendete Ersatzteile</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <div v-if="form.parts.length === 0" class="p-6 bg-gray-50 rounded-lg border border-gray-200 text-center">
                <p class="text-gray-500 mb-3">Keine Ersatzteile hinzugefügt</p>
                <button
                  type="button"
                  @click="addPart"
                  class="px-3 py-1.5 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 shadow-sm hover:shadow transition-all duration-200 text-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                  Ersatzteil hinzufügen
                </button>
              </div>

              <div v-else>
                <div class="overflow-x-auto">
                  <table class="w-full text-sm">
                    <thead class="bg-gray-50 text-gray-700">
                      <tr>
                        <th class="p-2 text-left">Nr.</th>
                        <th class="p-2 text-left">Bezeichnung</th>
                        <th class="p-2 text-left">Teilenummer</th>
                        <th class="p-2 text-left">Kauflink</th>
                        <th class="p-2 text-right">Anzahl</th>
                        <th class="p-2 text-right">Preis</th>
                        <th class="p-2 text-right">Gesamt</th>
                        <th class="p-2"></th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-100">
                      <tr v-for="(part, index) in form.parts" :key="index" class="hover:bg-gray-50">
                        <td class="p-2">
                          <div class="w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-xs font-medium">{{ index + 1 }}</div>
                        </td>
                        <td class="p-2">
                          <input
                            v-model="part.name"
                            type="text"
                            placeholder="z.B. Ölfilter"
                            class="w-full px-2 py-1 rounded border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 outline-none text-sm"
                            required
                          />
                        </td>
                        <td class="p-2">
                          <input
                            v-model="part.part_number"
                            type="text"
                            placeholder="z.B. 123456"
                            class="w-full px-2 py-1 rounded border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 outline-none text-sm"
                          />
                        </td>
                        <td class="p-2">
                          <input
                            v-model="part.purchase_url"
                            type="text"
                            placeholder="https://..."
                            class="w-full px-2 py-1 rounded border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 outline-none text-sm"
                          />
                        </td>
                        <td class="p-2">
                          <input
                            v-model="part.quantity"
                            type="number"
                            min="1"
                            step="1"
                            class="w-20 px-2 py-1 rounded border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 outline-none text-sm text-right"
                            required
                          />
                        </td>
                        <td class="p-2">
                          <div class="relative">
                            <span class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">€</span>
                            <input
                              v-model="part.cost"
                              type="number"
                              min="0"
                              step="0.01"
                              placeholder="0.00"
                              class="w-24 pl-5 pr-2 py-1 rounded border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 outline-none text-sm text-right"
                            />
                          </div>
                        </td>
                        <td class="p-2 text-right font-medium">
                          {{ formatCurrency((part.cost || 0) * part.quantity) }}
                        </td>
                        <td class="p-2 text-center">
                          <button
                            type="button"
                            @click="removePart(index)"
                            class="w-6 h-6 rounded-full hover:bg-red-50 hover:text-red-500 transition-colors inline-flex items-center justify-center"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                    <tfoot v-if="form.parts.length > 0" class="bg-gray-50">
                      <tr>
                        <td colspan="6" class="p-2 text-right font-medium">Ersatzteilkosten Gesamt:</td>
                        <td class="p-2 text-right font-bold text-blue-600">{{ formatCurrency(partsTotal) }}</td>
                        <td></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                <div class="mt-4 flex justify-end">
                  <button
                    type="button"
                    @click="addPart"
                    class="px-3 py-1.5 rounded-lg bg-blue-100 text-blue-700 font-medium hover:bg-blue-200 transition-colors duration-200 text-sm inline-flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Weiteres Ersatzteil hinzufügen
                  </button>
                </div>
              </div>
            </div>
          </details>
        </div>
      </div>

      <!-- Submit-Bereich -->
      <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
        <div class="flex justify-end gap-3">
          <Link :href="route('maintenance-logs.show', maintenanceLog.id)" class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-100 hover:border-gray-400 transition-all duration-200">Abbrechen</Link>
          <button
            type="submit"
            class="px-4 py-2 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 shadow-sm hover:shadow transition-all duration-200 focus:ring focus:ring-blue-200"
            :disabled="form.processing"
          >
            <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Speichern
          </button>
        </div>
      </div>
    </form>
  </div>
</template>
