<script setup lang="ts">
import { Link, router, useForm } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  vehicle: {
    id: number;
    make: string;
    model: string;
    license_plate: string | null;
    mileage: number;
  };
}>();

const form = useForm({
  vehicle_id: props.vehicle.id,
  title: '',
  type: 'service',
  date: new Date().toISOString().substr(0, 10),
  mileage: props.vehicle.mileage,
  cost: null as number | null,
  description: null as string | null,
  location: null as string | null,
  next_inspection_date: null as string | null,
  attachments: [] as File[],
  parts: [] as { part_id: number | null, name: string, part_number: string | null, quantity: number, cost: number | null, purchase_url: string | null }[],
});

// Pre-fill title with TÜV/HU when type is inspection
watch(() => form.type, (newType) => {
  if (newType === 'inspection' && !form.title) {
    form.title = 'TÜV/HU';
    
    // Set next inspection date to 2 years from current inspection date
    if (form.date) {
      const inspectionDate = new Date(form.date);
      inspectionDate.setFullYear(inspectionDate.getFullYear() + 2);
      form.next_inspection_date = inspectionDate.toISOString().substr(0, 10);
    }
  }
});

// Check if inspection type is selected
const isInspectionType = computed(() => {
  return form.type === 'inspection';
});

const addPart = () => {
  form.parts.push({
    part_id: null,
    name: '',
    part_number: null,
    quantity: 1,
    cost: null,
    purchase_url: null
  });
};

const removePart = (index: number) => {
  form.parts.splice(index, 1);
};

const fileInputRef = ref<HTMLInputElement | null>(null);
const attachmentPreviews = ref<Array<{ file: File, preview: string }>>([]);

const handleFileUpload = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    const newFiles = Array.from(input.files);

    // Vorschaubilder erstellen
    newFiles.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          attachmentPreviews.value.push({
            file,
            preview: e.target?.result as string
          });
        };
        reader.readAsDataURL(file);
      } else {
        attachmentPreviews.value.push({
          file,
          preview: ''
        });
      }
    });

    // Dateien zur Form hinzufügen
    form.attachments = [...form.attachments, ...newFiles];
  }
};

const removeAttachment = (index: number) => {
  attachmentPreviews.value.splice(index, 1);
  form.attachments.splice(index, 1);
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const typeOptions = [
  { value: 'service', label: 'Wartung' },
  { value: 'repair', label: 'Reparatur' },
  { value: 'modification', label: 'Umbau' },
  { value: 'inspection', label: 'TÜV/HU' },
  { value: 'purchase', label: 'Kauf' },
  { value: 'sale', label: 'Verkauf' },
  { value: 'insurance', label: 'Versicherung' },
  { value: 'carwash', label: 'Autowäsche' },
  { value: 'refueling', label: 'Tanken' },
  { value: 'parking', label: 'Parkgebühren' },
  { value: 'tires', label: 'Reifen' },
  { value: 'other', label: 'Sonstiges' }
].sort((a, b) => a.label.localeCompare(b.label, 'de-DE'));

// Berechnung der Ersatzteilkosten
const partsTotal = computed(() => {
  return form.parts.reduce((total, part) => {
    return total + (part.cost || 0) * part.quantity;
  }, 0);
});

// Berechnung der Gesamtkosten (Wartungskosten + Ersatzteilkosten)
const totalCost = computed(() => {
  return (form.cost || 0) + partsTotal.value;
});

// Formatieren von Währungsbeträgen
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
};

const isFormSubmitted = ref(false);

const submit = () => {
  isFormSubmitted.value = true;

  // Debug-Nachricht für Entwickler
  console.log('Formular wird eingereicht:', form);

  form.post(route('vehicles.maintenance-logs.store', props.vehicle.id), {
    forceFormData: true,
    preserveScroll: true,
    onSuccess: () => {
      // Zur Fahrzeugdetailseite zurückleiten
      console.log('Erfolg: Formular wurde erfolgreich übermittelt');
      router.visit(route('vehicles.show', props.vehicle.id));
    },
    onError: (errors) => {
      console.error('Fehler bei der Übermittlung:', errors);
      isFormSubmitted.value = true;

      // Anzeige eines Alerts für Benutzer, um Fehler deutlicher zu zeigen
      if (Object.keys(errors).length > 0) {
        const errorMessages = Object.values(errors).join('\n');
        alert('Fehler beim Speichern: ' + errorMessages);
      }
    },
    onFinish: () => {
      console.log('Anfrage abgeschlossen');
    }
  });
};
</script>

<template>
  <div class="p-4 md:p-6 bg-gray-50">
    <!-- Header mit Stil -->
    <div class="mb-4">
      <div class="flex items-center gap-3 mb-1">
        <Link :href="route('vehicles.show', props.vehicle.id)" class="btn btn-circle btn-outline btn-sm border-gray-300 hover:bg-gray-100 hover:border-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <h1 class="text-2xl font-bold text-gray-800">Wartung hinzufügen</h1>
      </div>
      <div class="h-1 w-24 bg-blue-500 mt-1 rounded-full"></div>
    </div>

    <form @submit.prevent="submit" class="space-y-4 max-w-7xl">
      <!-- Hauptinfo-Bereich -->
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
        <!-- Fahrzeugdaten und Wartungsbasis -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-8">
          <!-- Fahrzeugdaten Abschnitt -->
          <div class="mb-4 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Wartungsdaten</h2>
            </div>
          </div>

          <div class="flex items-center gap-3 mb-3">
            <span class="text-sm font-medium text-gray-600">Fahrzeug:</span>
            <div class="badge badge-lg py-2.5 px-3 border border-gray-300 font-medium text-gray-700 bg-gray-50">{{ vehicle.make }} {{ vehicle.model }}</div>
            <div v-if="vehicle.license_plate" class="badge badge-lg py-2.5 px-3 bg-blue-100 text-blue-800 border-blue-200 font-medium">{{ vehicle.license_plate }}</div>
          </div>

          <!-- TÜV badge when inspection type is selected -->
          <div v-if="isInspectionType && form.next_inspection_date" class="mb-3 flex items-center">
            <div class="mr-3 flex items-center justify-center w-16 h-16 rounded-full border-4 border-blue-500 p-1 bg-white shadow-sm">
              <div class="text-center">
                <div class="text-xs font-bold text-blue-800">TÜV</div>
                <div class="text-sm font-bold text-blue-600">{{ new Date(form.next_inspection_date).getMonth() + 1 }}/{{ new Date(form.next_inspection_date).getFullYear().toString().substr(2, 2) }}</div>
              </div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-600">Nächste HU gültig bis:</div>
              <div class="text-base font-bold text-blue-800">{{ new Date(form.next_inspection_date).toLocaleDateString('de-DE', { year: 'numeric', month: 'long' }) }}</div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Erste Spalte -->
            <div class="space-y-4">
              <!-- Titel -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Titel <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.title"
                  type="text"
                  placeholder="z.B. Ölwechsel, Inspektion, Reifenwechsel..."
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.title && isFormSubmitted }"
                />
                <div v-if="form.errors.title" class="text-red-500 text-sm mt-1">{{ form.errors.title }}</div>
              </div>

              <!-- Datum -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Datum <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.date"
                  type="date"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.date && isFormSubmitted }"
                />
                <div v-if="form.errors.date" class="text-red-500 text-sm mt-1">{{ form.errors.date }}</div>
              </div>

              <!-- Next Inspection Date (visible only if type is 'inspection') -->
              <div v-if="isInspectionType">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Nächste HU fällig am <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.next_inspection_date"
                  type="date"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.next_inspection_date && isFormSubmitted }"
                />
                <div v-if="form.errors.next_inspection_date" class="text-red-500 text-sm mt-1">{{ form.errors.next_inspection_date }}</div>
              </div>
            </div>

            <!-- Zweite Spalte -->
            <div class="space-y-4">
              <!-- Typ -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Typ <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="form.type"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 appearance-none bg-white"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.type && isFormSubmitted }"
                >
                  <option v-for="option in typeOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                </select>
                <div v-if="form.errors.type" class="text-red-500 text-sm mt-1">{{ form.errors.type }}</div>
              </div>

              <!-- Kilometerstand -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Kilometerstand <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="form.mileage"
                  type="number"
                  min="0"
                  placeholder="z.B. 50000"
                  class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  required
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.mileage && isFormSubmitted }"
                />
                <div v-if="form.errors.mileage" class="text-red-500 text-sm mt-1">{{ form.errors.mileage }}</div>
              </div>
            </div>
          </div>

          <!-- Kosten und Ort -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Kosten (€) <span class="text-xs text-gray-500 font-normal">(Arbeitskosten, Versicherung, TÜV, etc.)</span>
              </label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
                <input
                  v-model="form.cost"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="z.B. 150.00"
                  class="w-full pl-7 pr-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                  :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.cost && isFormSubmitted }"
                />
              </div>
              <div v-if="form.errors.cost" class="text-red-500 text-sm mt-1">{{ form.errors.cost }}</div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Ort/Werkstatt
              </label>
              <input
                v-model="form.location"
                type="text"
                placeholder="z.B. Autohaus Müller"
                class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.location && isFormSubmitted }"
              />
              <div v-if="form.errors.location" class="text-red-500 text-sm mt-1">{{ form.errors.location }}</div>
            </div>
          </div>
        </div>

        <!-- Kostenübersicht -->
        <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100 md:col-span-4">
          <div class="mb-4 border-b border-gray-100 pb-2">
            <div class="flex items-center">
              <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
              <h2 class="text-lg font-semibold text-gray-800">Kostenübersicht</h2>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">Gesamtkosten (ohne Teile):</span>
              <div class="font-medium">{{ formatCurrency(form.cost || 0) }}</div>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">Ersatzteilkosten:</span>
              <div class="font-medium">{{ formatCurrency(partsTotal) }}</div>
            </div>
            <div class="h-px w-full bg-gray-200 my-1"></div>
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium text-gray-700">Gesamtkosten:</span>
              <div class="font-bold text-blue-600 text-lg">{{ formatCurrency(totalCost) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Akkordeonsektionen -->
      <div class="space-y-4">
        <!-- Beschreibung -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group">
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Beschreibung</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Detaillierte Beschreibung
              </label>
              <textarea
                v-model="form.description"
                class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 resize-none h-32"
                placeholder="Detaillierte Beschreibung der durchgeführten Arbeiten..."
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': form.errors.description && isFormSubmitted }"
              ></textarea>
              <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
            </div>
          </details>
        </div>

        <!-- Dokumente & Bilder -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <details class="group">
            <summary class="flex items-center justify-between px-4 py-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center">
                <div class="w-1 h-6 bg-blue-500 rounded-full mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">Dokumente & Bilder</h2>
              </div>
              <svg class="h-5 w-5 text-gray-500 group-open:rotate-180 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </summary>
            <div class="p-4">
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <div class="flex flex-col items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <p class="text-sm text-center text-gray-600">Dateien hierher ziehen oder <label for="fileUpload" class="text-blue-500 font-medium cursor-pointer hover:underline">durchsuchen</label></p>
                  <input
                    id="fileUpload"
                    ref="fileInputRef"
                    type="file"
                    @change="handleFileUpload"
                    multiple
                    class="hidden"
                  />
                  <button type="button" @click="() => fileInputRef?.click()" class="px-4 py-2 rounded-lg bg-blue-100 text-blue-700 font-medium hover:bg-blue-200 transition-colors duration-200 text-sm mt-1">Dateien auswählen</button>
                </div>
              </div>
              <div v-if="form.errors.attachments" class="text-red-500 text-sm mt-1">{{ form.errors.attachments }}</div>

              <div v-if="attachmentPreviews.length > 0" class="mt-4">
                <h3 class="text-sm font-medium mb-2 text-gray-700">Ausgewählte Dateien ({{ attachmentPreviews.length }})</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div v-for="(attachment, index) in attachmentPreviews" :key="index"
                      class="flex items-center gap-2 p-2 bg-white rounded-lg border border-gray-200 hover:border-blue-200 transition-colors duration-300">
                    <div v-if="attachment.preview" class="h-10 w-10 flex-shrink-0">
                      <img :src="attachment.preview" alt="Vorschau" class="h-full w-full object-cover rounded" />
                    </div>
                    <div v-else class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div class="flex-grow">
                      <p class="text-sm font-medium truncate">{{ attachment.file.name }}</p>
                      <p class="text-xs text-gray-500">{{ formatFileSize(attachment.file.size) }}</p>
                    </div>
                    <button @click.prevent="removeAttachment(index)" class="h-6 w-6 flex items-center justify-center rounded-full hover:bg-red-50 hover:text-red-500 transition-all duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </details>
        </div>

        <!-- Verwendete Ersatzteile -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div class="border-b border-gray-100">
            <div class="px-4 py-3 bg-white">
              <h2 class="text-lg font-semibold text-gray-800">Verwendete Ersatzteile</h2>
            </div>
          </div>

          <div class="p-4">
            <div v-if="form.parts.length === 0" class="flex flex-col items-center justify-center py-8">
              <p class="text-gray-500 mb-4">Keine Ersatzteile hinzugefügt</p>
              <button @click.prevent="addPart" type="button" class="px-4 py-2 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 transition-all duration-200">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                Ersatzteil hinzufügen
              </button>
            </div>

            <div v-else>
              <div class="flex justify-end mb-4">
                <button @click.prevent="addPart" type="button" class="px-4 py-2 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 transition-all duration-200">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                  Ersatzteil hinzufügen
                </button>
              </div>

              <div class="space-y-3">
                <div v-for="(part, index) in form.parts" :key="index" class="bg-white rounded-lg border border-gray-200 p-3 hover:border-blue-200 transition-colors duration-300">
                  <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center gap-2">
                      <div class="w-5 h-5 rounded-full bg-blue-500 text-white flex items-center justify-center text-xs font-bold">{{ index + 1 }}</div>
                      <h3 class="font-medium text-gray-800">Ersatzteil</h3>
                    </div>
                    <button @click.prevent="removePart(index)" type="button" class="h-6 w-6 flex items-center justify-center rounded-full hover:bg-red-50 hover:text-red-500 transition-all duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">
                        Bezeichnung <span class="text-red-500">*</span>
                      </label>
                      <input
                        v-model="part.name"
                        type="text"
                        placeholder="z.B. Ölfilter"
                        class="w-full px-3 py-1.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
                        required
                      />
                    </div>

                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">
                        Teilenummer
                      </label>
                      <input
                        v-model="part.part_number"
                        type="text"
                        placeholder="z.B. 12345"
                        class="w-full px-3 py-1.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
                      />
                    </div>

                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">
                        Anzahl <span class="text-red-500">*</span>
                      </label>
                      <input
                        v-model.number="part.quantity"
                        type="number"
                        min="1"
                        placeholder="z.B. 1"
                        class="w-full px-3 py-1.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
                        required
                      />
                    </div>

                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">
                        Kosten (€)
                      </label>
                      <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">€</span>
                        <input
                          v-model.number="part.cost"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="z.B. 24.99"
                          class="w-full pl-7 pr-3 py-1.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
                        />
                      </div>
                    </div>

                    <div class="md:col-span-4 mt-2">
                      <label class="block text-xs font-medium text-gray-700 mb-1">
                        Kauflink
                      </label>
                      <input
                        v-model="part.purchase_url"
                        type="text"
                        placeholder="https://..."
                        class="w-full px-3 py-1.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Submit-Bereich -->
      <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
        <div class="flex justify-end gap-3">
          <Link :href="route('vehicles.show', props.vehicle.id)" class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-100 hover:border-gray-400 transition-all duration-200">Abbrechen</Link>
          <button
            type="submit"
            class="px-4 py-2 rounded-lg bg-blue-500 text-white font-medium hover:bg-blue-600 shadow-sm hover:shadow transition-all duration-200 focus:ring focus:ring-blue-200"
            :disabled="form.processing"
          >
            <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Speichern
          </button>
        </div>
      </div>
    </form>
  </div>
</template>
