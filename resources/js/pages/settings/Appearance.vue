<script setup lang="ts">
import { usePage, router } from '@inertiajs/vue3';
import type { Head } from '@inertiajs/vue3';

import AppearanceTabs from '@/components/AppearanceTabs.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import { type BreadcrumbItem } from '@/types';

import AppLayout from '@/layouts/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Appearance settings',
        href: '/settings/appearance',
    },
];

// Setup function to save theme settings
const saveSettings = () => {
    router.post('/settings/appearance', {}, {
        preserveScroll: true,
        onSuccess: () => {
            // Success notification could be added here
        }
    });
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
        <Head title="Appearance settings" />

        <SettingsLayout>
            <div class="space-y-6">
                <HeadingSmall title="Appearance settings" description="Update your account's appearance settings" />

                <div class="card bg-base-100 shadow-sm border p-6 rounded-lg">
                    <h3 class="text-lg font-medium mb-3">Theme</h3>
                    <p class="text-base-content/70 mb-4">Choose your preferred appearance mode.</p>
                    <AppearanceTabs />

                    <div class="mt-6 flex space-x-3">
                        <button class="btn btn-primary" @click="saveSettings">Save Changes</button>
                        <button class="btn">Cancel</button>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
