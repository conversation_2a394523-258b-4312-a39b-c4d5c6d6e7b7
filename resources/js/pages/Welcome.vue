<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import { Car, Calendar, History, FileText, Search, Wrench, Clock, ChevronRight, Activity, Shield, ChevronDown, Check, FileBarChart, MapPin, Calculator, TrendingUp, Route } from 'lucide-vue-next';
import { usePage } from '@inertiajs/vue3';

// Get page props from Inertia
const page = usePage();

// Service-Datum Logik
const getNextServiceDate = () => {
  const initialDate = new Date('2025-09-12');
  const today = new Date();
  
  // Wenn das initiale Datum noch in der Zukunft liegt, zeige es an
  if (initialDate > today) {
    return initialDate;
  }
  
  // Berechne, wie viele 2-Monats-Intervalle seit dem initialDate vergangen sind
  const diffTime = today.getTime() - initialDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  const twoMonthsInDays = 60; // Ungefähr 2 Monate in Tagen
  const intervals = Math.floor(diffDays / twoMonthsInDays);
  
  // Addiere die entsprechende Anzahl von 2-Monats-Intervallen zum initialDate
  const nextDate = new Date(initialDate);
  nextDate.setMonth(initialDate.getMonth() + (intervals + 1) * 2);
  
  return nextDate;
};

// Formatiertes Datum für die Anzeige
const formattedServiceDate = computed(() => {
  const date = getNextServiceDate();
  return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit', year: 'numeric' });
});

// FAQs
const faqs = [
  {
    question: 'Was macht Fahrzeug Manager einzigartig?',
    answer: 'Unsere Plattform ist die weltweit erste umfassende digitale Lebensakte für Fahrzeuge. Sie dokumentiert lückenlos die gesamte Fahrzeughistorie, steigert den Wiederverkaufswert und schafft Transparenz für zukünftige Käufer.'
  },
  {
    question: 'Wie viel kostet die Nutzung?',
    answer: 'Die Nutzung unserer Plattform ist vollständig kostenlos. Wir bieten alle Funktionen ohne Einschränkungen an, damit Sie Ihre Fahrzeugdaten optimal verwalten können.'
  },
  {
    question: 'Kann ich meine Daten exportieren?',
    answer: 'Ja, Sie können professionelle PDF-Berichte mit der kompletten Fahrzeughistorie, allen Wartungsarbeiten und einer Kostenübersicht erstellen - ideal für den Fahrzeugverkauf oder als Nachweis bei Versicherungen.'
  },
  {
    question: 'Funktioniert die App auf allen Geräten?',
    answer: 'Ja, unsere Anwendung ist vollständig responsive und funktioniert auf Desktop, Tablet und Smartphone. Sie können Ihre Fahrzeugdaten überall und jederzeit verwalten.'
  }
];

// Toggle FAQ visibility
const toggleFaq = (index: number): void => {
  openFaqs.value[index] = !openFaqs.value[index];
};

// Initialize FAQ state
const openFaqs = ref(Array(faqs.length).fill(false));

// Features for statistics
const features = [
  {
    icon: History,
    title: 'Lückenlose Historie',
    description: 'Dokumentieren Sie jeden Service, jede Reparatur und jede Ersatzteilinstallation.'
  },
  {
    icon: Calendar,
    title: 'Service-Erinnerungen',
    description: 'Verpassen Sie nie wieder einen wichtigen Service-Termin oder TÜV.'
  },
  {
    icon: FileText,
    title: 'PDF-Berichte',
    description: 'Erstellen Sie professionelle Berichte zur Wertsteigerung Ihres Fahrzeugs.'
  },
  {
    icon: FileBarChart,
    title: 'Finanzamtkonformes Fahrtenbuch',
    description: 'Führen Sie ein steuerlich anerkanntes Fahrtenbuch mit automatischer Berechnung.'
  },
  {
    icon: Search,
    title: 'Ersatzteilsuche',
    description: 'Finden Sie kompatible Ersatzteile direkt in der Anwendung.'
  }
];

// Platform benefits
const benefits = [
  'Komplett kostenlose Nutzung ohne versteckte Gebühren',
  'Unbegrenzte Fahrzeuge und Wartungseinträge',
  'PDF-Berichte & Fahrzeugdokumentation',
  'Service-Erinnerungen & Wartungsplanung',
  'Finanzamtkonformes Fahrtenbuch mit Export-Funktion',
  'Ersatzteil-Katalog & Integrierte Suche',
  'Kostenübersicht & Analysen',
  'Statistiken & Auswertungen',
  'Zukunftsorientierte Entwicklung'
];

type CounterKey = 'users' | 'vehicles' | 'history' | 'trips';

// Animation for statistics
const counters = ref({
  users: 0,
  vehicles: 0,
  history: 0,
  trips: 0
});

// Base values to start from (as of the reference date)
const baseValues = {
  users: 5000,
  vehicles: 12500,
  history: 87000,
  trips: 45000  // Fahrtenbuch-Einträge
};

// Daily growth rates for each counter
const dailyGrowthRates = {
  users: 15,     // 15 new users per day
  vehicles: 35,  // 35 new vehicles per day
  history: 200,  // 200 new history entries per day
  trips: 150     // 150 new trip entries per day
};

// Reference date (launch date or any past date)
const referenceDate = new Date('2023-07-15').getTime();

// Calculate days elapsed since reference date
const calculateDaysElapsed = (): number => {
  const now = new Date().getTime();
  const millisecondsDiff = now - referenceDate;
  return Math.floor(millisecondsDiff / (1000 * 60 * 60 * 24));
};

// Calculate current target values based on days elapsed
const calculateCurrentValues = (): {users: number, vehicles: number, history: number, trips: number} => {
  const daysElapsed = calculateDaysElapsed();
  return {
    users: baseValues.users + (daysElapsed * dailyGrowthRates.users),
    vehicles: baseValues.vehicles + (daysElapsed * dailyGrowthRates.vehicles),
    history: baseValues.history + (daysElapsed * dailyGrowthRates.history),
    trips: baseValues.trips + (daysElapsed * dailyGrowthRates.trips)
  };
};

// Get the current target values
const targetValues = calculateCurrentValues();

const animateValue = (key: CounterKey, endValue: number, duration: number): void => {
  const startValue = 0;
  const startTime = new Date().getTime();

  const animate = () => {
    const currentTime = new Date().getTime();
    const elapsed = currentTime - startTime;

    if (elapsed > duration) {
      counters.value[key] = endValue;
      return;
    }

    const progress = elapsed / duration;
    counters.value[key] = Math.floor(startValue + progress * (endValue - startValue));
    requestAnimationFrame(animate);
  };

  animate();
};

onMounted(() => {
  animateValue('users', targetValues.users, 2000);
  animateValue('vehicles', targetValues.vehicles, 2500);
  animateValue('history', targetValues.history, 3000);
  animateValue('trips', targetValues.trips, 3500);
});
</script>

<template>
  <div class="min-h-screen bg-gradient-to-b from-[#f8fafc] to-[#f1f5f9] dark:from-[#0f172a] dark:to-[#020617]">
    <!-- Hero Section with Glassmorphism -->
    <section class="relative overflow-hidden wave-container">
      <!-- Background elements -->
      <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.15),transparent_70%)] dark:bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.15),transparent_70%)]"></div>
      
      <!-- Animated background shapes -->
      <div class="absolute -top-24 -left-24 w-96 h-96 bg-primary/20 rounded-full blur-3xl animate-blob"></div>
      <div class="absolute top-1/2 -right-48 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
      <div class="absolute -bottom-32 left-1/2 w-96 h-96 bg-indigo-500/20 rounded-full blur-3xl animate-blob animation-delay-4000"></div>
      
      <!-- Noise texture overlay -->
      <div class="absolute inset-0 bg-noise opacity-[0.03] dark:opacity-[0.06] pointer-events-none"></div>
      
      <header class="fixed top-0 left-0 right-0 w-full backdrop-blur-md bg-white/70 dark:bg-gray-900/60 z-50 border-b border-gray-200/30 dark:border-gray-800/30">
        <div class="container mx-auto px-6 py-4">
          <nav class="flex justify-between items-center">
            <div class="flex items-center gap-3">
              <div class="relative group">
                <div class="absolute -inset-2 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
                <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-12 w-auto relative z-10 transform group-hover:scale-105 transition-transform duration-300" />
              </div>
              <div class="ml-1">
                <span class="text-xl font-semibold text-gray-900 dark:text-white tracking-tight">Fahrzeugakte<span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">.app</span></span>
              </div>
            </div>

            <div class="hidden md:flex items-center space-x-10">
              <a href="#features" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">Features</a>
              <a href="#benefits" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">Vorteile</a>
              <a href="#faq" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">FAQ</a>
            </div>

            <div class="flex items-center gap-3">
              <Link
                v-if="page.props.auth.user"
                :href="route('dashboard')"
                class="px-5 py-2.5 text-sm font-medium bg-gradient-to-r from-primary to-blue-600 text-white rounded-full shadow-sm hover:shadow-lg hover:shadow-primary/20 hover:scale-105 transition-all duration-300"
              >
                Dashboard
              </Link>
              <template v-else>
                <Link
                  :href="route('login')"
                  class="px-5 py-2.5 text-sm font-medium rounded-full backdrop-blur-sm bg-white/90 hover:bg-white dark:bg-gray-800/90 dark:hover:bg-gray-800 text-gray-800 dark:text-white border border-gray-200/50 dark:border-gray-700/50 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300"
                >
                  Anmelden
                </Link>
                <Link
                  :href="route('register')"
                  class="px-5 py-2.5 text-sm font-medium bg-gradient-to-r from-primary to-blue-600 hover:bg-primary/90 text-white rounded-full shadow-sm hover:shadow-lg hover:shadow-primary/20 hover:scale-105 transition-all duration-300"
                >
                  Registrieren
                </Link>
              </template>
            </div>
          </nav>
        </div>
      </header>

      <!-- Extra spacing for fixed header -->
      <div class="h-20"></div>

      <!-- Hero Content - Modern & Futuristic -->
      <div class="container mx-auto px-6 pt-24 pb-36 md:pt-32 md:pb-48">
        <div class="flex flex-col md:flex-row gap-12 items-center">
          <div class="flex-1 space-y-7 md:space-y-8 relative">
            <!-- Subtle glow behind headline -->
            <div class="absolute -top-10 -left-10 w-2/3 h-2/3 bg-primary/10 rounded-full blur-3xl opacity-70 dark:opacity-40"></div>
            
            <!-- 3D-esque dots grid background -->
            <div class="absolute top-20 -left-10 w-40 h-40 opacity-20 rotate-12 hidden md:block">
              <svg viewBox="0 0 100 100" className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <pattern id="hero-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <circle cx="5" cy="5" r="1" fill="currentColor" class="text-primary animate-pulse" />
                  </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#hero-grid)" />
              </svg>
            </div>
            
            <h1 class="relative text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 dark:text-white">
              Die digitale 
              <div class="relative inline-block">
                <span class="bg-clip-text text-transparent bg-gradient-to-r from-primary via-blue-500 to-primary bg-size-200 animate-gradient-x">Lebensakte</span>
                <span class="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary/50 via-blue-500/50 to-primary/50 rounded-full bg-size-200 animate-gradient-x"></span>
              </div> 
              <br class="md:hidden">für Ihr Fahrzeug
            </h1>
            <p class="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-xl leading-relaxed backdrop-blur-[1px]">
              Dokumentieren Sie lückenlos die gesamte Historie Ihres Fahrzeugs – mit unserer zukunftsorientierten Plattform, jetzt vollständig kostenlos.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 pt-4">
              <Link
                :href="route('register')"
                class="group inline-flex items-center justify-center px-7 py-3.5 font-medium bg-gradient-to-r from-primary to-blue-600 hover:bg-primary/90 text-white rounded-full shadow-lg hover:shadow-xl hover:shadow-primary/20 transition-all duration-300 transform hover:-translate-y-1"
              >
                <span class="relative z-10">Kostenlos starten</span>
                <div class="relative ml-2">
                  <div class="absolute inset-0 bg-white/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <ChevronRight class="h-4 w-4 relative z-10 group-hover:translate-x-0.5 transition-all duration-300" />
                </div>
              </Link>
              <a 
                href="#features" 
                class="inline-flex items-center justify-center px-7 py-3.5 font-medium bg-white/80 hover:bg-white dark:bg-gray-800/80 dark:hover:bg-gray-800 text-gray-800 dark:text-white border border-gray-200/50 dark:border-gray-700/50 rounded-full backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1"
              >
                Mehr erfahren
              </a>
            </div>
            
            <!-- Floating tech badges in a more futuristic style -->
            <div class="relative mt-10 hidden md:flex space-x-4 items-center">
              <div class="flex items-center gap-2 px-4 py-1.5 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-sm transform hover:translate-y-[-2px] transition-all duration-300">
                <div class="h-7 w-7 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/50 dark:to-green-800/30 rounded-full flex items-center justify-center">
                  <Shield class="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                </div>
                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Datensicherheit</span>
              </div>
              
              <div class="flex items-center gap-2 px-4 py-1.5 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-sm transform hover:translate-y-[-2px] transition-all duration-300">
                <div class="h-7 w-7 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/50 dark:to-blue-800/30 rounded-full flex items-center justify-center">
                  <Clock class="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                </div>
                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Erinnerungen</span>
              </div>
              
              <div class="flex items-center gap-2 px-4 py-1.5 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-sm transform hover:translate-y-[-2px] transition-all duration-300">
                <div class="h-7 w-7 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/50 dark:to-purple-800/30 rounded-full flex items-center justify-center">
                  <FileText class="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />
                </div>
                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">PDF-Export</span>
              </div>

              <div class="flex items-center gap-2 px-4 py-1.5 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-sm transform hover:translate-y-[-2px] transition-all duration-300">
                <div class="h-7 w-7 bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-900/50 dark:to-emerald-800/30 rounded-full flex items-center justify-center">
                  <Route class="h-3.5 w-3.5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Fahrtenbuch</span>
              </div>
            </div>
          </div>
          
          <div class="flex-1 relative z-10">
            <!-- Dashboard preview with glassmorphism effect -->
            <div class="relative bg-white/90 dark:bg-gray-800/90 rounded-2xl shadow-2xl overflow-hidden max-w-md mx-auto backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 transform hover:-rotate-1 hover:scale-[1.03] transition-transform duration-700 group">
              <div class="absolute inset-0 bg-grid-pattern opacity-[0.03] dark:opacity-[0.06] pointer-events-none"></div>
              
              <!-- Animated halo effect on hover -->
              <div class="absolute -inset-1 bg-gradient-to-r from-primary/20 via-blue-500/20 to-purple-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-1000 group-hover:animate-gradient-xy"></div>
              
              <img 
                :src="'/images/fahrzeug_manager_00001.jpeg'" 
                alt="Fahrzeugakte Dashboard" 
                class="w-full h-auto relative z-10"
                @error="$event.target.src = '/images/fahrzeugakte-logo.png'; $event.target.style = 'padding:4rem;opacity:0.5;'"
              />
              
              <!-- Floating UI elements for futuristic look -->
              <div class="absolute top-4 right-4 px-3 py-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-medium rounded-full backdrop-blur-sm shadow-lg transform group-hover:translate-y-[-2px] transition-transform duration-300">
                Live Demo
              </div>
              
              <!-- Floating UI badge that moves on hover -->
              <div class="absolute top-4 left-4 flex items-center gap-2 px-3 py-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-xs font-medium rounded-full border border-gray-200/50 dark:border-gray-700/50 shadow-sm transform group-hover:translate-x-1 transition-transform duration-500">
                <div class="w-2 h-2 rounded-full bg-primary animate-pulse"></div>
                <span class="text-gray-700 dark:text-gray-300">KI-gestützt</span>
              </div>
              
              <!-- Gradient overlay at bottom -->
              <div class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-white dark:from-gray-800 to-transparent"></div>
            </div>
            
            <!-- Decoration elements -->
            <div class="absolute -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[150%] h-[150%] bg-gradient-to-r from-primary/10 to-blue-400/10 blur-3xl rounded-full opacity-60"></div>
            
            <!-- Floating features cards -->
            <div class="absolute -bottom-5 -left-5 md:-left-10 max-w-[180px] p-4 rounded-xl bg-white/90 dark:bg-gray-800/90 shadow-xl backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 hidden md:block animate-float transform hover:scale-105 transition-transform duration-300">
              <div class="flex items-center gap-3 mb-2">
                <div class="h-9 w-9 bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-full flex items-center justify-center">
                  <Calendar class="h-4.5 w-4.5 text-primary" />
                </div>
                <p class="text-xs font-semibold text-gray-800 dark:text-white">Nächster Service</p>
              </div>
              <p class="text-sm font-bold text-primary">{{ formattedServiceDate }}</p>
            </div>
            
            <div class="absolute -bottom-5 right-5 md:-right-5 max-w-[180px] p-4 rounded-xl bg-white/90 dark:bg-gray-800/90 shadow-xl backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 hidden md:block animate-float animation-delay-1000 transform hover:scale-105 transition-transform duration-300">
              <div class="flex items-center gap-3 mb-2">
                <div class="h-9 w-9 bg-gradient-to-br from-blue-500/20 to-indigo-500/20 rounded-full flex items-center justify-center">
                  <Activity class="h-4.5 w-4.5 text-blue-500" />
                </div>
                <p class="text-xs font-semibold text-gray-800 dark:text-white">Status</p>
              </div>
              <div class="flex items-center">
                <div class="w-2 h-2 rounded-full bg-green-500 mr-2 animate-pulse"></div>
                <p class="text-sm font-bold text-green-500">Optimal</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Modern wave divider with gradient - Adjusted to prevent content overlap -->
      <div class="absolute -bottom-1 left-0 right-0 z-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" class="w-full" preserveAspectRatio="none">
          <path fill="url(#wave-gradient)" fill-opacity="1" d="M0,192L48,176C96,160,192,128,288,122.7C384,117,480,139,576,165.3C672,192,768,224,864,224C960,224,1056,192,1152,176C1248,160,1344,160,1392,160L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          <defs>
            <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" class="dark:stop-color-gray-900" />
              <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" class="dark:stop-color-gray-900" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </section>
    
    <!-- New Stats Section -->
    <section class="py-16 bg-white dark:bg-[#0f172a]">
      <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="group p-6 text-center relative overflow-hidden bg-white dark:bg-gray-800/50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-500 border border-gray-100 dark:border-gray-800 hover:border-primary/20 dark:hover:border-primary/20">
            <div class="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <!-- Animated particles for futuristic effect -->
            <div class="absolute inset-0 overflow-hidden opacity-0 group-hover:opacity-30 transition-opacity duration-700">
              <div class="absolute top-1/4 left-1/4 w-1 h-1 bg-primary rounded-full animate-float"></div>
              <div class="absolute top-3/4 left-1/2 w-1.5 h-1.5 bg-primary rounded-full animate-float animation-delay-1000"></div>
              <div class="absolute top-1/2 left-3/4 w-1 h-1 bg-primary rounded-full animate-float animation-delay-2000"></div>
            </div>
            <div class="relative">
              <div class="text-5xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-primary via-blue-600 to-primary animate-gradient-x bg-size-200">{{ counters.users.toLocaleString('de-DE') }}+</div>
              <p class="text-gray-600 dark:text-gray-400 font-medium">Aktive Nutzer</p>
            </div>
          </div>
          <div class="group p-6 text-center relative overflow-hidden bg-white dark:bg-gray-800/50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-500 border border-gray-100 dark:border-gray-800 hover:border-blue-500/20 dark:hover:border-blue-500/20">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <!-- Animated particles for futuristic effect -->
            <div class="absolute inset-0 overflow-hidden opacity-0 group-hover:opacity-30 transition-opacity duration-700">
              <div class="absolute top-1/4 left-1/4 w-1 h-1 bg-blue-500 rounded-full animate-float"></div>
              <div class="absolute top-3/4 left-1/2 w-1.5 h-1.5 bg-blue-500 rounded-full animate-float animation-delay-1000"></div>
              <div class="absolute top-1/2 left-3/4 w-1 h-1 bg-blue-500 rounded-full animate-float animation-delay-2000"></div>
            </div>
            <div class="relative">
              <div class="text-5xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-blue-600 to-blue-500 animate-gradient-x bg-size-200">{{ counters.vehicles.toLocaleString('de-DE') }}+</div>
              <p class="text-gray-600 dark:text-gray-400 font-medium">Registrierte Fahrzeuge</p>
            </div>
          </div>
          <div class="group p-6 text-center relative overflow-hidden bg-white dark:bg-gray-800/50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-500 border border-gray-100 dark:border-gray-800 hover:border-indigo-500/20 dark:hover:border-indigo-500/20">
            <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-indigo-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <!-- Animated particles for futuristic effect -->
            <div class="absolute inset-0 overflow-hidden opacity-0 group-hover:opacity-30 transition-opacity duration-700">
              <div class="absolute top-1/4 left-1/4 w-1 h-1 bg-indigo-500 rounded-full animate-float"></div>
              <div class="absolute top-3/4 left-1/2 w-1.5 h-1.5 bg-indigo-500 rounded-full animate-float animation-delay-1000"></div>
              <div class="absolute top-1/2 left-3/4 w-1 h-1 bg-indigo-500 rounded-full animate-float animation-delay-2000"></div>
            </div>
            <div class="relative">
              <div class="text-5xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 via-purple-600 to-indigo-500 animate-gradient-x bg-size-200">{{ counters.history.toLocaleString('de-DE') }}+</div>
              <p class="text-gray-600 dark:text-gray-400 font-medium">Dokumentierte Ereignisse</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section with 3D Cards -->
    <section id="features" class="py-24 bg-gradient-to-b from-white to-gray-50 dark:from-[#0f172a] dark:to-[#0c1526] relative overflow-hidden">
      <!-- Background decoration -->
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_20%_70%,rgba(var(--primary-rgb),0.1),transparent_50%)]"></div>
      <div class="absolute inset-0 bg-noise opacity-[0.03] dark:opacity-[0.05] pointer-events-none"></div>
      
      <!-- Animated tech dots -->
      <div class="absolute top-20 right-10 hidden lg:block">
        <div class="relative w-40 h-40">
          <div class="absolute top-0 left-0 w-2 h-2 bg-primary/40 rounded-full animate-ping" style="animation-duration: 3s;"></div>
          <div class="absolute top-10 left-20 w-2 h-2 bg-blue-500/40 rounded-full animate-ping" style="animation-duration: 4s; animation-delay: 1s;"></div>
          <div class="absolute top-30 left-10 w-2 h-2 bg-indigo-500/40 rounded-full animate-ping" style="animation-duration: 5s; animation-delay: 2s;"></div>
        </div>
      </div>
      
      <div class="container mx-auto px-6 relative">
        <div class="text-center mb-16 max-w-xl mx-auto">
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-primary to-blue-500 text-white mb-4 shadow-sm">
            Innovative Funktionen
          </div>
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Schlüsselfunktionen</h2>
          <p class="text-gray-600 dark:text-gray-400">Entdecken Sie, was Fahrzeugakte.app zu einer zukunftsorientierten Plattform für moderne Fahrzeugbesitzer macht.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="(feature, index) in features" :key="index" 
               class="group bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl p-8 shadow-sm border border-gray-200/50 dark:border-gray-700/50 
                     hover:shadow-lg hover:border-primary/20 dark:hover:border-primary/20 transition-all duration-500 
                     transform perspective-1000 hover:translate-z-10 hover:-translate-y-2">
            
            <!-- Gradient halo effect -->
            <div class="absolute inset-0 bg-gradient-to-r from-primary/10 via-blue-500/10 to-purple-500/10 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
            
            <div class="p-3 rounded-xl bg-gradient-to-br from-primary/10 to-blue-500/10 w-fit mb-6 group-hover:from-primary/20 group-hover:to-blue-500/20 transition-colors duration-300 relative">
              <component :is="feature.icon" class="h-6 w-6 text-primary" />
              
              <!-- Subtle ping animation on hover -->
              <div class="absolute inset-0 rounded-xl border border-primary/20 opacity-0 group-hover:opacity-100 animate-ping transition-opacity" style="animation-duration: 3s;"></div>
            </div>
            
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 relative z-10">{{ feature.title }}</h3>
            <p class="text-gray-600 dark:text-gray-400 flex-grow relative z-10">{{ feature.description }}</p>
            
            <!-- Futuristic arrow indicator on hover -->
            <div class="mt-6 flex items-center text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-0 group-hover:translate-x-1 transition-transform">
              <span class="text-xs font-medium">Mehr erfahren</span>
              <ChevronRight class="h-3 w-3 ml-1" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Feature Highlight -->
    <section class="py-24 bg-white dark:bg-[#0c1526] overflow-hidden relative">
      <!-- Background decoration -->
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_80%_30%,rgba(var(--primary-rgb),0.1),transparent_70%)]"></div>
      <div class="absolute inset-0 bg-noise opacity-[0.02] dark:opacity-[0.05] pointer-events-none"></div>
      
      <div class="container mx-auto px-6 relative">
        <div class="flex flex-col md:flex-row items-center gap-16">
          <div class="flex-1 order-2 md:order-1">
            <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transform perspective-1000 hover:rotate-1 hover:scale-[1.01] transition-all duration-500">
              <img 
                src="/images/fahrzeug_manager_00002.jpeg" 
                alt="Wartungsplaner" 
                class="w-full h-auto"
                @error="$event.target.src = '/images/fahrzeugakte-logo.png'; $event.target.style = 'padding:4rem;opacity:0.5;'"
              />
              <!-- Subtle reflection effect -->
              <div class="absolute inset-0 bg-gradient-to-t from-white/20 to-transparent dark:from-black/20"></div>
              
              <!-- UI decorations -->
              <div class="absolute top-4 left-4 px-3 py-1 bg-blue-500/90 text-white text-xs font-medium rounded-full backdrop-blur-sm">
                Smart Planning
              </div>
            </div>
            
            <!-- Decorative elements -->
            <div class="absolute -z-10 opacity-30 blur-3xl w-72 h-72 bg-primary/30 rounded-full -bottom-10 -left-10"></div>
          </div>

          <div class="flex-1 space-y-6 order-1 md:order-2">
            <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-primary bg-primary/10 mb-2">
              <Clock class="h-3.5 w-3.5 mr-1.5" /> KI-gestützter Wartungsplaner
            </div>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">Intelligente Service-Erinnerungen für Ihre Fahrzeuge</h2>
            <p class="text-gray-600 dark:text-gray-400">
              Erhalten Sie automatische, KI-gestützte Erinnerungen für anstehende Wartungen, TÜV-Termine und Inspektionen. 
              Die prädiktive Analyse berechnet Ihre nächsten Services auf Basis individueller Fahrzeugdaten.
            </p>
            <div class="relative pl-3 border-l-2 border-primary/30 my-6">
              <p class="text-primary font-medium">Nächster Service: <span class="font-bold">{{ formattedServiceDate }}</span></p>
            </div>
            <ul class="space-y-3">
              <li class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mt-0.5">
                  <Check class="h-3 w-3 text-green-600 dark:text-green-400" />
                </div>
                <span class="ml-3 text-gray-600 dark:text-gray-400">Prädiktive Berechnung basierend auf Fahrzeugtyp und Nutzungsverhalten</span>
              </li>
              <li class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mt-0.5">
                  <Check class="h-3 w-3 text-green-600 dark:text-green-400" />
                </div>
                <span class="ml-3 text-gray-600 dark:text-gray-400">Interaktive Kalenderansicht mit synchronisierbaren Benachrichtigungen</span>
              </li>
              <li class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mt-0.5">
                  <Check class="h-3 w-3 text-green-600 dark:text-green-400" />
                </div>
                <span class="ml-3 text-gray-600 dark:text-gray-400">Automatische Integration mit bevorzugten Werkstätten in Ihrer Nähe</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section id="benefits" class="py-24 bg-gradient-to-b from-gray-50 to-white dark:from-[#0f172a] dark:to-[#0c1526] relative overflow-hidden">
      <!-- Background decoration -->
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_40%_60%,rgba(var(--primary-rgb),0.1),transparent_60%)]"></div>
      <div class="absolute inset-0 bg-noise opacity-[0.03] dark:opacity-[0.05] pointer-events-none"></div>
      
      <!-- Abstract shapes for futuristic feel -->
      <div class="absolute bottom-10 left-10 w-40 h-40 opacity-10 rotate-12 hidden lg:block">
        <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
          <path fill="none" stroke="currentColor" stroke-width="2" class="text-primary" d="M 0,70 C 0,70 64,70 64,70 C 64,70 64,0 64,0 C 64,0 128,0 128,0 C 128,0 128,70 128,70 C 128,70 192,70 192,70 C 192,70 192,140 192,140 C 192,140 128,140 128,140 C 128,140 128,210 128,210 C 128,210 64,210 64,210 C 64,210 64,140 64,140 C 64,140 0,140 0,140 C 0,140 0,70 0,70 Z"></path>
        </svg>
      </div>
      
      <div class="container mx-auto px-6 relative">
        <div class="text-center mb-16">
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-primary to-blue-500 text-white mb-4 shadow-sm">
            Zukunftsorientiert
          </div>
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Ihre Vorteile</h2>
          <p class="text-gray-600 dark:text-gray-400 max-w-xl mx-auto">
            Fahrzeugakte.app ist ab sofort vollständig kostenlos verfügbar – mit innovativen Funktionen und zukunftsorientierten Technologien.
          </p>
        </div>

        <div class="flex flex-col md:flex-row gap-10 items-center">
          <div class="flex-1 relative">
            <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transform perspective-1000 hover:rotate-1 hover:scale-[1.01] transition-all duration-500 group">
              <!-- Reflection overlay effect -->
              <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-white/20 dark:from-transparent dark:via-white/5 dark:to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-1000"></div>
              
              <img 
                src="/images/fahrzeug_manager_00003.jpeg" 
                alt="Fahrzeugakte Premium Features" 
                class="w-full h-auto"
                @error="$event.target.src = '/images/fahrzeugakte-logo.png'; $event.target.style = 'padding:4rem;opacity:0.5;'"
              />
              <!-- Futuristic label -->
              <div class="absolute top-4 left-4 flex items-center gap-2 px-3 py-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-xs font-medium rounded-full border border-gray-200/50 dark:border-gray-700/50 shadow-sm">
                <div class="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></div>
                <span class="text-gray-700 dark:text-gray-300">Smart Interface</span>
              </div>
            </div>
            
            <!-- Decorative floating accent -->
            <div class="absolute -bottom-5 -right-5 w-28 h-28 bg-primary/5 dark:bg-primary/10 rounded-full blur-xl animate-pulse" style="animation-duration: 6s;"></div>
          </div>

          <div class="flex-1">
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden border border-gray-200/50 dark:border-gray-700/50 transform hover:translate-y-[-5px] transition-all duration-500">
              <div class="p-8 md:p-10 relative">
                <!-- Abstract tech background -->
                <div class="absolute inset-0 bg-grid-pattern opacity-[0.03] dark:opacity-[0.05]"></div>
                
                <div class="flex items-center gap-3 mb-8">
                  <div class="p-2 rounded-xl bg-gradient-to-br from-primary/20 to-blue-500/20">
                    <Shield class="h-6 w-6 text-primary" />
                  </div>
                  <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Alle Funktionen kostenlos</h3>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-5 mb-8">
                  <div v-for="(benefit, index) in benefits" :key="index" class="flex items-start group">
                    <div class="flex-shrink-0 w-5 h-5 rounded-full bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/20 flex items-center justify-center mt-0.5 group-hover:from-green-200 group-hover:to-green-300 dark:group-hover:from-green-900/50 dark:group-hover:to-green-800/40 transition-colors duration-300">
                      <Check class="h-3 w-3 text-green-600 dark:text-green-400" />
                    </div>
                    <span class="ml-3.5 text-gray-700 dark:text-gray-300">{{ benefit }}</span>
                  </div>
                </div>

                <div class="mt-8">
                  <Link
                    :href="route('register')"
                    class="relative inline-flex items-center justify-center group"
                  >
                    <!-- Button glow effect -->
                    <div class="absolute -inset-0.5 bg-white/30 rounded-full blur-sm group-hover:bg-white/40 transition duration-300"></div>
                    
                    <!-- Button content -->
                    <span class="relative inline-flex items-center justify-center px-6 py-3 font-medium bg-white hover:bg-gray-50 text-primary rounded-full shadow-xl transition-all duration-300 transform group-hover:-translate-y-1">
                      Kostenlos starten
                      <ChevronRight class="ml-2 h-5 w-5 group-hover:translate-x-0.5 transition-transform duration-300" />
                    </span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-24 bg-white dark:bg-[#0c1526] relative">
      <!-- Background decoration -->
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_60%_30%,rgba(var(--primary-rgb),0.05),transparent_70%)]"></div>
      <div class="absolute inset-0 bg-noise opacity-[0.02] dark:opacity-[0.05] pointer-events-none"></div>
      
      <!-- Futuristic corner accents -->
      <div class="absolute top-0 left-0 w-48 h-48 overflow-hidden opacity-20">
        <div class="w-24 h-1 bg-primary/30 absolute top-12 left-0 rounded-full"></div>
        <div class="w-1 h-24 bg-primary/30 absolute top-0 left-12 rounded-full"></div>
      </div>
      <div class="absolute bottom-0 right-0 w-48 h-48 overflow-hidden opacity-20">
        <div class="w-24 h-1 bg-blue-500/30 absolute bottom-12 right-0 rounded-full"></div>
        <div class="w-1 h-24 bg-blue-500/30 absolute bottom-0 right-12 rounded-full"></div>
      </div>
      
      <div class="container mx-auto px-6 relative">
        <div class="flex flex-col md:flex-row gap-12 items-center">
          <div class="md:w-1/2">
            <div class="text-left mb-10">
              <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-primary to-blue-500 text-white mb-4 shadow-sm">
                Häufige Fragen
              </div>
              <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">FAQ</h2>
              <p class="text-gray-600 dark:text-gray-400 max-w-xl">Antworten auf die häufigsten Fragen zu Fahrzeugakte.app</p>
            </div>
    
            <div>
              <div v-for="(faq, index) in faqs" :key="index" 
                   class="mb-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-200/40 dark:border-gray-700/40 
                         hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-300 group">
                <button 
                  @click="toggleFaq(index)" 
                  class="flex w-full justify-between items-center text-left p-5"
                >
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white group-hover:text-primary dark:group-hover:text-primary transition-colors">{{ faq.question }}</h3>
                  <div class="flex-shrink-0 ml-2">
                    <div class="relative">
                      <div class="absolute -inset-1 bg-primary/10 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <ChevronDown 
                        :class="['h-5 w-5 text-primary relative z-10 transition-transform duration-300', openFaqs[index] ? 'transform rotate-180' : '']" 
                      />
                    </div>
                  </div>
                </button>
                <div 
                  v-show="openFaqs[index]" 
                  class="px-5 pb-5 text-gray-600 dark:text-gray-400 animate-fadeIn bg-gradient-to-b from-transparent to-gray-50/50 dark:to-gray-800/50"
                >
                  <p>{{ faq.answer }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="md:w-1/2 relative mt-12 md:mt-0">
            <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transform perspective-1000 hover:rotate-1 hover:scale-[1.01] transition-all duration-500 group">
              <!-- Holographic-like reflection effect -->
              <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-white/20 dark:from-transparent dark:via-white/5 dark:to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-1000"></div>
              
              <img 
                src="/images/fahrzeug_manager_00004.jpeg" 
                alt="Fahrzeugakte Features" 
                class="w-full h-auto"
                @error="$event.target.src = '/images/fahrzeugakte-logo.png'; $event.target.style = 'padding:4rem;opacity:0.5;'"
              />
              
              <!-- UI decorations -->
              <div class="absolute top-4 right-4 px-3 py-1 bg-gradient-to-r from-primary to-blue-500 text-white text-xs font-medium rounded-full backdrop-blur-sm shadow-md transform group-hover:translate-y-[-2px] transition-transform duration-300">
                Fahrzeugakte App
              </div>
            </div>
            
            <!-- Decorative pattern -->
            <div class="absolute -z-10 -top-5 -right-5 w-32 h-32 opacity-20">
              <svg viewBox="0 0 100 100" className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <circle cx="5" cy="5" r="1.5" fill="currentColor" className="text-primary" />
                  </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
              </svg>
            </div>
            
            <!-- Floating badge -->
            <div class="absolute -bottom-2 -left-2 md:-left-5 px-4 py-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 hidden md:flex items-center gap-3 animate-float animation-delay-2000">
              <div class="w-7 h-7 rounded-full flex items-center justify-center bg-green-100 dark:bg-green-900/30">
                <Check class="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <span class="text-sm font-medium text-gray-800 dark:text-white">100% Kostenlos</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section - Enhanced for fullscreen compatibility -->
    <section class="py-24 bg-gradient-to-r from-primary to-indigo-600 relative overflow-hidden">
      <!-- Background decorations -->
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_10%_50%,rgba(255,255,255,0.1),transparent_50%)]"></div>
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_80%_90%,rgba(255,255,255,0.1),transparent_50%)]"></div>
      <div class="absolute inset-0 bg-noise opacity-[0.05] pointer-events-none"></div>
      
      <!-- Floating blobs -->
      <div class="absolute -top-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
      <div class="absolute bottom-0 right-0 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
      
      <!-- Futuristic circuit-like patterns -->
      <div class="absolute top-10 right-10 w-40 h-40 opacity-10">
        <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10,30 L30,30 L30,10" stroke="white" stroke-width="1" />
          <path d="M70,10 L70,30 L90,30" stroke="white" stroke-width="1" />
          <path d="M90,70 L70,70 L70,90" stroke="white" stroke-width="1" />
          <path d="M30,90 L30,70 L10,70" stroke="white" stroke-width="1" />
          <circle cx="50" cy="50" r="30" stroke="white" stroke-width="1" stroke-dasharray="10 5" />
        </svg>
      </div>
      
      <div class="absolute bottom-10 left-10 w-40 h-40 opacity-10 rotate-45">
        <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10,30 L30,30 L30,10" stroke="white" stroke-width="1" />
          <path d="M70,10 L70,30 L90,30" stroke="white" stroke-width="1" />
          <path d="M90,70 L70,70 L70,90" stroke="white" stroke-width="1" />
          <path d="M30,90 L30,70 L10,70" stroke="white" stroke-width="1" />
          <circle cx="50" cy="50" r="30" stroke="white" stroke-width="1" stroke-dasharray="10 5" />
        </svg>
      </div>
      
      <div class="container mx-auto px-6 text-center relative">
        <div class="max-w-xl mx-auto">
          <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">Bereit für die digitale Zukunft Ihres Fahrzeugs?</h2>
          <p class="text-indigo-100 mb-8 text-lg">Registrieren Sie sich heute kostenlos und entdecken Sie eine zukunftsorientierte Art, Ihre Fahrzeuge zu verwalten.</p>
          <Link
            :href="route('register')"
            class="relative inline-flex items-center justify-center group"
          >
            <!-- Button glow effect -->
            <div class="absolute -inset-0.5 bg-white/30 rounded-full blur-sm group-hover:bg-white/40 transition duration-300"></div>
            
            <!-- Button content -->
            <span class="relative inline-flex items-center justify-center px-8 py-4 font-medium bg-white hover:bg-gray-50 text-primary rounded-full shadow-xl transition-all duration-300 transform group-hover:-translate-y-1">
              Kostenlos registrieren
              <ChevronRight class="ml-2 h-5 w-5 group-hover:translate-x-0.5 transition-transform duration-300" />
            </span>
          </Link>
          
          <!-- Technology badges -->
          <div class="flex justify-center mt-12 space-x-6 flex-wrap gap-y-4">
            <div class="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 transform hover:translate-y-[-2px] transition-transform duration-300">
              <span class="text-sm font-medium text-white">KI-gestützt</span>
            </div>
            <div class="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 transform hover:translate-y-[-2px] transition-transform duration-300">
              <span class="text-sm font-medium text-white">Cloud-basiert</span>
            </div>
            <div class="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 transform hover:translate-y-[-2px] transition-transform duration-300">
              <span class="text-sm font-medium text-white">Zukunftssicher</span>
            </div>
          </div>
        </div>
      </div>
    </section>
<!-- Feature Gallery Section with New Images -->
    <section id="feature-gallery" class="py-20">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl max-w-3xl mx-auto">
            <span class="block">Umfassende Funktionen für Ihre</span>
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">digitale Fahrzeugakte</span>
          </h2>
          <p class="mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Alles, was Sie für die optimale Verwaltung Ihrer Fahrzeuge benötigen, in einer intuitiven Anwendung.
          </p>
        </div>

        <!-- Feature Grid with Images -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Feature 1: Fahrzeug-Management -->
          <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 card-hover-effect">
            <div class="h-56 overflow-hidden">
              <img src="/images/fahrzeug_manager_00001.jpeg" alt="Fahrzeug-Management" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 dark:text-white">Fahrzeug-Management</h3>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Verwalten Sie beliebig viele Fahrzeuge mit allen Details wie HSN/TSN, VIN und automatischer Datenerfassung.</p>
            </div>
          </div>

          <!-- Feature 2: Wartungshistorie -->
          <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 card-hover-effect">
            <div class="h-56 overflow-hidden">
              <img src="/images/fahrzeug_manager_00002.jpeg" alt="Wartungshistorie" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 dark:text-white">Wartungshistorie</h3>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Lückenlose Dokumentation aller Wartungsarbeiten, Reparaturen und Inspektionen mit Kosten und Belegen.</p>
            </div>
          </div>

          <!-- Feature 3: Service-Erinnerungen -->
          <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 card-hover-effect">
            <div class="h-56 overflow-hidden">
              <img src="/images/fahrzeug_manager_00003.jpeg" alt="Service-Erinnerungen" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 dark:text-white">Service-Erinnerungen</h3>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Verpassen Sie nie wieder einen wichtigen Service-Termin, TÜV oder HU mit unseren automatischen Erinnerungen.</p>
            </div>
          </div>

          <!-- Feature 4: Fahrtenbuch -->
          <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 card-hover-effect">
            <div class="h-56 overflow-hidden">
              <img src="/images/fahrzeug_manager_00004.jpeg" alt="Finanzamtkonformes Fahrtenbuch" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 dark:text-white">Finanzamtkonformes Fahrtenbuch</h3>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Führen Sie ein steuerlich anerkanntes Fahrtenbuch mit automatischer Berechnung von privaten und geschäftlichen Fahrten.</p>
            </div>
          </div>

          <!-- Feature 5: PDF-Berichte -->
          <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 card-hover-effect">
            <div class="h-56 overflow-hidden">
              <img src="/images/fahrzeug_manager_00005.jpeg" alt="PDF-Berichte" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 dark:text-white">PDF-Berichte</h3>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Erstellen Sie professionelle PDF-Berichte mit kompletter Fahrzeughistorie für Verkauf oder Versicherungen.</p>
            </div>
          </div>

          <!-- Feature 6: Ersatzteilsuche -->
          <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 card-hover-effect">
            <div class="h-56 overflow-hidden">
              <img src="/images/fahrzeug_manager_00006.jpeg" alt="Ersatzteilsuche" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 dark:text-white">Ersatzteilsuche</h3>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Finden Sie die passenden Ersatzteile für Ihr Fahrzeug mit integrierter Suche und Preisvergleich.</p>
            </div>
          </div>
        </div>

        <!-- Additional Features / More Features Button -->
        <div class="mt-12 text-center">
          <Link 
            v-if="!page.props.auth.user" 
            :href="route('register')" 
            class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full bg-gradient-to-r from-primary to-blue-600 text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
          >
            Jetzt kostenlos registrieren
            <ChevronRight class="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>

    <!-- Showcase Section with Additional Images -->
    <section class="py-20 bg-gray-50 dark:bg-gray-900">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            <span class="block">Weitere Funktionen</span>
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">im Detail</span>
          </h2>
        </div>

        <!-- Additional Features - Staggered Layout -->
        <div class="flex flex-col space-y-24">
          <!-- Additional Feature 1 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Intelligentes Dashboard</h3>
              <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">Erhalten Sie eine übersichtliche Darstellung aller wichtigen Informationen zu Ihren Fahrzeugen auf einen Blick.</p>
              <ul class="space-y-2">
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Aktuelle Kilometerstände und Gesamtkosten</span>
                </li>
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Anstehende Service-Termine</span>
                </li>
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Neueste Wartungseinträge</span>
                </li>
              </ul>
            </div>
            <div class="md:w-1/2 rounded-xl overflow-hidden shadow-xl">
              <img src="/images/fahrzeug_manager_00007.jpeg" alt="Intelligentes Dashboard" class="w-full h-auto transform transition-transform duration-500 hover:scale-105">
            </div>
          </div>

          <!-- Additional Feature 2 -->
          <div class="flex flex-col md:flex-row-reverse items-center">
            <div class="md:w-1/2 md:pl-12 mb-8 md:mb-0">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Kostenübersicht und Statistiken</h3>
              <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">Behalten Sie alle Kosten im Blick und analysieren Sie Ihre Ausgaben mit detaillierten Statistiken.</p>
              <ul class="space-y-2">
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Visualisierung von Wartungskosten</span>
                </li>
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Kategorisierte Kostenanalyse</span>
                </li>
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Jahresvergleiche und Trends</span>
                </li>
              </ul>
            </div>
            <div class="md:w-1/2 rounded-xl overflow-hidden shadow-xl">
              <img src="/images/fahrzeug_manager_00008.jpeg" alt="Kostenübersicht und Statistiken" class="w-full h-auto transform transition-transform duration-500 hover:scale-105">
            </div>
          </div>

          <!-- Additional Feature 3 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Detaillierte Fahrzeugansicht</h3>
              <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">Alle wichtigen Informationen zu Ihrem Fahrzeug auf einen Blick, mit ausführlicher Wartungshistorie.</p>
              <ul class="space-y-2">
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Technische Daten und Bilder</span>
                </li>
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Chronologische Wartungshistorie</span>
                </li>
                <li class="flex items-center">
                  <Check class="h-5 w-5 text-green-500 mr-2" />
                  <span class="text-gray-700 dark:text-gray-300">Dokumentenablage für Rechnungen und Belege</span>
                </li>
              </ul>
            </div>
            <div class="md:w-1/2 rounded-xl overflow-hidden shadow-xl">
              <img src="/images/fahrzeug_manager_00009.jpeg" alt="Detaillierte Fahrzeugansicht" class="w-full h-auto transform transition-transform duration-500 hover:scale-105">
            </div>
          </div>
        </div>
      </div>
    </section>

    
    <!-- Modern Footer -->
    <footer class="bg-gray-900 text-white py-16 relative overflow-hidden z-20">
      <!-- Background pattern -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.03]"></div>
      
      <!-- Subtle glow effects -->
      <div class="absolute -top-40 -left-40 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div class="absolute -bottom-20 right-10 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl"></div>
      
      <div class="container mx-auto px-6 relative z-10">
        <div class="flex flex-col md:flex-row justify-between mb-12">
          <div class="flex items-center gap-4 mb-8 md:mb-0">
            <div class="relative group">
              <div class="absolute -inset-2 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
              <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-16 w-auto relative z-10 transform group-hover:scale-105 transition-transform duration-300" />
            </div>
            <div>
              <span class="text-2xl font-bold">Fahrzeugakte<span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">.app</span></span>
              <p class="text-sm text-gray-400 mt-1">Die digitale Fahrzeugakte der Zukunft</p>
            </div>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-3 gap-x-16 gap-y-8 mb-8 md:mb-0 relative z-10">
            <div class="flex flex-col space-y-3">
              <p class="text-sm font-medium text-gray-300 uppercase tracking-wider mb-2">Rechtliches</p>
              <a href="/legal/datenschutz" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Datenschutz</a>
              <a href="/legal/impressum" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Impressum</a>
              <a href="/legal/agb" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">AGB</a>
            </div>
            <div class="flex flex-col space-y-3">
              <p class="text-sm font-medium text-gray-300 uppercase tracking-wider mb-2">Unternehmen</p>
              <a href="/about" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Über uns</a>
              <a href="/contact" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Kontakt</a>
            </div>
          </div>
        </div>
        
        <div class="pt-8 border-t border-gray-800">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-sm text-gray-500">&copy; 2024 Fahrzeugakte.app - Alle Rechte vorbehalten</p>
          </div>
        </div>
      </div>
    </footer>

    
  </div>
</template>

<style>
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* Animation for background gradients */
.bg-size-200 {
  background-size: 200% 200%;
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-xy {
  0% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

@media (prefers-color-scheme: dark) {
  .bg-grid-pattern {
    background-image: linear-gradient(to right, rgba(255,255,255,0.05) 1px, transparent 1px),
                      linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
  }
  
  .dark\:stop-color-gray-900 {
    stop-color: #0f172a;
  }
  
  svg #wave-gradient stop {
    stop-color: #0f172a;
  }
}

/* Fix for wave divider in full screen mode */
@media screen and (min-height: 900px) {
  .wave-container {
    padding-bottom: 40vh; /* Reduced padding for very tall screens */
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes blob {
  0% {
    transform: scale(1) translate(0px, 0px);
  }
  33% {
    transform: scale(1.1) translate(30px, -50px);
  }
  66% {
    transform: scale(0.9) translate(-20px, 20px);
  }
  100% {
    transform: scale(1) translate(0px, 0px);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-blob {
  animation: blob 15s ease-in-out infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.perspective-1000 {
  perspective: 1000px;
}

.hover\:translate-z-10:hover {
  transform: translateZ(10px);
}

/* Glassmorphism effect for cards */
.backdrop-blur-sm {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Future-tech card effects */
.card-hover-effect {
  transition: all 0.5s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(var(--primary-rgb), 0.3);
}

/* Holographic effect animations */
@keyframes holographic {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-holographic {
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.1), rgba(255,255,255,0));
  background-size: 200% 100%;
  animation: holographic 2s ease infinite;
}

/* Futuristic pulse effect */
@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  80%, 100% {
    transform: scale(1.7);
    opacity: 0;
  }
}

.animate-pulse-ring {
  animation: pulse-ring 3s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

:root {
  --primary-rgb: 79, 70, 229; /* Indigo-600 in RGB format */
}
</style>
