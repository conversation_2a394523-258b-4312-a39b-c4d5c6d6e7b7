<script setup lang="ts">
import { ref } from 'vue';
import { Link } from '@inertiajs/vue3';
import { MapPin, Phone, Mail, Clock, Check } from 'lucide-vue-next';

// Formularstatus
const name = ref('');
const email = ref('');
const subject = ref('');
const message = ref('');
const formSubmitted = ref(false);
const formError = ref(false);

// Formular absenden
const submitForm = () => {
  // Hier würde normalerweise die API-Anfrage zum Senden des Formulars stattfinden
  // In diesem Beispiel simulieren wir eine erfolgreiche Übermittlung
  
  if (name.value && email.value && message.value) {
    formSubmitted.value = true;
    formError.value = false;
    
    // Formular zurücksetzen
    setTimeout(() => {
      name.value = '';
      email.value = '';
      subject.value = '';
      message.value = '';
      formSubmitted.value = false;
    }, 5000);
  } else {
    formError.value = true;
  }
};

// Kontaktdaten
const contactInfo = [
  {
    icon: MapPin,
    title: 'Adresse',
    content: 'Hauptstraße 18, 89173 Lonsee, Deutschland'
  },
  {
    icon: Phone,
    title: 'Telefon',
    content: '+49 (0) 1727352712'
  },
  {
    icon: Mail,
    title: 'E-Mail',
    content: '<EMAIL>'
  },
  {
    icon: Clock,
    title: 'Geschäftszeiten',
    content: 'Mo-Fr: 9:00 - 17:00 Uhr'
  }
];

// FAQ-Daten
const faqs = [
  {
    question: 'Wie kann ich mein Konto erstellen?',
    answer: 'Sie können ein Konto erstellen, indem Sie auf unserer Startseite auf "Registrieren" klicken und das Anmeldeformular ausfüllen. Die Registrierung ist kostenlos und dauert nur wenige Minuten.'
  },
  {
    question: 'Ist die Nutzung wirklich kostenlos?',
    answer: 'Ja, Fahrzeugakte.app ist vollständig kostenlos nutzbar. Es gibt keine versteckten Kosten oder Gebühren.'
  },
  {
    question: 'Wie kann ich Unterstützung bei technischen Problemen erhalten?',
    answer: 'Bei technischen Problemen können Sie uns über das Kontaktformular auf dieser Seite oder direkt per E-<NAME_EMAIL> erreichen. Unser Support-Team wird sich so schnell wie möglich bei Ihnen melden.'
  }
];

// Toggle FAQ visibility
const openFaqs = ref(Array(faqs.length).fill(false));
const toggleFaq = (index) => {
  openFaqs.value[index] = !openFaqs.value[index];
};
</script>

<template>
  <div class="min-h-screen bg-gradient-to-b from-[#f8fafc] to-[#f1f5f9] dark:from-[#0f172a] dark:to-[#020617]">
    <!-- Header (wiederverwendet von Welcome.vue) -->
    <header class="fixed top-0 left-0 right-0 w-full backdrop-blur-md bg-white/70 dark:bg-gray-900/60 z-50 border-b border-gray-200/30 dark:border-gray-800/30">
      <div class="container mx-auto px-6 py-4">
        <nav class="flex justify-between items-center">
          <div class="flex items-center gap-3">
            <div class="relative group">
              <div class="absolute -inset-2 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
              <Link href="/">
                <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-12 w-auto relative z-10 transform group-hover:scale-105 transition-transform duration-300" />
              </Link>
            </div>
            <div class="ml-1">
              <Link href="/" class="text-xl font-semibold text-gray-900 dark:text-white tracking-tight">Fahrzeugakte<span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">.app</span></Link>
            </div>
          </div>

          <div class="hidden md:flex items-center space-x-10">
            <Link href="/#features" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">Features</Link>
            <Link href="/#benefits" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">Vorteile</Link>
            <Link href="/#faq" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">FAQ</Link>
          </div>

          <div class="flex items-center gap-3">
            <Link
              href="/register"
              class="px-5 py-2.5 text-sm font-medium bg-gradient-to-r from-primary to-blue-600 hover:bg-primary/90 text-white rounded-full shadow-sm hover:shadow-lg hover:shadow-primary/20 hover:scale-105 transition-all duration-300"
            >
              Registrieren
            </Link>
          </div>
        </nav>
      </div>
    </header>

    <!-- Spacing for fixed header -->
    <div class="h-20"></div>

    <!-- Hero Section -->
    <section class="py-20 md:py-28 relative overflow-hidden">
      <!-- Background elements -->
      <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.15),transparent_70%)] dark:bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.15),transparent_70%)]"></div>
      <div class="absolute -top-40 -right-40 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
      <div class="absolute -bottom-32 -left-20 w-96 h-96 bg-primary/20 rounded-full blur-3xl animate-blob"></div>
      
      <div class="container mx-auto px-6 relative">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">Kontaktieren Sie uns</h1>
          <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            Haben Sie Fragen zu Fahrzeugakte.app? Unser Team steht Ihnen gerne zur Verfügung.
            Wir freuen uns auf Ihre Nachricht!
          </p>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="py-16 bg-white dark:bg-gray-900 relative">
      <div class="absolute inset-0 bg-noise opacity-[0.02] dark:opacity-[0.05] pointer-events-none"></div>
      
      <div class="container mx-auto px-6">
        <div class="flex flex-col lg:flex-row gap-12">
          <!-- Contact Form -->
          <div class="lg:w-7/12 relative">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 relative">
              <div class="absolute -inset-1 bg-gradient-to-r from-primary/10 via-blue-500/10 to-indigo-500/10 rounded-3xl blur opacity-30"></div>
              <div class="relative">
                <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Schreiben Sie uns</h2>
                
                <!-- Success Message -->
                <div v-if="formSubmitted" class="mb-8 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 flex items-start">
                  <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 dark:bg-green-800/50 flex items-center justify-center mt-0.5">
                    <Check class="h-3 w-3 text-green-600 dark:text-green-400" />
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-green-800 dark:text-green-200">Vielen Dank für Ihre Nachricht!</p>
                    <p class="text-sm text-green-700 dark:text-green-300 mt-1">Wir werden uns so schnell wie möglich bei Ihnen melden.</p>
                  </div>
                </div>
                
                <!-- Error Message -->
                <div v-if="formError" class="mb-8 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p class="text-sm font-medium text-red-800 dark:text-red-200">Bitte füllen Sie alle erforderlichen Felder aus.</p>
                </div>
                
                <form @submit.prevent="submitForm" class="space-y-6">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name *</label>
                      <input 
                        id="name" 
                        v-model="name" 
                        type="text" 
                        class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/50"
                        placeholder="Ihr Name"
                        required
                      />
                    </div>
                    <div>
                      <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">E-Mail *</label>
                      <input 
                        id="email" 
                        v-model="email" 
                        type="email" 
                        class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/50"
                        placeholder="Ihre E-Mail-Adresse"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Betreff</label>
                    <input 
                      id="subject" 
                      v-model="subject" 
                      type="text" 
                      class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder="Betreff Ihrer Nachricht"
                    />
                  </div>
                  
                  <div>
                    <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nachricht *</label>
                    <textarea 
                      id="message" 
                      v-model="message" 
                      rows="5" 
                      class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder="Ihre Nachricht"
                      required
                    ></textarea>
                  </div>
                  
                  <div class="text-right">
                    <button 
                      type="submit" 
                      class="px-6 py-3 bg-gradient-to-r from-primary to-blue-600 text-white font-medium rounded-lg shadow-md hover:shadow-lg hover:translate-y-[-2px] transition-all duration-300"
                    >
                      Nachricht senden
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
          
          <!-- Contact Info -->
          <div class="lg:w-5/12">
            <div class="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50 dark:border-gray-700/50 relative">
              <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-8">Kontaktinformationen</h2>
              
              <div class="space-y-6">
                <div v-for="(info, index) in contactInfo" :key="index" class="flex items-start">
                  <div class="flex-shrink-0 w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mt-1">
                    <component :is="info.icon" class="h-5 w-5 text-primary" />
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ info.title }}</h3>
                    <p class="text-gray-600 dark:text-gray-300">{{ info.content }}</p>
                  </div>
                </div>
              </div>
              
              
              
              <!-- Decorative element -->
              <div class="absolute -z-10 -top-10 -right-10 w-40 h-40 bg-primary/5 rounded-full blur-2xl"></div>
              <div class="absolute -z-10 -bottom-10 -left-10 w-40 h-40 bg-blue-500/5 rounded-full blur-2xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-gray-50 dark:bg-gray-800/50 relative overflow-hidden">
      <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_30%_30%,rgba(var(--primary-rgb),0.1),transparent_70%)]"></div>
      
      <div class="container mx-auto px-6 relative">
        <div class="max-w-3xl mx-auto">
          <div class="text-center mb-10">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Häufig gestellte Fragen</h2>
            <p class="text-gray-600 dark:text-gray-300">
              Antworten auf die am häufigsten gestellten Fragen. Wenn Sie Ihre Frage hier nicht finden, kontaktieren Sie uns einfach.
            </p>
          </div>
          
          <div class="space-y-4">
            <div 
              v-for="(faq, index) in faqs" 
              :key="index" 
              class="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-200/40 dark:border-gray-700/40 hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-300"
            >
              <button 
                @click="toggleFaq(index)" 
                class="flex w-full justify-between items-center text-left p-5"
              >
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ faq.question }}</h3>
                <div class="flex-shrink-0 ml-2">
                  <div class="relative w-6 h-6">
                    <div class="absolute w-6 h-0.5 bg-primary rounded top-1/2 transform -translate-y-1/2 transition-transform duration-300"></div>
                    <div 
                      class="absolute w-0.5 h-6 bg-primary rounded left-1/2 transform -translate-x-1/2 transition-transform duration-300"
                      :class="{'opacity-0': openFaqs[index]}"
                    ></div>
                  </div>
                </div>
              </button>
              <div 
                v-show="openFaqs[index]" 
                class="px-5 pb-5 text-gray-600 dark:text-gray-300 animate-fadeIn"
              >
                <p>{{ faq.answer }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Map Section -->
    <section class="py-16 bg-white dark:bg-gray-900">
      <div class="container mx-auto px-6">
        <div class="rounded-3xl overflow-hidden shadow-xl h-[400px] relative">
          
          
          <!-- Overlay mit Kontaktdaten -->
          <div class="absolute top-6 left-6 max-w-xs bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Fahrzeugakte.app</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-1">Hauptstraße 18</p>
            <p class="text-gray-600 dark:text-gray-300 mb-3">89173 Lonsee</p>
            <p class="text-gray-600 dark:text-gray-300">
              <span class="font-medium">Tel:</span> +49 (0) ************
            </p>
            <p class="text-gray-600 dark:text-gray-300">
              <span class="font-medium">E-Mail:</span> <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer (wiederverwendet von Welcome.vue) -->
    <footer class="bg-gray-900 text-white py-16 relative overflow-hidden z-20">
      <!-- Background pattern -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.03]"></div>
      
      <div class="container mx-auto px-6 relative z-10">
        <div class="flex flex-col md:flex-row justify-between mb-12">
          <div class="flex items-center gap-4 mb-8 md:mb-0">
            <div class="relative group">
              <div class="absolute -inset-2 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
              <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-16 w-auto relative z-10 transform group-hover:scale-105 transition-transform duration-300" />
            </div>
            <div>
              <span class="text-2xl font-bold">Fahrzeugakte<span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">.app</span></span>
              <p class="text-sm text-gray-400 mt-1">Die digitale Fahrzeugakte der Zukunft</p>
            </div>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-2 gap-x-16 gap-y-8 mb-8 md:mb-0 relative z-10">
            <div class="flex flex-col space-y-3">
              <p class="text-sm font-medium text-gray-300 uppercase tracking-wider mb-2">Rechtliches</p>
              <a href="/legal/datenschutz" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Datenschutz</a>
              <a href="/legal/impressum" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Impressum</a>
              <a href="/legal/agb" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">AGB</a>
            </div>
            <div class="flex flex-col space-y-3">
              <p class="text-sm font-medium text-gray-300 uppercase tracking-wider mb-2">Unternehmen</p>
              <a href="/about" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Über uns</a>
              <a href="/contact" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Kontakt</a>
            </div>
          </div>
        </div>
        
        <div class="pt-8 border-t border-gray-800">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-sm text-gray-500">&copy; 2024 Fahrzeugakte.app - Alle Rechte vorbehalten</p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style>
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

.animate-blob {
  animation: blob 15s ease-in-out infinite;
}

@keyframes blob {
  0% {
    transform: scale(1) translate(0px, 0px);
  }
  33% {
    transform: scale(1.1) translate(30px, -50px);
  }
  66% {
    transform: scale(0.9) translate(-20px, 20px);
  }
  100% {
    transform: scale(1) translate(0px, 0px);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

:root {
  --primary-rgb: 79, 70, 229; /* Indigo-600 in RGB format */
}
</style> 