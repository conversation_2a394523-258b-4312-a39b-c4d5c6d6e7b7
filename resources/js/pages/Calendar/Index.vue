<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import { Link, router } from '@inertiajs/vue3';
import axios from 'axios';

defineOptions({
  layout: AppSidebarLayout,
});

// Toast notification composable
const useToast = () => {
  return {
    showToast: (message: string, type = 'info') => {
      console.log(`${type.toUpperCase()}: ${message}`);

      // Use daisyUI toast if available
      if (typeof window !== 'undefined' && window.document) {
        const toast = document.createElement('div');
        toast.className = `toast toast-end`;

        const alert = document.createElement('div');
        alert.className = `alert alert-${type === 'error' ? 'error' : type === 'success' ? 'success' : 'info'}`;
        alert.textContent = message;

        toast.appendChild(alert);
        document.body.appendChild(toast);

        setTimeout(() => {
          document.body.removeChild(toast);
        }, 3000);
      }
    }
  };
};

const { showToast } = useToast();

// Type definitions
interface Vehicle {
  id: number;
  make: string;
  model: string;
  license_plate: string | null;
  color: string | null;
}

interface Reminder {
    id: number;
    title: string;
    vehicle_id: number;
    due_date: string | null;
    due_mileage: number | null;
    status: string;
    priority: string;
  notes: string | null;
  vehicle: Vehicle;
}

interface MaintenanceLog {
    id: number;
    title: string;
    vehicle_id: number;
    date: string;
  mileage: number | null;
    type: string;
  cost: number | null;
  notes: string | null;
  vehicle: Vehicle;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  events: CalendarEvent[];
}

interface CalendarEvent {
      id: number;
  title: string;
  type: 'reminder' | 'maintenance';
  status?: string;
  priority?: string;
  logType?: string;
  vehicle: Vehicle;
  notes?: string | null;
  time?: string | null;
  data: Reminder | MaintenanceLog;
}

interface HourEvents {
  hour: number;
  label: string;
  events: CalendarEvent[];
}

interface NewEvent {
  title: string;
  date: string;
  vehicle_id: number | null;
  type: string;
  priority: string;
  notes: string;
  is_recurring: boolean;
  recurrence_pattern: string;
  recurrence_interval: number;
}

interface EditedEvent {
  id: number | null;
  title: string;
  date: string;
  status: string;
  priority: string;
  notes: string;
  vehicle_id: number | null;
}

interface Filters {
  showPendingReminders: boolean;
  showCompletedReminders: boolean;
  showOverdueReminders: boolean;
  showMaintenanceLogs: boolean;
  vehicleFilter: string;
  typeFilter: string;
  dateRangeStart: string | null;
  dateRangeEnd: string | null;
}

const props = defineProps<{
  reminders: Reminder[];
  maintenance_logs: MaintenanceLog[];
}>();

// Calendar state
const today = new Date();
const currentMonth = ref(today.getMonth());
const currentYear = ref(today.getFullYear());
const currentDate = ref(new Date());
const selectedDate = ref(new Date());
const viewType = ref('month'); // 'month', 'week', 'day', or 'list'
const isLoading = ref(false);

// Event creation
const showCreateEventModal = ref(false);
const newEvent = ref<NewEvent>({
  title: '',
  date: '',
  vehicle_id: null,
  type: 'reminder',
  priority: 'medium',
  notes: '',
  is_recurring: false,
  recurrence_pattern: 'monthly',
  recurrence_interval: 1
});

// Vehicle list for dropdown
const vehicles = ref<Vehicle[]>([]);

// Filter state
const filters = ref<Filters>({
  showPendingReminders: true,
  showCompletedReminders: false,
  showOverdueReminders: true,
  showMaintenanceLogs: true,
  vehicleFilter: 'all',
  typeFilter: 'all',
  dateRangeStart: null,
  dateRangeEnd: null,
});

// Month and weekday names
const monthNames = [
  'Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
  'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'
];
const weekDays = ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'];
const fullWeekDays = ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'];

// Event details modal
const selectedEvent = ref<CalendarEvent | null>(null);
const showDetailsModal = ref(false);
const isEditMode = ref(false);
const editedEvent = ref<EditedEvent>({
  id: null,
  title: '',
  date: '',
  status: '',
  priority: '',
  notes: '',
  vehicle_id: null
});

// Get unique vehicles from reminders and maintenance logs
onMounted(() => {
  const uniqueVehicles = new Map<number, Vehicle>();

  if (Array.isArray(props.reminders)) {
    props.reminders.forEach(reminder => {
      if (reminder.vehicle) {
        uniqueVehicles.set(reminder.vehicle.id, reminder.vehicle);
      }
    });
  }

  if (Array.isArray(props.maintenance_logs)) {
    props.maintenance_logs.forEach(log => {
      if (log.vehicle) {
        uniqueVehicles.set(log.vehicle.id, log.vehicle);
      }
    });
  }

  vehicles.value = Array.from(uniqueVehicles.values());
});

// Navigation controls
const previousMonth = () => {
  if (currentMonth.value === 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }
  updateCurrentDate();
};

const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentMonth.value = 0;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }
  updateCurrentDate();
};

const goToToday = () => {
  currentMonth.value = today.getMonth();
  currentYear.value = today.getFullYear();
  selectedDate.value = new Date();
  updateCurrentDate();
};

const updateCurrentDate = () => {
  currentDate.value = new Date(currentYear.value, currentMonth.value, 1);
};

// Update the current view based on the selected date
watch(selectedDate, (newDate) => {
  currentMonth.value = newDate.getMonth();
  currentYear.value = newDate.getFullYear();
});

// Calendar days for current month
const calendarDays = computed<CalendarDay[]>(() => {
  const firstDay = new Date(currentYear.value, currentMonth.value, 1);
  const lastDay = new Date(currentYear.value, currentMonth.value + 1, 0);
  const days: CalendarDay[] = [];

  // Add days from previous month
  const prevMonthLastDay = new Date(currentYear.value, currentMonth.value, 0).getDate();
  for (let i = firstDay.getDay(); i > 0; i--) {
    const date = new Date(currentYear.value, currentMonth.value - 1, prevMonthLastDay - i + 1);
    days.push({
      date,
      isCurrentMonth: false,
      isToday: isSameDay(date, today),
      isSelected: isSameDay(date, selectedDate.value),
      events: getEventsForDate(date)
    });
  }

  // Add days from current month
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(currentYear.value, currentMonth.value, i);
    days.push({
      date,
      isCurrentMonth: true,
      isToday: isSameDay(date, today),
      isSelected: isSameDay(date, selectedDate.value),
      events: getEventsForDate(date)
    });
  }

  // Add days from next month
  const remainingDays = 42 - days.length; // Always show 6 weeks
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(currentYear.value, currentMonth.value + 1, i);
    days.push({
      date,
      isCurrentMonth: false,
      isToday: isSameDay(date, today),
      isSelected: isSameDay(date, selectedDate.value),
      events: getEventsForDate(date)
    });
  }

  return days;
});

// Get weekdays for the current week view
const weekDaysForCurrentWeek = computed<{date: Date, isToday: boolean, isSelected: boolean, events: CalendarEvent[]}[]>(() => {
  const days = [];
  const weekStart = getStartOfWeek(selectedDate.value);

  for (let i = 0; i < 7; i++) {
    const date = new Date(weekStart);
    date.setDate(weekStart.getDate() + i);
    days.push({
      date,
      isToday: isSameDay(date, today),
      isSelected: isSameDay(date, selectedDate.value),
      events: getEventsForDate(date)
    });
  }

  return days;
});

// Get hours for the day view
const hoursForDayView = computed<HourEvents[]>(() => {
  const hours = [];
  for (let i = 0; i < 24; i++) {
    hours.push({
      hour: i,
      label: `${String(i).padStart(2, '0')}:00`,
      events: getEventsForHour(selectedDate.value, i)
    });
  }
  return hours;
});

// Helper to get start of week
const getStartOfWeek = (date: Date): Date => {
  const result = new Date(date);
  const day = result.getDay();
  result.setDate(result.getDate() - day);
  return result;
};

// Check if two dates are the same day
const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.getDate() === date2.getDate() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getFullYear() === date2.getFullYear();
};

// Get events for a specific date
const getEventsForDate = (date: Date): CalendarEvent[] => {
  const events: CalendarEvent[] = [];
  const dateString = formatDateToYMD(date);

  // Add reminders
  if (Array.isArray(props.reminders)) {
    props.reminders.forEach(reminder => {
      if (!reminder.due_date) return;

      // Skip based on filter status
      if (reminder.status === 'pending' && !filters.value.showPendingReminders) return;
      if (reminder.status === 'overdue' && !filters.value.showOverdueReminders) return;
      if (reminder.status === 'completed' && !filters.value.showCompletedReminders) return;

      // Apply vehicle filter
      if (filters.value.vehicleFilter !== 'all' &&
          reminder.vehicle_id !== parseInt(filters.value.vehicleFilter)) return;

      const reminderDate = reminder.due_date.split('T')[0];
      if (reminderDate === dateString) {
        events.push({
          id: reminder.id,
          title: reminder.title,
          type: 'reminder',
          priority: reminder.priority,
          status: reminder.status,
          vehicle: reminder.vehicle,
          notes: reminder.notes,
          time: reminder.due_date ? new Date(reminder.due_date).toTimeString().substring(0, 5) : null,
          data: reminder
        });
      }
    });
  }

  // Add maintenance logs
  if (filters.value.showMaintenanceLogs && Array.isArray(props.maintenance_logs)) {
    props.maintenance_logs.forEach(log => {
      // Apply vehicle filter
      if (filters.value.vehicleFilter !== 'all' &&
          log.vehicle_id !== parseInt(filters.value.vehicleFilter)) return;

      // Apply type filter
      if (filters.value.typeFilter !== 'all' && log.type !== filters.value.typeFilter) return;

      const logDate = log.date.split('T')[0];
      if (logDate === dateString) {
        events.push({
          id: log.id,
          title: log.title,
          type: 'maintenance',
          logType: log.type,
          vehicle: log.vehicle,
          notes: log.notes,
          time: log.date ? new Date(log.date).toTimeString().substring(0, 5) : null,
          data: log
        });
      }
    });
  }

  return events.sort((a, b) => {
    if (!a.time && !b.time) return 0;
    if (!a.time) return 1;
    if (!b.time) return -1;
    return a.time.localeCompare(b.time);
  });
};

// Get events for a specific hour (for day view)
const getEventsForHour = (date: Date, hour: number): CalendarEvent[] => {
  const events = getEventsForDate(date);
  return events.filter(event => {
    if (!event.time) return hour === 9; // Default to 9 AM if no time
    const eventHour = parseInt(event.time.split(':')[0]);
    return eventHour === hour;
  });
};

// Format date to YYYY-MM-DD
const formatDateToYMD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Format date for display
const formatDate = (dateString: string | null): string => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

// Format date with time
const formatDateTime = (dateString: string | null): string => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// CSS classes for priority badges
const getPriorityClass = (priority: string) => {
  switch (priority) {
    case 'high': return 'badge-error';
    case 'medium': return 'badge-warning';
    case 'low': return 'badge-info';
    default: return 'badge-secondary';
  }
};

// CSS classes for status badges
const getStatusClass = (status: string) => {
  switch (status) {
    case 'pending': return 'badge-info';
    case 'completed': return 'badge-success';
    case 'overdue': return 'badge-error';
    default: return 'badge-secondary';
  }
};

// CSS classes for maintenance types
const getMaintenanceTypeClass = (type: string) => {
  switch (type) {
    case 'repair': return 'badge-error';
    case 'service': return 'badge-info';
    case 'modification': return 'badge-secondary';
    case 'inspection': return 'badge-warning';
    case 'purchase': return 'badge-success';
    case 'sale': return 'badge-primary';
    default: return 'badge-neutral';
  }
};

// Display names for maintenance types
const getMaintenanceTypeName = (type: string) => {
  switch (type) {
    case 'service': return 'Wartung';
    case 'repair': return 'Reparatur';
    case 'modification': return 'Umbau';
    case 'inspection': return 'TÜV/HU';
    case 'purchase': return 'Kauf';
    case 'sale': return 'Verkauf';
    case 'insurance': return 'Versicherung';
    default: return 'Sonstiges';
  }
};

// Get priority display name
const getPriorityName = (priority: string) => {
  switch (priority) {
    case 'high': return 'Hoch';
    case 'medium': return 'Mittel';
    case 'low': return 'Niedrig';
    default: return 'Unbekannt';
  }
};

// Get status display name
const getStatusName = (status: string) => {
  switch (status) {
    case 'pending': return 'Ausstehend';
    case 'completed': return 'Abgeschlossen';
    case 'overdue': return 'Überfällig';
    default: return 'Unbekannt';
  }
};

// Open event details modal
const openEventDetails = (event: CalendarEvent) => {
  selectedEvent.value = event;
  showDetailsModal.value = true;
  isEditMode.value = false;
};

// Select a calendar day
const selectDate = (date: Date) => {
  selectedDate.value = date;
  if (viewType.value === 'month') {
    viewType.value = 'day';
  }
};

// Toggle view type
const toggleView = (view: string) => {
  viewType.value = view;
};

// Filtered and upcoming reminders
const upcomingReminders = computed(() => {
  if (!Array.isArray(props.reminders)) return [];

  const filtered = props.reminders.filter(reminder => {
    if (reminder.status === 'pending' && !filters.value.showPendingReminders) return false;
    if (reminder.status === 'overdue' && !filters.value.showOverdueReminders) return false;
    if (reminder.status === 'completed' && !filters.value.showCompletedReminders) return false;

    if (filters.value.vehicleFilter !== 'all' &&
        reminder.vehicle_id !== parseInt(filters.value.vehicleFilter)) return false;

    return true;
  }).sort((a, b) => {
    if (!a.due_date) return 1;
    if (!b.due_date) return -1;
    return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
  });

  return filtered.slice(0, 5); // Show next 5
});

// Reset filters
const resetFilters = () => {
  filters.value = {
    showPendingReminders: true,
    showCompletedReminders: false,
    showOverdueReminders: true,
    showMaintenanceLogs: true,
    vehicleFilter: 'all',
    typeFilter: 'all',
    dateRangeStart: null,
    dateRangeEnd: null
  };
};

// Open create event modal
const openCreateEventModal = () => {
  newEvent.value = {
    title: '',
    date: formatDateToYMD(selectedDate.value),
    vehicle_id: null,
    type: 'reminder',
    priority: 'medium',
    notes: '',
    is_recurring: false,
    recurrence_pattern: 'monthly',
    recurrence_interval: 1
  };
  showCreateEventModal.value = true;
};

// Create a new event
const createEvent = async () => {
  isLoading.value = true;

  try {
    if (newEvent.value.type === 'reminder') {
      await axios.post(route('vehicles.service-reminders.store', newEvent.value.vehicle_id), {
        title: newEvent.value.title,
        due_date: newEvent.value.date,
        priority: newEvent.value.priority,
        notes: newEvent.value.notes,
        status: 'pending'
      });
    } else {
      await axios.post(route('vehicles.maintenance-logs.store', newEvent.value.vehicle_id), {
        title: newEvent.value.title,
        date: newEvent.value.date,
        type: newEvent.value.type,
        notes: newEvent.value.notes
      });
    }

    showToast('Ereignis erfolgreich erstellt', 'success');
    showCreateEventModal.value = false;

    // Reload the page to get updated data
    router.reload();
  } catch (error) {
    console.error('Fehler beim Erstellen des Ereignisses:', error);
    showToast('Fehler beim Erstellen des Ereignisses', 'error');
  } finally {
    isLoading.value = false;
  }
};

// Edit existing event
const editEvent = (event: CalendarEvent) => {
  isEditMode.value = true;
  editedEvent.value = {
    id: event.id,
    title: event.title,
    date: event.data.due_date ? event.data.due_date.split('T')[0] :
         (event.data.date ? event.data.date.split('T')[0] : ''),
    status: event.status || 'pending',
    priority: event.priority || 'medium',
    notes: event.notes || '',
    vehicle_id: event.vehicle.id
  };
};

// Update event
const updateEvent = async () => {
  isLoading.value = true;

  try {
    if (selectedEvent.value.type === 'reminder') {
      await axios.put(route('service-reminders.update', selectedEvent.value.id), {
        title: editedEvent.value.title,
        due_date: editedEvent.value.date,
        priority: editedEvent.value.priority,
        notes: editedEvent.value.notes,
        status: editedEvent.value.status
      });
    } else {
      await axios.put(route('maintenance-logs.update', selectedEvent.value.id), {
        title: editedEvent.value.title,
        date: editedEvent.value.date,
        notes: editedEvent.value.notes
      });
    }

    showToast('Ereignis erfolgreich aktualisiert', 'success');
    showDetailsModal.value = false;
    isEditMode.value = false;

    // Reload the page to get updated data
    router.reload();
  } catch (error) {
    console.error('Fehler beim Aktualisieren des Ereignisses:', error);
    showToast('Fehler beim Aktualisieren des Ereignisses', 'error');
  } finally {
    isLoading.value = false;
  }
};

// Quick complete a reminder
const completeReminder = async (reminderId: number) => {
  isLoading.value = true;

  try {
    await axios.put(route('service-reminders.update', reminderId), {
      status: 'completed'
    });

    showToast('Erinnerung als abgeschlossen markiert', 'success');
    showDetailsModal.value = false;

    // Reload the page to get updated data
    router.reload();
  } catch (error) {
    console.error('Fehler beim Aktualisieren der Erinnerung:', error);
    showToast('Fehler beim Aktualisieren der Erinnerung', 'error');
  } finally {
    isLoading.value = false;
  }
};

// Get events summary for the selected date
const selectedDateEvents = computed(() => {
  return getEventsForDate(selectedDate.value);
});
</script>

<template>
  <div>
    <!-- Loading overlay -->
    <div v-if="isLoading" class="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
      <div class="loading loading-spinner loading-lg text-primary"></div>
    </div>

    <!-- Main content -->
    <div class="container mx-auto px-4 py-6">
      <!-- Page header with controls -->
      <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
        <div>
          <h1 class="text-3xl font-bold text-primary">Servicekalender</h1>
          <p class="text-base-content/70 mt-1">Wartungen, Reparaturen und Serviceerinnerungen</p>
        </div>

        <div class="flex flex-wrap gap-2 items-center">
        <div class="btn-group">
          <button
            @click="toggleView('month')"
            class="btn btn-sm"
              :class="{ 'btn-primary': viewType === 'month', 'btn-ghost': viewType !== 'month' }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
              Monat
            </button>
            <button
              @click="toggleView('week')"
              class="btn btn-sm"
              :class="{ 'btn-primary': viewType === 'week', 'btn-ghost': viewType !== 'week' }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="4 7 4 4 20 4 20 7"></polyline><line x1="9" y1="20" x2="9" y2="4"></line><line x1="15" y1="20" x2="15" y2="4"></line><polyline points="4 11 20 11"></polyline><polyline points="4 16 20 16"></polyline></svg>
              Woche
            </button>
            <button
              @click="toggleView('day')"
              class="btn btn-sm"
              :class="{ 'btn-primary': viewType === 'day', 'btn-ghost': viewType !== 'day' }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line><line x1="3" y1="16" x2="21" y2="16"></line></svg>
              Tag
          </button>
          <button
            @click="toggleView('list')"
            class="btn btn-sm"
              :class="{ 'btn-primary': viewType === 'list', 'btn-ghost': viewType !== 'list' }"
          >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="8" y1="6" x2="21" y2="6"></line><line x1="8" y1="12" x2="21" y2="12"></line><line x1="8" y1="18" x2="21" y2="18"></line><line x1="3" y1="6" x2="3.01" y2="6"></line><line x1="3" y1="12" x2="3.01" y2="12"></line><line x1="3" y1="18" x2="3.01" y2="18"></line></svg>
              Liste
          </button>
        </div>

        <div class="dropdown dropdown-end">
            <label tabindex="0" class="btn btn-sm btn-outline gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"/></svg>
            Filter
          </label>
            <div tabindex="0" class="dropdown-content z-[1] p-3 shadow-xl bg-base-200 rounded-box w-72">
              <h3 class="font-semibold mb-2">Status Filter</h3>
              <div class="divider my-1"></div>

            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-2">
                  <input type="checkbox" v-model="filters.showPendingReminders" class="checkbox checkbox-sm checkbox-info" />
                  <span class="label-text">Ausstehende Termine</span>
                  <div class="badge badge-info badge-sm ml-auto">{{ props.reminders?.filter(r => r.status === 'pending').length || 0 }}</div>
              </label>
            </div>

            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-2">
                  <input type="checkbox" v-model="filters.showOverdueReminders" class="checkbox checkbox-sm checkbox-error" />
                  <span class="label-text">Überfällige Termine</span>
                  <div class="badge badge-error badge-sm ml-auto">{{ props.reminders?.filter(r => r.status === 'overdue').length || 0 }}</div>
              </label>
            </div>

            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-2">
                  <input type="checkbox" v-model="filters.showCompletedReminders" class="checkbox checkbox-sm checkbox-success" />
                  <span class="label-text">Abgeschlossene Termine</span>
                  <div class="badge badge-success badge-sm ml-auto">{{ props.reminders?.filter(r => r.status === 'completed').length || 0 }}</div>
              </label>
            </div>

            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-2">
                  <input type="checkbox" v-model="filters.showMaintenanceLogs" class="checkbox checkbox-sm checkbox-primary" />
                  <span class="label-text">Wartungseinträge</span>
                  <div class="badge badge-primary badge-sm ml-auto">{{ props.maintenance_logs?.length || 0 }}</div>
              </label>
            </div>

              <h3 class="font-semibold mt-4 mb-2">Fahrzeug</h3>
              <div class="divider my-1"></div>

              <select v-model="filters.vehicleFilter" class="select select-sm select-bordered w-full">
                <option value="all">Alle Fahrzeuge</option>
                <option v-for="vehicle in vehicles" :key="vehicle.id" :value="vehicle.id">
                  {{ vehicle.make }} {{ vehicle.model }} {{ vehicle.license_plate ? `(${vehicle.license_plate})` : '' }}
                </option>
              </select>

              <div class="mt-4 flex justify-end">
                <button @click="resetFilters" class="btn btn-sm btn-ghost">Zurücksetzen</button>
          </div>
        </div>
          </div>

          <button @click="openCreateEventModal" class="btn btn-sm btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            Neuer Termin
          </button>
      </div>
    </div>

      <!-- Calendar header with date navigation -->
      <div class="card bg-base-100 shadow-xl mb-6">
        <div class="card-body p-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-3">
              <button @click="previousMonth" class="btn btn-circle btn-sm btn-ghost">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 18l-6-6 6-6"/></svg>
              </button>

              <div class="dropdown">
                <label tabindex="0" class="text-xl font-semibold hover:underline cursor-pointer">
                  {{ viewType === 'month' ? monthNames[currentMonth] + ' ' + currentYear :
                     viewType === 'week' ? 'KW ' + Math.ceil((selectedDate.getDate() + 6 - selectedDate.getDay()) / 7) + ', ' + monthNames[selectedDate.getMonth()] + ' ' + selectedDate.getFullYear() :
                     viewType === 'day' ? selectedDate.getDate() + '. ' + monthNames[selectedDate.getMonth()] + ' ' + selectedDate.getFullYear() :
                     'Alle Termine' }}
                </label>
                <div tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                  <div class="grid grid-cols-3 gap-1">
                    <button
                      v-for="(month, index) in monthNames"
                      :key="month"
                      @click="currentMonth = index; updateCurrentDate()"
                      class="btn btn-xs"
                      :class="{ 'btn-primary': currentMonth === index }"
                    >
                      {{ month.substring(0, 3) }}
                    </button>
                  </div>
                  <div class="divider my-1"></div>
                  <div class="grid grid-cols-4 gap-1">
                    <button
                      v-for="year in [currentYear - 1, currentYear, currentYear + 1, currentYear + 2]"
                      :key="year"
                      @click="currentYear = year; updateCurrentDate()"
                      class="btn btn-xs"
                      :class="{ 'btn-primary': currentYear === year }"
                    >
                      {{ year }}
                    </button>
                  </div>
                </div>
              </div>

              <button @click="nextMonth" class="btn btn-circle btn-sm btn-ghost">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
              </button>

              <button @click="goToToday" class="btn btn-sm btn-outline ml-2">
                Heute
              </button>
            </div>

            <div class="stats shadow stats-horizontal bg-base-200">
              <div class="stat py-2 px-4">
                <div class="stat-title text-xs">Anstehend</div>
                <div class="stat-value text-lg text-info">{{ props.reminders?.filter(r => r.status === 'pending').length || 0 }}</div>
              </div>
              <div class="stat py-2 px-4">
                <div class="stat-title text-xs">Überfällig</div>
                <div class="stat-value text-lg text-error">{{ props.reminders?.filter(r => r.status === 'overdue').length || 0 }}</div>
              </div>
              <div class="stat py-2 px-4">
                <div class="stat-title text-xs">Erledigt</div>
                <div class="stat-value text-lg text-success">{{ props.reminders?.filter(r => r.status === 'completed').length || 0 }}</div>
              </div>
            </div>
          </div>
            </div>
          </div>

      <!-- Main calendar area -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Month View -->
        <div v-if="viewType === 'month'" class="card bg-base-100 shadow-xl lg:col-span-3 overflow-hidden">
          <div class="card-body p-0">
            <!-- Weekday headers -->
            <div class="grid grid-cols-7 bg-base-200">
              <div
                v-for="day in weekDays"
                :key="day"
                class="p-2 text-center font-medium border-b"
              >
              {{ day }}
              </div>
            </div>

            <!-- Calendar days -->
            <div class="grid grid-cols-7 auto-rows-fr">
            <div
              v-for="(day, index) in calendarDays"
              :key="index"
                class="p-1 border relative min-h-[100px]"
              :class="{
                  'bg-base-200 bg-opacity-50': !day.isCurrentMonth,
                  'bg-primary bg-opacity-5': day.isToday && !day.isSelected,
                  'bg-primary bg-opacity-10 border-primary': day.isSelected,
                  'cursor-pointer hover:bg-base-200': day.isCurrentMonth && !day.isSelected
                }"
                @click="selectDate(day.date)"
            >
              <div class="flex justify-between items-start">
                <span
                    class="text-sm font-medium rounded-full w-7 h-7 flex items-center justify-center"
                  :class="{
                    'bg-primary text-primary-content': day.isToday,
                      'bg-primary bg-opacity-20': day.isSelected && !day.isToday,
                    'opacity-40': !day.isCurrentMonth
                  }"
                >
                  {{ day.date.getDate() }}
                </span>

                  <span v-if="day.events.length > 0" class="badge badge-sm">
                    {{ day.events.length }}
                  </span>
              </div>

                <!-- Events for this day -->
                <div class="mt-1 space-y-1 max-h-[80px] overflow-y-auto pr-1 text-xs">
                  <div
                    v-for="(event, eventIndex) in day.events.slice(0, 3)"
                    :key="`${event.type}-${event.id}-${eventIndex}`"
                    @click.stop="openEventDetails(event)"
                    class="p-1 rounded cursor-pointer flex items-center gap-1 truncate"
                    :class="{
                      'bg-info bg-opacity-20 hover:bg-opacity-30': event.type === 'reminder' && event.status === 'pending',
                      'bg-success bg-opacity-20 hover:bg-opacity-30': event.type === 'reminder' && event.status === 'completed',
                      'bg-error bg-opacity-20 hover:bg-opacity-30': event.type === 'reminder' && event.status === 'overdue',
                      'bg-primary bg-opacity-20 hover:bg-opacity-30': event.type === 'maintenance'
                    }"
                  >
                    <div class="w-2 h-2 rounded-full shrink-0"
                      :class="{
                        'bg-info': event.type === 'reminder' && event.status === 'pending',
                        'bg-success': event.type === 'reminder' && event.status === 'completed',
                        'bg-error': event.type === 'reminder' && event.status === 'overdue',
                        'bg-primary': event.type === 'maintenance'
                      }"
                    ></div>

                    <span>{{ event.title }}</span>
                  </div>

                  <div v-if="day.events.length > 3" class="text-xs text-right italic">
                    + {{ day.events.length - 3 }} weitere
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Week View -->
        <div v-if="viewType === 'week'" class="card bg-base-100 shadow-xl lg:col-span-3">
          <div class="card-body p-0">
            <!-- Week days header -->
            <div class="grid grid-cols-7 bg-base-200">
              <div
                v-for="(day, index) in weekDaysForCurrentWeek"
                :key="index"
                class="p-2 text-center font-medium border-b flex flex-col"
                :class="{ 'bg-primary bg-opacity-10': day.isToday }"
              >
                <span>{{ weekDays[day.date.getDay()] }}</span>
                <span
                  class="text-base rounded-full w-7 h-7 flex items-center justify-center mx-auto mt-1"
                  :class="{ 'bg-primary text-primary-content': day.isToday }"
                >
                  {{ day.date.getDate() }}
                </span>
              </div>
            </div>

            <!-- Week schedule -->
            <div class="overflow-y-auto max-h-[600px]">
              <div class="grid grid-cols-7 min-h-full">
                <div
                  v-for="(day, dayIndex) in weekDaysForCurrentWeek"
                  :key="dayIndex"
                  class="border-r relative min-h-[600px] p-1"
                  :class="{ 'bg-primary bg-opacity-5': day.isToday }"
                >
                  <div
                    v-for="(event, eventIndex) in day.events"
                    :key="`week-${dayIndex}-${eventIndex}`"
                  @click="openEventDetails(event)"
                    class="p-2 mb-1 rounded shadow-sm cursor-pointer border-l-4 text-xs"
                  :class="{
                      'border-info bg-info bg-opacity-10': event.type === 'reminder' && event.status === 'pending',
                      'border-success bg-success bg-opacity-10': event.type === 'reminder' && event.status === 'completed',
                      'border-error bg-error bg-opacity-10': event.type === 'reminder' && event.status === 'overdue',
                      'border-primary bg-primary bg-opacity-10': event.type === 'maintenance'
                    }"
                  >
                    <div class="font-medium mb-1 truncate">{{ event.title }}</div>
                    <div class="flex justify-between items-center">
                      <span class="opacity-70">{{ event.time || '09:00' }}</span>
                      <span class="badge badge-sm" :class="{
                        'badge-info': event.type === 'reminder' && event.status === 'pending',
                        'badge-success': event.type === 'reminder' && event.status === 'completed',
                        'badge-error': event.type === 'reminder' && event.status === 'overdue',
                        'badge-primary': event.type === 'maintenance'
                      }">
                        {{ event.type === 'reminder' ? getStatusName(event.status) : getMaintenanceTypeName(event.logType) }}
                      </span>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

        <!-- Day View -->
        <div v-if="viewType === 'day'" class="card bg-base-100 shadow-xl lg:col-span-3">
          <div class="card-body p-4">
            <h2 class="text-xl font-semibold mb-4">
              {{ selectedDate.getDate() }} {{ monthNames[selectedDate.getMonth()] }} {{ selectedDate.getFullYear() }}
              <span v-if="selectedDate.toDateString() === today.toDateString()" class="badge badge-primary ml-2">Heute</span>
            </h2>

            <div v-if="selectedDateEvents.length === 0" class="alert alert-info">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
              <span>Keine Termine für diesen Tag gefunden.</span>
              <button @click="openCreateEventModal" class="btn btn-sm btn-ghost">Termin hinzufügen</button>
          </div>

            <div v-else class="space-y-4 max-h-[600px] overflow-y-auto">
              <div
                v-for="hour in hoursForDayView.filter(h => h.events.length > 0)"
                :key="`hour-${hour.hour}`"
                class="border-l-4 pl-4 py-2 relative"
                :class="{
                  'border-info': hour.hour >= 8 && hour.hour < 12,
                  'border-warning': hour.hour >= 12 && hour.hour < 17,
                  'border-primary': hour.hour >= 17 || hour.hour < 8
                }"
              >
                <div class="absolute -left-3 top-2 px-1 bg-base-100 rounded text-sm font-medium">
                  {{ hour.label }}
                </div>

                <div class="ml-4 space-y-2">
                  <div
                    v-for="(event, eventIndex) in hour.events"
                    :key="`day-${hour.hour}-${eventIndex}`"
                    @click="openEventDetails(event)"
                    class="card shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                  >
              <div class="card-body p-3">
                <div class="flex justify-between items-start">
                        <h3 class="font-medium text-base">{{ event.title }}</h3>
                        <div class="badge" :class="{
                          'badge-info': event.type === 'reminder' && event.status === 'pending',
                          'badge-success': event.type === 'reminder' && event.status === 'completed',
                          'badge-error': event.type === 'reminder' && event.status === 'overdue',
                          'badge-primary': event.type === 'maintenance'
                        }">
                          {{ event.type === 'reminder' ? getStatusName(event.status) : getMaintenanceTypeName(event.logType) }}
                  </div>
                </div>

                      <div class="flex items-center text-sm gap-2">
                        <span class="opacity-70">{{ event.vehicle.make }} {{ event.vehicle.model }}</span>
                        <span v-if="event.vehicle.license_plate" class="badge badge-sm badge-outline">
                          {{ event.vehicle.license_plate }}
                        </span>
                      </div>

                      <div v-if="event.notes" class="mt-2 text-sm opacity-80 line-clamp-2">
                        {{ event.notes }}
                      </div>

                <div class="card-actions justify-end mt-2">
                        <Link
                          :href="route('vehicles.show', event.vehicle.id)"
                          class="btn btn-xs btn-ghost"
                          @click.stop
                        >
                    Fahrzeug
                  </Link>
                        <button
                          v-if="event.type === 'reminder' && event.status !== 'completed'"
                          @click.stop="completeReminder(event.id)"
                          class="btn btn-xs btn-success"
                        >
                          Erledigt
                        </button>
                      </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

        <!-- List View (All events) -->
        <div v-if="viewType === 'list'" class="card bg-base-100 shadow-xl lg:col-span-3">
      <div class="card-body">
            <div class="tabs tabs-boxed mb-4">
              <a
                class="tab"
                :class="{ 'tab-active': filters.typeFilter === 'all' }"
                @click="filters.typeFilter = 'all'"
              >
                Alle
              </a>
              <a
                class="tab"
                :class="{ 'tab-active': filters.typeFilter === 'service' }"
                @click="filters.typeFilter = 'service'"
              >
                Wartung
              </a>
              <a
                class="tab"
                :class="{ 'tab-active': filters.typeFilter === 'repair' }"
                @click="filters.typeFilter = 'repair'"
              >
                Reparatur
              </a>
              <a
                class="tab"
                :class="{ 'tab-active': filters.typeFilter === 'inspection' }"
                @click="filters.typeFilter = 'inspection'"
              >
                TÜV/HU
              </a>
            </div>

        <!-- Servicerinnerungen -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-2">Serviceerinnerungen</h3>

          <div v-if="upcomingReminders.length === 0" class="alert alert-info">
                <div>Keine Serviceerinnerungen mit den aktuellen Filtereinstellungen gefunden.</div>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Titel</th>
                  <th>Fahrzeug</th>
                  <th>Fällig am</th>
                  <th>Status</th>
                  <th>Priorität</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                    <tr
                      v-for="reminder in props.reminders.filter(r => {
                        // Apply all filters
                        if (r.status === 'pending' && !filters.showPendingReminders) return false;
                        if (r.status === 'overdue' && !filters.showOverdueReminders) return false;
                        if (r.status === 'completed' && !filters.showCompletedReminders) return false;
                        if (filters.vehicleFilter !== 'all' && r.vehicle_id !== parseInt(filters.vehicleFilter)) return false;
                        return true;
                      })"
                      :key="reminder.id"
                      class="hover:bg-base-200 cursor-pointer"
                      @click="openEventDetails({
                        id: reminder.id,
                        title: reminder.title,
                        type: 'reminder',
                        status: reminder.status,
                        priority: reminder.priority,
                        vehicle: reminder.vehicle,
                        notes: reminder.notes,
                        data: reminder
                      })"
                    >
                  <td>{{ reminder.title }}</td>
                  <td>
                    {{ reminder.vehicle.make }} {{ reminder.vehicle.model }}
                    <span v-if="reminder.vehicle.license_plate" class="badge badge-sm">{{ reminder.vehicle.license_plate }}</span>
                  </td>
                  <td>{{ formatDate(reminder.due_date) }}</td>
                  <td>
                    <div class="badge" :class="getStatusClass(reminder.status)">
                          {{ getStatusName(reminder.status) }}
                    </div>
                  </td>
                  <td>
                    <div class="badge" :class="getPriorityClass(reminder.priority)">
                          {{ getPriorityName(reminder.priority) }}
                    </div>
                  </td>
                  <td>
                        <div class="flex gap-1">
                          <Link
                            :href="route('vehicles.show', reminder.vehicle_id)"
                            class="btn btn-xs btn-ghost"
                            @click.stop
                          >
                            Fahrzeug
                    </Link>
                          <button
                            v-if="reminder.status !== 'completed'"
                            @click.stop="completeReminder(reminder.id)"
                            class="btn btn-xs btn-success"
                          >
                            Erledigt
                          </button>
                        </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Wartungseinträge -->
        <div>
              <h3 class="text-lg font-medium mb-2">Wartungseinträge</h3>

              <div v-if="!props.maintenance_logs || props.maintenance_logs.length === 0" class="alert alert-info">
                <div>Keine Wartungseinträge gefunden.</div>
              </div>

              <div v-else-if="!filters.showMaintenanceLogs" class="alert alert-warning">
                <div>Wartungseinträge sind in den Filtereinstellungen ausgeblendet.</div>
          </div>

          <div v-else class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Titel</th>
                  <th>Fahrzeug</th>
                  <th>Datum</th>
                  <th>Typ</th>
                      <th>Kosten</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                    <tr
                      v-for="log in props.maintenance_logs.filter(log => {
                        if (!filters.showMaintenanceLogs) return false;
                        if (filters.vehicleFilter !== 'all' && log.vehicle_id !== parseInt(filters.vehicleFilter)) return false;
                        if (filters.typeFilter !== 'all' && log.type !== filters.typeFilter) return false;
                        return true;
                      })"
                      :key="log.id"
                      class="hover:bg-base-200 cursor-pointer"
                      @click="openEventDetails({
                        id: log.id,
                        title: log.title,
                        type: 'maintenance',
                        logType: log.type,
                        vehicle: log.vehicle,
                        notes: log.notes,
                        data: log
                      })"
                    >
                  <td>{{ log.title }}</td>
                  <td>
                    {{ log.vehicle.make }} {{ log.vehicle.model }}
                    <span v-if="log.vehicle.license_plate" class="badge badge-sm">{{ log.vehicle.license_plate }}</span>
                  </td>
                  <td>{{ formatDate(log.date) }}</td>
                  <td>
                    <div class="badge" :class="getMaintenanceTypeClass(log.type)">
                      {{ getMaintenanceTypeName(log.type) }}
                    </div>
                  </td>
                      <td>{{ log.cost ? `${log.cost.toFixed(2)} €` : '-' }}</td>
                      <td>
                        <Link
                          :href="route('maintenance-logs.show', log.id)"
                          class="btn btn-xs btn-ghost"
                          @click.stop
                        >
                      Details
                    </Link>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

        <!-- Sidebar with upcoming events -->
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h3 class="text-lg font-medium mb-4">Anstehende Termine</h3>

            <div v-if="upcomingReminders.length === 0" class="alert alert-info">
              <div>Keine anstehenden Termine gefunden.</div>
              </div>

            <div v-else class="space-y-3">
              <div
                v-for="reminder in upcomingReminders"
                :key="reminder.id"
                class="card bg-base-200 hover:shadow-md transition-shadow cursor-pointer"
                @click="openEventDetails({
                  id: reminder.id,
                  title: reminder.title,
                  type: 'reminder',
                  status: reminder.status,
                  priority: reminder.priority,
                  vehicle: reminder.vehicle,
                  notes: reminder.notes,
                  data: reminder
                })"
              >
                <div class="card-body p-3">
                  <div class="flex justify-between items-start">
                    <h4 class="font-medium">{{ reminder.title }}</h4>
                    <div class="badge" :class="getStatusClass(reminder.status)">
                      {{ getStatusName(reminder.status) }}
                </div>
                </div>

                  <div class="text-sm flex items-center gap-1">
                    {{ reminder.vehicle.make }} {{ reminder.vehicle.model }}
                    <span v-if="reminder.vehicle.license_plate" class="badge badge-sm badge-outline">
                      {{ reminder.vehicle.license_plate }}
                  </span>
                </div>

                  <div class="text-sm">Fällig am: {{ formatDate(reminder.due_date) }}</div>

                  <div class="card-actions justify-end mt-2 flex-wrap gap-1">
                    <Link :href="route('vehicles.show', reminder.vehicle_id)" class="btn btn-xs btn-ghost" @click.stop>
                      Fahrzeug
                    </Link>
                    <button
                      v-if="reminder.status !== 'completed'"
                      @click.stop="completeReminder(reminder.id)"
                      class="btn btn-xs btn-success"
                    >
                      Erledigt
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="divider"></div>

            <button @click="openCreateEventModal" class="btn btn-primary btn-sm w-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
              Neuer Termin
            </button>
          </div>
        </div>
      </div>

      <!-- Event-Details Modal -->
      <div v-if="showDetailsModal" class="modal modal-open">
        <div class="modal-box max-w-2xl">
          <div v-if="!isEditMode">
            <div class="flex justify-between">
              <h3 class="font-bold text-xl" v-if="selectedEvent">{{ selectedEvent.title }}</h3>
              <div class="badge badge-lg" v-if="selectedEvent" :class="{
                'badge-info': selectedEvent.type === 'reminder' && selectedEvent.status === 'pending',
                'badge-success': selectedEvent.type === 'reminder' && selectedEvent.status === 'completed',
                'badge-error': selectedEvent.type === 'reminder' && selectedEvent.status === 'overdue',
                'badge-primary': selectedEvent.type === 'maintenance'
              }">
                {{ selectedEvent.type === 'reminder' ? getStatusName(selectedEvent.status) : getMaintenanceTypeName(selectedEvent.logType) }}
              </div>
            </div>

            <div v-if="selectedEvent" class="py-4 space-y-4">
              <div class="flex flex-col md:flex-row gap-8">
                <div class="flex-1 space-y-3">
                  <div class="bg-base-200 p-4 rounded-lg">
                    <div class="font-medium mb-2">Fahrzeug</div>
                    <div class="flex items-center gap-2">
                      <span>{{ selectedEvent.vehicle.make }} {{ selectedEvent.vehicle.model }}</span>
                      <span v-if="selectedEvent.vehicle.license_plate" class="badge badge-sm">{{ selectedEvent.vehicle.license_plate }}</span>
                    </div>
                    <Link :href="route('vehicles.show', selectedEvent.vehicle.id)" class="btn btn-xs btn-outline mt-2">
                      Zum Fahrzeug
                    </Link>
                  </div>

                  <div v-if="selectedEvent.type === 'reminder'" class="space-y-3">
                    <div class="bg-base-200 p-4 rounded-lg">
                      <div class="font-medium mb-2">Termindaten</div>
                      <div class="grid grid-cols-2 gap-2">
                <div>
                          <span class="opacity-70">Fällig am:</span>
                          <div>{{ formatDate(selectedEvent.data.due_date) }}</div>
                        </div>
                        <div>
                          <span class="opacity-70">Priorität:</span>
                          <div>
                  <span class="badge" :class="getPriorityClass(selectedEvent.priority)">
                              {{ getPriorityName(selectedEvent.priority) }}
                  </span>
                          </div>
                        </div>
                      </div>
                </div>
              </div>

                  <div v-else-if="selectedEvent.type === 'maintenance'" class="space-y-3">
                    <div class="bg-base-200 p-4 rounded-lg">
                      <div class="font-medium mb-2">Wartungsdaten</div>
                      <div class="grid grid-cols-2 gap-2">
                <div>
                          <span class="opacity-70">Datum:</span>
                          <div>{{ formatDate(selectedEvent.data.date) }}</div>
                        </div>
                        <div>
                          <span class="opacity-70">Typ:</span>
                          <div>
                  <span class="badge" :class="getMaintenanceTypeClass(selectedEvent.logType)">
                    {{ getMaintenanceTypeName(selectedEvent.logType) }}
                  </span>
                          </div>
                        </div>
                        <div v-if="selectedEvent.data.mileage">
                          <span class="opacity-70">Kilometerstand:</span>
                          <div>{{ selectedEvent.data.mileage.toLocaleString() }} km</div>
                        </div>
                        <div v-if="selectedEvent.data.cost">
                          <span class="opacity-70">Kosten:</span>
                          <div>{{ selectedEvent.data.cost.toFixed(2) }} €</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flex-1">
                  <div class="bg-base-200 p-4 rounded-lg h-full">
                    <div class="font-medium mb-2">Notizen</div>
                    <div v-if="selectedEvent.notes" class="whitespace-pre-wrap">{{ selectedEvent.notes }}</div>
                    <div v-else class="opacity-70 italic">Keine Notizen vorhanden</div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-action">
            <button class="btn btn-ghost" @click="showDetailsModal = false">Schließen</button>
              <button v-if="selectedEvent && selectedEvent.type === 'reminder' && selectedEvent.status !== 'completed'"
                class="btn btn-success" @click="completeReminder(selectedEvent.id)">
                Als erledigt markieren
              </button>
              <button v-if="selectedEvent" class="btn btn-primary" @click="editEvent(selectedEvent)">
                Bearbeiten
              </button>
            <Link
              v-if="selectedEvent && selectedEvent.type === 'maintenance'"
              :href="route('maintenance-logs.show', selectedEvent.id)"
                class="btn btn-secondary"
            >
              Details anzeigen
            </Link>
          </div>
        </div>

          <!-- Edit Mode -->
          <div v-else>
            <h3 class="font-bold text-xl mb-4">Termin bearbeiten</h3>

            <form @submit.prevent="updateEvent" class="space-y-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Titel</span>
                </label>
                <input v-model="editedEvent.title" type="text" class="input input-bordered" required />
      </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">Datum</span>
                </label>
                <input v-model="editedEvent.date" type="date" class="input input-bordered" required />
              </div>

              <div v-if="selectedEvent && selectedEvent.type === 'reminder'" class="grid grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Status</span>
                  </label>
                  <select v-model="editedEvent.status" class="select select-bordered">
                    <option value="pending">Ausstehend</option>
                    <option value="completed">Abgeschlossen</option>
                    <option value="overdue">Überfällig</option>
                  </select>
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Priorität</span>
                  </label>
                  <select v-model="editedEvent.priority" class="select select-bordered">
                    <option value="low">Niedrig</option>
                    <option value="medium">Mittel</option>
                    <option value="high">Hoch</option>
                  </select>
                </div>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">Notizen</span>
                </label>
                <textarea v-model="editedEvent.notes" class="textarea textarea-bordered h-24"></textarea>
              </div>

              <div class="modal-action">
                <button type="button" class="btn btn-ghost" @click="isEditMode = false">Abbrechen</button>
                <button type="submit" class="btn btn-primary" :disabled="isLoading">Speichern</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Create Event Modal -->
      <div v-if="showCreateEventModal" class="modal modal-open">
        <div class="modal-box">
          <h3 class="font-bold text-xl mb-4">Neuer Termin</h3>

          <form @submit.prevent="createEvent" class="space-y-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Art des Eintrags</span>
              </label>
              <select v-model="newEvent.type" class="select select-bordered">
                <option value="reminder">Servicerinnerung</option>
                <option value="service">Wartung</option>
                <option value="repair">Reparatur</option>
                <option value="inspection">TÜV/HU</option>
                <option value="modification">Umbau</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Fahrzeug</span>
              </label>
              <select v-model="newEvent.vehicle_id" class="select select-bordered" required>
                <option disabled value="null">Bitte wählen...</option>
                <option v-for="vehicle in vehicles" :key="vehicle.id" :value="vehicle.id">
                  {{ vehicle.make }} {{ vehicle.model }} {{ vehicle.license_plate ? `(${vehicle.license_plate})` : '' }}
                </option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Titel</span>
              </label>
              <input v-model="newEvent.title" type="text" class="input input-bordered" required />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Datum</span>
              </label>
              <input v-model="newEvent.date" type="date" class="input input-bordered" required />
            </div>

            <div v-if="newEvent.type === 'reminder'" class="form-control">
              <label class="label">
                <span class="label-text">Priorität</span>
              </label>
              <select v-model="newEvent.priority" class="select select-bordered">
                <option value="low">Niedrig</option>
                <option value="medium">Mittel</option>
                <option value="high">Hoch</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Notizen</span>
              </label>
              <textarea v-model="newEvent.notes" class="textarea textarea-bordered h-24"></textarea>
            </div>

            <div v-if="newEvent.type === 'reminder'" class="form-control">
              <label class="label cursor-pointer justify-start gap-2">
                <input type="checkbox" v-model="newEvent.is_recurring" class="checkbox checkbox-sm" />
                <span class="label-text">Wiederkehrend</span>
              </label>
            </div>

            <div v-if="newEvent.is_recurring" class="grid grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Häufigkeit</span>
                </label>
                <select v-model="newEvent.recurrence_pattern" class="select select-bordered">
                  <option value="daily">Täglich</option>
                  <option value="weekly">Wöchentlich</option>
                  <option value="monthly">Monatlich</option>
                  <option value="yearly">Jährlich</option>
                </select>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">Intervall</span>
                </label>
                <input v-model.number="newEvent.recurrence_interval" type="number" min="1" class="input input-bordered" />
              </div>
            </div>

            <div class="modal-action">
              <button type="button" class="btn btn-ghost" @click="showCreateEventModal = false">Abbrechen</button>
              <button type="submit" class="btn btn-primary" :disabled="isLoading || !newEvent.vehicle_id">Erstellen</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
