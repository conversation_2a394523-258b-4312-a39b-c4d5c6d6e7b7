<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';

const props = defineProps({
    subscription: Object,
    planDetails: Object
});

// Formatiert den Preis für die Anzeige
const formatPrice = (price) => {
    if (!price) return '–';
    return `€${price.toFixed(2)}`;
};

// Berechnet das nächste Abrechnungsdatum
const getNextBillingDate = () => {
    if (!props.subscription || !props.subscription.ends_at) return null;
    
    const date = new Date(props.subscription.ends_at);
    return date.toLocaleDateString('de-DE', { 
        day: '2-digit', 
        month: '2-digit', 
        year: 'numeric' 
    });
};
</script>

<template>
    <AppLayout title="Abonnement abgeschlossen">
        <Head title="Abonnement abgeschlossen" />

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <div class="text-center">
                        <div class="flex justify-center items-center mb-6">
                            <div class="bg-green-100 dark:bg-green-900 rounded-full p-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-green-500 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                        
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-3">Vielen Dank für Ihr Abonnement!</h1>
                        <p class="text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-xl mx-auto">
                            Ihr Abonnement wurde erfolgreich verarbeitet und ist jetzt aktiv.
                        </p>
                        
                        <!-- Abonnementdetails -->
                        <div class="mb-8 max-w-md mx-auto">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
                                <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Ihr Abonnement</h2>
                                
                                <div class="grid grid-cols-1 gap-4 text-left">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600 dark:text-gray-400">Plan:</span>
                                        <span class="font-medium text-gray-900 dark:text-white">{{ planDetails?.name || 'Standard' }}</span>
                                    </div>
                                    
                                    <div v-if="planDetails?.price" class="flex justify-between items-center">
                                        <span class="text-gray-600 dark:text-gray-400">Preis:</span>
                                        <span class="font-medium text-gray-900 dark:text-white">{{ formatPrice(planDetails.price) }}
                                            <span class="text-sm text-gray-500 dark:text-gray-400">/{{ planDetails.interval || 'monat' }}</span>
                                        </span>
                                    </div>
                                    
                                    <div v-if="subscription?.created_at" class="flex justify-between items-center">
                                        <span class="text-gray-600 dark:text-gray-400">Startdatum:</span>
                                        <span class="font-medium text-gray-900 dark:text-white">
                                            {{ new Date(subscription.created_at).toLocaleDateString('de-DE') }}
                                        </span>
                                    </div>
                                    
                                    <div v-if="getNextBillingDate()" class="flex justify-between items-center">
                                        <span class="text-gray-600 dark:text-gray-400">Nächste Abrechnung:</span>
                                        <span class="font-medium text-gray-900 dark:text-white">{{ getNextBillingDate() }}</span>
                                    </div>
                                    
                                    <div v-if="subscription?.stripe_status" class="flex justify-between items-center">
                                        <span class="text-gray-600 dark:text-gray-400">Status:</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                            Aktiv
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-6">
                            <Button size="lg" class="group transition-all duration-300 hover:shadow-lg bg-primary hover:bg-primary-600 text-white" asChild>
                                <Link :href="route('dashboard')" class="flex items-center px-6 py-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 group-hover:translate-x-1 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                    </svg>
                                    <span class="font-medium">Zum Dashboard</span>
                                </Link>
                            </Button>
                            <Button variant="outline" size="lg" class="group border-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 text-gray-800 dark:text-white" asChild>
                                <Link :href="route('subscription.manage')" class="flex items-center px-6 py-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 group-hover:rotate-12 transition-transform" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="font-medium">Abonnement verwalten</span>
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template> 