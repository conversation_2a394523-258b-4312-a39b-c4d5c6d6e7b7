<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';

// Direkter Import der Button-Komponente
import Button from '@/components/ui/button/Button.vue';

const props = defineProps({
    intent: Object,
    plans: Array,
});

// Session-Fehler abfangen
const page = usePage();
const flashError = ref('');
const flashSuccess = ref('');

// Bei Initialisierung und Updates prüfen, ob Flash-Nachrichten vorliegen
watch(() => page.props.flash, (flash) => {
    if (flash && flash.error) {
        flashError.value = flash.error;
        // Nach 5 Sekunden ausblenden
        setTimeout(() => {
            flashError.value = '';
        }, 5000);
    }
    if (flash && flash.success) {
        flashSuccess.value = flash.success;
        // Nach 5 Sekunden ausblenden
        setTimeout(() => {
            flashSuccess.value = '';
        }, 5000);
    }
}, { immediate: true, deep: true });

const selectedPlan = ref(null);
const stripe = ref(null);
const elements = ref(null);
// Separate Elemente für jeden Teil der Kreditkarte
const cardNumberElement = ref(null);
const cardExpiryElement = ref(null);
const cardCvcElement = ref(null);
const errorMessages = ref({
    cardNumber: '',
    cardExpiry: '',
    cardCvc: '',
    general: ''
});
const processing = ref(false);
const isStripeLoaded = ref(false);
const isCardMounted = ref(false);
const isLoadingStripe = ref(false);

const form = useForm({
    payment_method: '',
    plan_id: '',
});

// Stripe dynamisch laden
const loadStripeScript = () => {
    if (isLoadingStripe.value) return;
    
    isLoadingStripe.value = true;
    console.log('Versuche Stripe zu laden...');
    
    // Prüfen, ob Stripe bereits geladen ist
    if (window.Stripe) {
        console.log('Stripe bereits im Fenster vorhanden');
        initStripe();
        isLoadingStripe.value = false;
        return;
    }
    
    // Script-Element erstellen und Stripe-Bibliothek laden
    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    script.async = true;
    
    script.onload = () => {
        console.log('Stripe-Skript erfolgreich geladen');
        // Ein kurzes Timeout hinzufügen, um sicherzustellen, dass Stripe vollständig geladen ist
        setTimeout(() => {
            initStripe();
            isLoadingStripe.value = false;
        }, 300);
    };
    
    script.onerror = () => {
        console.error('Fehler beim Laden des Stripe-Skripts');
        errorMessages.value.general = 'Fehler beim Laden der Zahlungsverarbeitung';
        isLoadingStripe.value = false;
    };
    
    document.head.appendChild(script);
};

// Initialisiere Stripe
onMounted(() => {
    loadStripeScript();
});

// Überwache Änderungen am ausgewählten Plan
watch(selectedPlan, (newVal) => {
    if (newVal && !isCardMounted.value) {
        // Nochmals versuchen, wenn Plan ausgewählt wird
        if (!isStripeLoaded.value) {
            loadStripeScript();
        } else {
            mountCardElements();
        }
    }
});

const initStripe = () => {
    if (window.Stripe) {
        console.log('Stripe wird initialisiert');
        
        try {
            // Live-Umgebung: Verwenden des Live-Keys
            const stripeKey = 'pk_live_51RAud7FbLhupDbC8U8ACJTc7Yo0mY2aQeGKbP7NEAoE0lKBWOqRYOCGAOCbNuymqmdeqQ0QvPNJkgxv2K5NtTNJQ00AcAPMQ05';
            console.log('Verwende Stripe-Key:', stripeKey);
            
            // Bestimmen, ob wir uns im Test-Modus befinden
            const isTestMode = stripeKey.startsWith('pk_test');
            console.log('Stripe Test-Modus:', isTestMode ? 'Ja' : 'Nein');
            
            // Stripe initialisieren und API-Version explizit festlegen
            stripe.value = window.Stripe(stripeKey, {
                apiVersion: '2023-10-16', // Neueste stabile API-Version
                locale: 'de'             // Deutschsprachige Fehlermeldungen
            });
            
            // Elements mit optimierten Einstellungen erstellen
            elements.value = stripe.value.elements({
                locale: 'de',            // Deutschsprachige Fehlermeldungen
                fonts: [
                    {
                        cssSrc: 'https://fonts.googleapis.com/css?family=Roboto',
                    },
                ],
            });
            
            isStripeLoaded.value = true;
            console.log('Stripe erfolgreich geladen');
            
            // Wenn bereits ein Plan ausgewählt ist, Card Elements mounten
            if (selectedPlan.value) {
                mountCardElements();
            }
        } catch (error) {
            console.error('Fehler bei Stripe-Initialisierung:', error);
            errorMessages.value.general = 'Fehler bei der Initialisierung des Zahlungssystems: ' + (error.message || error);
        }
    } else {
        console.error('Stripe ist nicht verfügbar');
        errorMessages.value.general = 'Zahlungssystem ist nicht verfügbar';
    }
};

const mountCardElements = () => {
    if (!isStripeLoaded.value) {
        console.log('Stripe noch nicht geladen, initialisiere erneut');
        loadStripeScript();
        return;
    }
    
    if (!elements.value) {
        console.error('Elements nicht verfügbar');
        return;
    }
    
    const elementStyle = {
        base: {
            fontSize: '16px',
            color: '#32325d',
            fontFamily: '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
            '::placeholder': {
                color: '#aab7c4',
            },
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a',
        },
    };
    
    try {
        // Kreditkartennummer Element erstellen
        if (!cardNumberElement.value) {
            cardNumberElement.value = elements.value.create('cardNumber', { style: elementStyle });
            
            cardNumberElement.value.on('change', (event) => {
                errorMessages.value.cardNumber = event.error ? event.error.message : '';
            });
        }
        
        // Ablaufdatum Element erstellen
        if (!cardExpiryElement.value) {
            cardExpiryElement.value = elements.value.create('cardExpiry', { style: elementStyle });
            
            cardExpiryElement.value.on('change', (event) => {
                errorMessages.value.cardExpiry = event.error ? event.error.message : '';
            });
        }
        
        // Prüfziffer Element erstellen
        if (!cardCvcElement.value) {
            cardCvcElement.value = elements.value.create('cardCvc', { style: elementStyle });
            
            cardCvcElement.value.on('change', (event) => {
                errorMessages.value.cardCvc = event.error ? event.error.message : '';
            });
        }
        
        nextTick(() => {
            // Elemente in DOM mounten
            const cardNumberContainer = document.getElementById('card-number-element');
            const cardExpiryContainer = document.getElementById('card-expiry-element');
            const cardCvcContainer = document.getElementById('card-cvc-element');
            
            if (cardNumberContainer && cardExpiryContainer && cardCvcContainer) {
                cardNumberElement.value.mount('#card-number-element');
                cardExpiryElement.value.mount('#card-expiry-element');
                cardCvcElement.value.mount('#card-cvc-element');
                
                isCardMounted.value = true;
                console.log('Card Elements erfolgreich gemountet');
            } else {
                console.error('Ein oder mehrere Container nicht gefunden!');
            }
        });
    } catch (error) {
        console.error('Fehler beim Erstellen der Card Elements:', error);
        errorMessages.value.general = 'Fehler beim Erstellen der Zahlungsfelder';
    }
};

const selectPlan = (plan) => {
    selectedPlan.value = plan;
    form.plan_id = plan.id;
    
    // Card Elements mounten, nachdem ein Plan ausgewählt wurde
    nextTick(() => {
        console.log('Plan ausgewählt, versuche Card Elements zu mounten');
        mountCardElements();
    });
};

const subscribe = async () => {
    if (!stripe.value || !elements.value) {
        errorMessages.value.general = 'Zahlungsverarbeitung ist nicht verfügbar';
        return;
    }
    
    if (!cardNumberElement.value || !cardExpiryElement.value || !cardCvcElement.value) {
        errorMessages.value.general = 'Bitte geben Sie Ihre Karteninformationen ein';
        return;
    }
    
    processing.value = true;
    
    try {
        console.log('Erstelle Zahlungsmethode...');
        const result = await stripe.value.createPaymentMethod({
            type: 'card',
            card: cardNumberElement.value,
            billing_details: {
                // Optional: Hier könnten Name und Adresse hinzugefügt werden
            },
        });
        
        console.log('Zahlungsmethode Ergebnis:', result);
        
        // Sicheres Prüfen auf Fehler
        if (result.error) {
            errorMessages.value.general = result.error.message || 'Fehler bei der Zahlungsverarbeitung';
            processing.value = false;
            return;
        }
        
        // Sicherstellen, dass paymentMethod existiert
        if (!result.paymentMethod || !result.paymentMethod.id) {
            errorMessages.value.general = 'Keine gültige Zahlungsmethode erstellt';
            processing.value = false;
            return;
        }
        
        form.payment_method = result.paymentMethod.id;
        
        // Debug-Ausgabe
        console.log('Sende Zahlungsdaten an Server:', {
            payment_method: form.payment_method,
            plan_id: form.plan_id
        });
        
        // Submit form to server
        form.post(route('subscription.checkout'), {
            preserveScroll: true,
            onError: (errors) => {
                console.error('Form-Fehler:', errors);
                processing.value = false;
            },
            onSuccess: () => {
                console.log('Erfolgreich gesendet');
            }
        });
    } catch (e) {
        console.error('Fehler bei der Zahlungsverarbeitung:', e);
        errorMessages.value.general = 'Ein unerwarteter Fehler ist aufgetreten: ' + (e.message || e);
        processing.value = false;
    }
};
</script>

<template>
    <AppLayout title="Abonnementpläne">
        <Head title="Abonnementpläne" />

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Flash-Nachrichten -->
                <div v-if="flashError" class="alert alert-error mb-4">
                    <div class="flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>{{ flashError }}</span>
                    </div>
                </div>
                <div v-if="flashSuccess" class="alert alert-success mb-4">
                    <div class="flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>{{ flashSuccess }}</span>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h1 class="text-2xl font-semibold mb-6 text-gray-800 dark:text-white">Wählen Sie Ihren Abonnementplan</h1>
                    
                    <!-- Plans Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div 
                            v-for="plan in plans" 
                            :key="plan.id" 
                            :class="[
                                'border rounded-lg overflow-hidden shadow transition-all duration-300',
                                selectedPlan && selectedPlan.id === plan.id
                                    ? 'border-primary dark:border-primary transform scale-105'
                                    : 'border-gray-200 dark:border-gray-700 hover:shadow-md'
                            ]"
                        >
                            <div class="p-6">
                                <h3 class="card-title text-xl">{{ plan.name }}</h3>
                                <div class="mt-2 mb-4">
                                    <span class="text-2xl font-bold text-gray-900 dark:text-white">€{{ plan.price }}</span>
                                    <span class="text-gray-500 dark:text-gray-400">/ {{ plan.interval }}</span>
                                </div>
                                
                                <hr class="my-4 border-gray-200 dark:border-gray-700">
                                
                                <ul class="space-y-3 mb-6">
                                    <li v-for="feature in plan.features" :key="feature" class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span class="text-gray-700 dark:text-gray-300">{{ feature }}</span>
                                    </li>
                                </ul>
                                
                                <!-- Vereinfachter Button mit DaisyUI Klassen -->
                                <button 
                                    class="btn btn-primary w-full"
                                    @click="selectPlan(plan)" 
                                >
                                    {{ selectedPlan && selectedPlan.id === plan.id ? 'Ausgewählt' : 'Plan wählen' }}
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Form -->
                    <div v-if="selectedPlan" class="mt-8 p-6 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Zahlungsinformationen</h2>
                        <p class="mb-4 text-gray-600 dark:text-gray-400">
                            Sie abonnieren den <span class="font-semibold">{{ selectedPlan.name }}</span> Plan für
                            <span class="font-semibold">€{{ selectedPlan.price }}/{{ selectedPlan.interval }}</span>.
                        </p>
                        
                        <form @submit.prevent="subscribe" class="space-y-4">
                            <!-- Kartennummer -->
                            <div>
                                <label for="card-number-element" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Kartennummer
                                </label>
                                <div 
                                    id="card-number-element" 
                                    class="p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 min-h-[40px]"
                                >
                                    <!-- Stripe Card Number Element wird hier eingefügt -->
                                </div>
                                <div v-if="errorMessages.cardNumber" class="text-red-500 text-sm mt-1">{{ errorMessages.cardNumber }}</div>
                            </div>
                            
                            <!-- Ablaufdatum und Prüfziffer in einer Zeile -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Ablaufdatum -->
                                <div>
                                    <label for="card-expiry-element" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Ablaufdatum
                                    </label>
                                    <div 
                                        id="card-expiry-element" 
                                        class="p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 min-h-[40px]"
                                    >
                                        <!-- Stripe Card Expiry Element wird hier eingefügt -->
                                    </div>
                                    <div v-if="errorMessages.cardExpiry" class="text-red-500 text-sm mt-1">{{ errorMessages.cardExpiry }}</div>
                                </div>
                                
                                <!-- Prüfziffer -->
                                <div>
                                    <label for="card-cvc-element" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Prüfziffer (CVC)
                                    </label>
                                    <div 
                                        id="card-cvc-element" 
                                        class="p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 min-h-[40px]"
                                    >
                                        <!-- Stripe Card CVC Element wird hier eingefügt -->
                                    </div>
                                    <div v-if="errorMessages.cardCvc" class="text-red-500 text-sm mt-1">{{ errorMessages.cardCvc }}</div>
                                </div>
                            </div>
                            
                            <!-- Allgemeine Fehlermeldungen -->
                            <div v-if="errorMessages.general" class="text-red-500 text-sm mt-1">{{ errorMessages.general }}</div>
                            
                            <!-- Lade-Indikator -->
                            <div v-if="(!isCardMounted && isStripeLoaded) || isLoadingStripe" class="p-2 text-center text-sm text-gray-500">
                                Kartenformular wird geladen...
                            </div>
                            
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                Ihre Zahlung wird sicher über Stripe verarbeitet.
                            </div>
                            
                            <div class="flex justify-end space-x-3">
                                <button type="button" class="btn btn-outline" @click="selectedPlan = null" :disabled="processing">
                                    Abbrechen
                                </button>
                                <button type="submit" class="btn btn-primary" :disabled="processing || !isCardMounted">
                                    <span v-if="processing" class="flex items-center">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Verarbeitung...
                                    </span>
                                    <span v-else>Jetzt abonnieren</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template> 