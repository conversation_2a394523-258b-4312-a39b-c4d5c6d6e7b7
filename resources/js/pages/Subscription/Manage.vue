<script setup>
import { ref, onMounted, nextTick, computed } from 'vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';

const props = defineProps({
    subscription: Object,
    invoices: Array,
    paymentMethod: Object,
    trialEndsAt: String,
    expiresAt: String,
    daysRemaining: Number,
    isInTrialPeriod: Boolean,
    restrictedMode: Boolean,
    consecutiveSubscriptions: Number,
    purchasedSubscriptions: Number,
});

const page = usePage();
const showUpdatePaymentForm = ref(false);
const showCancelModal = ref(false);
const showResumeModal = ref(false);
const stripe = ref(null);
const elements = ref(null);
const cardElement = ref(null);
const cardError = ref('');
const processing = ref(false);

const updatePaymentForm = useForm({
    payment_method: '',
});

const cancelForm = useForm({});

const resumeForm = useForm({});

// Plan-Mapping für menschenlesbare Namen
const planMapping = {
    'price_1RAwm7FbLhupDbC8WBFS4MMI': 'Basis',
    'price_1RAwNVFbLhupDbC8JG6MNRw9': 'Premium',
    'price_1RAwORFbLhupDbC8ZuzL1Lcj': 'Professional',
    'price_1RAwLkFbLhupDbO8TEnFHStA': 'Basis',
    'price_1RAwLkFbLhupDbC8TEnFHStA': 'Basis', // Korrekte ID aus dem Screenshot
};

// Berechne den Plannamen basierend auf der Stripe-Preis-ID
const planName = computed(() => {
    if (!props.subscription || !props.subscription.stripe_price) {
        console.log('Keine Subscription oder stripe_price gefunden');
        return 'Unbekannt';
    }
    
    console.log('Aktuelle Stripe-Preis-ID:', props.subscription.stripe_price);
    console.log('Gefundener Plan:', planMapping[props.subscription.stripe_price] || 'Nicht gefunden');
    
    return planMapping[props.subscription.stripe_price] || props.subscription.stripe_price;
});

// Fallback Berechnungen falls die Props nicht vom Controller kommen
const computedExpiresAt = computed(() => {
    if (props.expiresAt) return props.expiresAt;
    
    if (!props.subscription) return null;
    
    if (props.subscription.ends_at) {
        return props.subscription.ends_at;
    } else if (props.subscription.current_period_end) {
        return props.subscription.current_period_end;
    }
    
    return null;
});

const computedDaysRemaining = computed(() => {
    if (props.daysRemaining !== undefined) return props.daysRemaining;
    
    if (!computedExpiresAt.value) return null;
    
    const now = new Date();
    const end = new Date(computedExpiresAt.value);
    
    // Setze die Uhrzeiten auf Mitternacht, um nur ganze Tage zu zählen
    now.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);
    
    // Berechne die Differenz in Millisekunden und dann in Tagen
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    console.log('Datum jetzt:', now);
    console.log('Enddatum:', end);
    console.log('Berechnete Tage:', diffDays);
    
    return diffDays;
});

const computedIsInTrialPeriod = computed(() => {
    if (props.isInTrialPeriod !== undefined) return props.isInTrialPeriod;
    
    if (!props.trialEndsAt) return false;
    
    const now = new Date();
    const trialEnd = new Date(props.trialEndsAt);
    return now < trialEnd;
});

const computedConsecutiveSubscriptions = computed(() => {
    return props.consecutiveSubscriptions || 0;
});

const computedRestrictedMode = computed(() => {
    return props.restrictedMode || false;
});

onMounted(() => {
    console.log('Komponentenprops:', props);
    console.log('Anzahl gekaufter Abonnements:', props.purchasedSubscriptions);
    console.log('Berechnete verbleibende Tage:', props.daysRemaining);
    
    if (showUpdatePaymentForm.value) {
        initializeStripe();
    }
});

const initializeStripe = () => {
    if (window.Stripe) {
        stripe.value = window.Stripe(import.meta.env.VITE_STRIPE_KEY);
        elements.value = stripe.value.elements();
        cardElement.value = elements.value.create('card');
        
        // Mount the card element
        nextTick(() => {
            if (document.getElementById('card-element')) {
                cardElement.value.mount('#card-element');
                
                // Listen for errors
                cardElement.value.on('change', (event) => {
                    cardError.value = event.error ? event.error.message : '';
                });
            }
        });
    }
};

const toggleUpdatePaymentForm = () => {
    showUpdatePaymentForm.value = !showUpdatePaymentForm.value;
    
    if (showUpdatePaymentForm.value) {
        nextTick(() => {
            initializeStripe();
        });
    }
};

const updatePaymentMethod = async () => {
    if (!stripe.value || !elements.value) {
        return;
    }
    
    processing.value = true;
    
    try {
        const { paymentMethod, error } = await stripe.value.createPaymentMethod({
            type: 'card',
            card: cardElement.value,
        });
        
        if (error) {
            cardError.value = error.message;
            processing.value = false;
            return;
        }
        
        updatePaymentForm.payment_method = paymentMethod.id;
        
        // Submit form to server
        updatePaymentForm.post(route('subscription.payment-method.update'), {
            preserveScroll: true,
            onSuccess: () => {
                showUpdatePaymentForm.value = false;
                processing.value = false;
            },
            onError: () => {
                processing.value = false;
            },
        });
    } catch (e) {
        cardError.value = 'An unexpected error occurred.';
        processing.value = false;
    }
};

const cancelSubscription = () => {
    cancelForm.post(route('subscription.cancel'), {
        preserveScroll: true,
        onSuccess: () => {
            showCancelModal.value = false;
        },
    });
};

const resumeSubscription = () => {
    resumeForm.post(route('subscription.resume'), {
        preserveScroll: true,
        onSuccess: () => {
            showResumeModal.value = false;
        },
    });
};

const formatCardNumber = (last4) => {
    return `•••• •••• •••• ${last4}`;
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    
    try {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        };
        return new Date(dateString).toLocaleDateString('de-DE', options);
    } catch (e) {
        console.error('Fehler beim Formatieren des Datums:', e);
        return dateString;
    }
};

const formatPrice = (price) => {
    return new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: 'EUR',
    }).format(price / 100);
};
</script>

<template>
    <AppLayout title="Abonnement verwalten">
        <Head title="Abonnement verwalten" />

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                <!-- Alerts -->
                <div v-if="page.props.flash && page.props.flash.success" class="alert alert-success shadow-lg mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 stroke-current" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{{ page.props.flash.success }}</span>
                </div>
                
                <div v-if="page.props.flash && page.props.flash.error" class="alert alert-error shadow-lg mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 stroke-current" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{{ page.props.flash.error }}</span>
                </div>

                <!-- Subscription Status -->
                <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Abonnementstatus</h2>
                    
                    <div class="mt-4 space-y-4">
                        <!-- Anzeige für Benutzer mit aktivem Abonnement -->
                        <template v-if="subscription">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Plan:</span>
                                <span class="badge badge-primary px-4 py-3 text-sm">{{ planName }}</span>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Status:</span>
                                <span 
                                    :class="[
                                        'badge px-4 py-3 text-sm',
                                        subscription.ended_at ? 'badge-error' : 
                                        subscription.ends_at ? 'badge-warning' : 'badge-success'
                                    ]"
                                >
                                    {{ 
                                        subscription.ended_at ? 'Beendet' : 
                                        subscription.ends_at ? 'Wird gekündigt' : 
                                        'Aktiv' 
                                    }}
                                </span>
                            </div>
                            
                            <!-- Anzahl gekaufter Abonnements anzeigen -->
                            <div v-if="purchasedSubscriptions > 1" class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Gekaufte Abonnements:</span>
                                <span class="badge badge-accent px-4 py-3 text-sm">{{ purchasedSubscriptions }}</span>
                            </div>
                        </template>
                        
                        <!-- Anzeige für Benutzer ohne Abonnement und ohne Testphase -->
                        <template v-else>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Status:</span>
                                <span v-if="computedIsInTrialPeriod" class="badge badge-success px-4 py-3 text-sm">Kostenlose Testversion</span>
                                <span v-else class="badge badge-error px-4 py-3 text-sm">Kein aktives Abonnement</span>
                            </div>
                            
                            <div v-if="!computedIsInTrialPeriod" class="mt-4">
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    Sie haben derzeit kein aktives Abonnement. Wählen Sie einen Plan, um alle Funktionen zu nutzen.
                                </p>
                                <div class="mt-4">
                                    <Button asChild variant="default" class="text-white">
                                        <Link :href="route('subscription.index')">
                                            Abonnement auswählen
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        </template>
                        
                        <!-- Ablaufdatum und verbleibende Tage - für alle Benutzer mit Ablaufdatum -->
                        <div v-if="computedExpiresAt" class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Gültig bis:</span>
                            <span>{{ formatDate(computedExpiresAt) }}</span>
                        </div>
                        
                        <div v-if="computedDaysRemaining !== null" class="flex items-center justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Verbleibende Tage:</span>
                            <span 
                                :class="{
                                    'text-red-500': computedDaysRemaining < 5,
                                    'text-yellow-500': computedDaysRemaining >= 5 && computedDaysRemaining < 15,
                                    'text-green-500': computedDaysRemaining >= 15
                                }"
                            >
                                {{ computedDaysRemaining }} Tage
                            </span>
                        </div>
                        
                        <!-- Kostenlose Testphase - für alle Benutzer in Testphase -->
                        <div v-if="computedIsInTrialPeriod" class="mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-md text-blue-800 dark:text-blue-200">
                            <div class="font-semibold mb-1">Kostenlose Testphase</div>
                            <p class="text-sm">
                                Sie befinden sich in der kostenlosen 6-monatigen Testphase. 
                                Diese endet am {{ formatDate(trialEndsAt) }}.
                            </p>
                            <p class="text-sm mt-2">
                                <strong>Wichtig:</strong> Nach Ablauf der kostenlosen Testphase können Sie weiterhin Ihre gespeicherten Daten einsehen, 
                                aber alle Funktionen sind eingeschränkt. Wenn Sie innerhalb eines Jahres nach Ablauf der Testphase kein Abonnement abschließen, 
                                wird Ihr Konto automatisch gelöscht.
                            </p>
                        </div>
                        
                        <!-- Eingeschränkter Modus warnung -->
                        <div v-if="computedRestrictedMode" class="mt-4 p-3 bg-red-50 dark:bg-red-900 rounded-md text-red-800 dark:text-red-200">
                            <div class="font-semibold mb-1">Eingeschränkter Modus</div>
                            <p class="text-sm">
                                Ihr Abonnement ist abgelaufen oder Ihre kostenlose Testphase ist beendet. 
                                Sie können weiterhin auf Ihre Daten zugreifen, aber einige Funktionen sind eingeschränkt.
                                Bitte erneuern Sie Ihr Abonnement, um wieder vollen Zugriff zu erhalten.
                            </p>
                        </div>
                        
                        <!-- Aufeinanderfolgende Abonnements -->
                        <div v-if="computedConsecutiveSubscriptions > 0" class="mt-4 p-3 bg-green-50 dark:bg-green-900 rounded-md text-green-800 dark:text-green-200">
                            <div class="font-semibold mb-1">Treuerabatt</div>
                            <p class="text-sm">
                                Sie haben bereits {{ computedConsecutiveSubscriptions }} aufeinanderfolgende Basis-Abonnements abgeschlossen. 
                                Bei 2 aufeinanderfolgenden Abonnements erhalten Sie einen Monat kostenlose Nutzung!
                            </p>
                        </div>
                        
                        <!-- Aktionsbuttons für Abonnenten oder Testphasen-Nutzer -->
                        <div v-if="subscription || computedIsInTrialPeriod" class="flex justify-end space-x-3 mt-6">
                            <Button
                                v-if="!subscription || (subscription && (subscription.ended_at || !subscription.stripe_status))"
                                asChild
                                variant="default"
                                class="text-white"
                            >
                                <Link :href="route('subscription.index')">
                                    {{ subscription ? 'Erneut abonnieren' : 'Jetzt abonnieren' }}
                                </Link>
                            </Button>
                            
                            <Button
                                v-else-if="subscription && subscription.ends_at"
                                @click="showResumeModal = true"
                                variant="default"
                            >
                                Abonnement fortsetzen
                            </Button>
                            
                            <Button
                                v-else-if="subscription"
                                @click="showCancelModal = true"
                                variant="destructive"
                            >
                                Abonnement kündigen
                            </Button>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Method -->
                <div v-if="paymentMethod" class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
                    <div class="flex justify-between">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Zahlungsmethode</h2>
                        <button 
                            @click="toggleUpdatePaymentForm"
                            class="text-sm text-primary hover:underline"
                        >
                            {{ showUpdatePaymentForm ? 'Abbrechen' : 'Aktualisieren' }}
                        </button>
                    </div>
                    
                    <div v-if="!showUpdatePaymentForm" class="mt-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="rounded-full p-3 bg-gray-100 dark:bg-gray-700">
                                    <!-- Card Icons -->
                                    <svg 
                                        v-if="paymentMethod.card && paymentMethod.card.brand === 'visa'" 
                                        class="h-6 w-6" 
                                        fill="currentColor" 
                                        viewBox="0 0 32 32"
                                    >
                                        <path d="M32 12.007v-0.012c0-1.421-1.115-2.536-2.536-2.536h-26.803c-1.421 0-2.661 1.115-2.661 2.536v7.621c0 1.421 1.24 2.411 2.661 2.411h26.803c1.421 0 2.536-0.989 2.536-2.411zM30.48 19.615c0 0.552-0.5 1.068-1.052 1.068h-26.729c-0.552 0-1.177-0.516-1.177-1.068v-7.621c0-0.552 0.625-1.068 1.177-1.068h26.729c0.552 0 1.052 0.516 1.052 1.068z"></path>
                                        <path d="M10.844 16.536l1.333-3.255h2.068l-2.365 5.599h-2.068l-2.365-5.599h2.068zM15.333 18.881v-5.599h1.953v5.599zM20.833 18.943c-0.401 0-0.766-0.068-1.083-0.199-0.318-0.136-0.552-0.318-0.708-0.536l0.927-0.964c0.089 0.089 0.199 0.161 0.339 0.214 0.136 0.057 0.282 0.089 0.427 0.089 0.172 0 0.292-0.021 0.354-0.068 0.068-0.047 0.099-0.104 0.099-0.188 0-0.078-0.047-0.141-0.146-0.188-0.099-0.047-0.271-0.099-0.516-0.161-0.198-0.047-0.38-0.109-0.542-0.178-0.167-0.068-0.318-0.167-0.448-0.292-0.136-0.125-0.24-0.276-0.318-0.453-0.079-0.183-0.119-0.396-0.119-0.641 0-0.245 0.047-0.464 0.136-0.656 0.089-0.193 0.219-0.354 0.385-0.485 0.161-0.125 0.354-0.224 0.573-0.292 0.219-0.062 0.459-0.094 0.724-0.094 0.354 0 0.683 0.068 0.99 0.198 0.307 0.136 0.537 0.313 0.698 0.537l-0.924 0.911c-0.057-0.078-0.146-0.146-0.25-0.203-0.11-0.057-0.234-0.089-0.364-0.089-0.141 0-0.245 0.021-0.307 0.068-0.062 0.042-0.094 0.094-0.094 0.167 0 0.068 0.047 0.125 0.136 0.167 0.089 0.042 0.25 0.094 0.485 0.145 0.213 0.057 0.406 0.12 0.578 0.193 0.172 0.073 0.323 0.167 0.453 0.281 0.125 0.115 0.224 0.26 0.292 0.443 0.068 0.178 0.104 0.396 0.104 0.656 0 0.254-0.047 0.48-0.141 0.688-0.089 0.198-0.224 0.37-0.391 0.511-0.172 0.136-0.375 0.245-0.609 0.318-0.238 0.078-0.501 0.115-0.786 0.115zM25.359 16.536l1.333-3.255h2.073l-2.37 5.599h-2.063l-2.37-5.599h2.068z"></path>
                                    </svg>

                                    <svg 
                                        v-else-if="paymentMethod.card && paymentMethod.card.brand === 'mastercard'" 
                                        class="h-6 w-6" 
                                        fill="currentColor" 
                                        viewBox="0 0 32 32"
                                    >
                                        <path d="M16 4.828c-0.734 0.833-1.406 1.739-2 2.703v8.937c0.927 1.525 2.063 2.828 3.333 3.953 3.661-2.839 6-7.297 6-12.328 0-1.266-0.161-2.495-0.453-3.667-1.281-0.073-2.589-0.073-3.88 0-1.214 0.073-2.24 0.229-3 0.401zM10.667 17.161v-1.198c0-0.401 0.333-0.734 0.734-0.734 0.401 0 0.734 0.333 0.734 0.734v1.198c0 0.401-0.333 0.734-0.734 0.734-0.401 0-0.734-0.333-0.734-0.734zM14.891 17.161c0-0.401 0.333-0.734 0.734-0.734 0.401 0 0.734 0.333 0.734 0.734v1.198c0 0.401-0.333 0.734-0.734 0.734-0.401 0-0.734-0.333-0.734-0.734z"></path>
                                        <path d="M14 7.531c3.927-3.083 9-3.563 13.464-1.615 0.021 0.062 0.073 0.089 0.099 0.151-2.068-5.12-7-8.828-12.901-9.031-6.797-0.219-12.625 4.698-14 11.172-0.172 0.797-0.266 1.625-0.297 2.469 1.016-1.615 2.162-3.057 3.401-4.349 2.583-2.693 6.099-4.422 10.099-4.537 0.047 0.026 0.099 0.047 0.135 0.099zM29.333 16c0 1.911-0.391 3.74-1.099 5.401-1.979 4.594-6.349 7.875-11.495 8.432-0.245 0.026-0.505 0.047-0.74 0.073-6.979 0.526-13.193-4.255-14.865-10.656-0.323-1.229-0.5-2.505-0.5-3.828 0-8.932 7.24-16.172 16.172-16.172 1.323 0 2.599 0.177 3.828 0.5 6.401 1.677 11.178 7.885 10.656 14.865-0.026 0.245-0.047 0.474-0.073 0.74-0.245 0.214-0.599 0.354-0.989 0.354-0.802 0-1.453-0.651-1.453-1.453 0-0.682 0.464-1.24 1.094-1.401 0.073-0.214 0.13-0.443 0.13-0.682 0-0.599-0.245-1.125-0.641-1.521s-0.922-0.641-1.521-0.641c-1.188 0-2.161 0.974-2.161 2.161 0 1.188 0.974 2.161 2.161 2.161 0.151 0 0.297-0.021 0.443-0.047 0.318 0.37 0.786 0.599 1.318 0.599z"></path>
                                    </svg>

                                    <svg 
                                        v-else 
                                        xmlns="http://www.w3.org/2000/svg" 
                                        class="h-6 w-6" 
                                        fill="none" 
                                        viewBox="0 0 24 24" 
                                        stroke="currentColor"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800 dark:text-gray-200">
                                    {{ paymentMethod.card ? formatCardNumber(paymentMethod.card.last4) : 'Unbekannte Zahlungsmethode' }}
                                </p>
                                <p v-if="paymentMethod.card" class="text-sm text-gray-600 dark:text-gray-400">
                                    Gültig bis {{ paymentMethod.card.exp_month }}/{{ paymentMethod.card.exp_year }}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Update Payment Form -->
                    <div v-else class="mt-4">
                        <form @submit.prevent="updatePaymentMethod" class="space-y-4">
                            <div>
                                <label for="card-element" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Neue Zahlungsmethode
                                </label>
                                <div 
                                    id="card-element" 
                                    class="p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    <!-- Stripe Elements will be inserted here -->
                                </div>
                                <div v-if="cardError" class="text-red-500 text-sm mt-1">{{ cardError }}</div>
                            </div>
                            
                            <div class="flex justify-end space-x-3">
                                <Button @click="toggleUpdatePaymentForm" :disabled="processing" variant="outline">
                                    Abbrechen
                                </Button>
                                <Button type="submit" :disabled="processing || !stripe">
                                    <span v-if="processing" class="flex items-center">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Verarbeitung...
                                    </span>
                                    <span v-else>Zahlungsmethode aktualisieren</span>
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Invoices -->
                <div v-if="invoices && invoices.length > 0" class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Rechnungsverlauf</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th class="text-left">Datum</th>
                                    <th class="text-left">Betrag</th>
                                    <th class="text-left">Status</th>
                                    <th class="text-right">Aktionen</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="invoice in invoices" :key="invoice.id" class="border-t">
                                    <td class="py-3">{{ formatDate(invoice.date) }}</td>
                                    <td class="py-3">{{ formatPrice(invoice.amount_due) }}</td>
                                    <td class="py-3">
                                        <span 
                                            :class="[
                                                'px-2 py-1 rounded-full text-xs font-medium',
                                                invoice.paid 
                                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                                                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                            ]"
                                        >
                                            {{ invoice.paid ? 'Bezahlt' : 'Unbezahlt' }}
                                        </span>
                                    </td>
                                    <td class="py-3 text-right">
                                        <a 
                                            v-if="invoice.invoice_pdf"
                                            :href="invoice.invoice_pdf" 
                                            target="_blank"
                                            class="text-primary hover:underline text-sm mr-2"
                                        >
                                            Stripe PDF
                                        </a>
                                        <a 
                                            :href="route('subscription.invoice.download', invoice.id)" 
                                            class="text-primary hover:underline text-sm"
                                        >
                                            Herunterladen
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cancel Confirmation Modal -->
        <div v-if="showCancelModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600 dark:text-red-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                    Abonnement kündigen
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Sind Sie sicher, dass Sie Ihr Abonnement kündigen möchten? Alle Ihre Premium-Funktionen werden sofort deaktiviert. Diese Aktion kann nicht rückgängig gemacht werden.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <Button 
                            type="button"
                            variant="destructive"
                            @click="cancelSubscription"
                        >
                            Abonnement kündigen
                        </Button>
                        <Button 
                            type="button"
                            variant="outline"
                            @click="showCancelModal = false"
                            class="mr-3"
                        >
                            Abonnement behalten
                        </Button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Resume Confirmation Modal -->
        <div v-if="showResumeModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900 sm:mx-0 sm:h-10 sm:w-10">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 dark:text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                    Abonnement fortsetzen
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Sind Sie sicher, dass Sie Ihr Abonnement fortsetzen möchten? Ihre Premium-Funktionen werden sofort wiederhergestellt.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <Button 
                            type="button"
                            @click="resumeSubscription"
                        >
                            Abonnement fortsetzen
                        </Button>
                        <Button 
                            type="button"
                            variant="outline"
                            @click="showResumeModal = false"
                            class="mr-3"
                        >
                            Abbrechen
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template> 