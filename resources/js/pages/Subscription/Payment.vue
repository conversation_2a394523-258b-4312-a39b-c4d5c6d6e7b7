<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { Head, useForm, usePage } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';

const props = defineProps({
    plan: Object,
    setup_intent: String
});

const page = usePage();
const stripe = ref(null);
const elements = ref(null);
const cardElement = ref(null);
const processing = ref(false);
const cardError = ref('');

const checkoutForm = useForm({
    payment_method: '',
    plan: props.plan.id,
    coupon: ''
});

onMounted(() => {
    if (window.Stripe) {
        stripe.value = window.Stripe(import.meta.env.VITE_STRIPE_KEY);
        elements.value = stripe.value.elements();
        cardElement.value = elements.value.create('card');
        
        nextTick(() => {
            if (document.getElementById('card-element')) {
                cardElement.value.mount('#card-element');
                
                // Listen for errors
                cardElement.value.on('change', (event) => {
                    cardError.value = event.error ? event.error.message : '';
                });
            }
        });
    }
});

const handleSubscription = async () => {
    if (!stripe.value || !elements.value) {
        return;
    }
    
    processing.value = true;
    
    try {
        const { paymentMethod, error } = await stripe.value.createPaymentMethod({
            type: 'card',
            card: cardElement.value,
        });
        
        if (error) {
            cardError.value = error.message;
            processing.value = false;
            return;
        }
        
        checkoutForm.payment_method = paymentMethod.id;
        
        // Submit form to server
        checkoutForm.post(route('subscription.checkout'), {
            onSuccess: (response) => {
                if (response?.props?.flash?.requires_action) {
                    handleStripePayment(response.props.flash.payment_intent_client_secret);
                } else {
                    window.location.href = route('subscription.success');
                }
            },
            onError: () => {
                processing.value = false;
            },
        });
    } catch (e) {
        console.error(e);
        cardError.value = 'An unexpected error occurred.';
        processing.value = false;
    }
};

const handleStripePayment = async (clientSecret) => {
    const { error } = await stripe.value.confirmCardPayment(clientSecret);
    
    if (error) {
        cardError.value = error.message;
        processing.value = false;
    } else {
        window.location.href = route('subscription.success');
    }
};

const formatPrice = (price) => {
    return new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: 'EUR',
    }).format(price / 100);
};
</script>

<template>
    <AppLayout :title="`Abonnieren von ${plan.name}`">
        <Head :title="`Abonnieren von ${plan.name}`" />

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <!-- Plan Summary -->
                    <div class="mb-8">
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                            Abonnement abschließen
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400">
                            Sie abonnieren den {{ plan.name }} Plan.
                        </p>
                        
                        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        {{ plan.name }}
                                    </h3>
                                    <p class="text-gray-600 dark:text-gray-400">
                                        {{ plan.interval === 'month' ? 'Monatliches' : 'Jährliches' }} Abonnement
                                    </p>
                                </div>
                                <div class="text-xl font-bold text-gray-900 dark:text-white">
                                    {{ formatPrice(plan.price) }}
                                    <span class="text-sm font-normal text-gray-600 dark:text-gray-400">
                                        / {{ plan.interval === 'month' ? 'Monat' : 'Jahr' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Checkout Form -->
                    <form @submit.prevent="handleSubscription" class="space-y-6">
                        <!-- Coupon Field (Optional) -->
                        <div>
                            <label for="coupon" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Rabattcode (Optional)
                            </label>
                            <input
                                id="coupon"
                                v-model="checkoutForm.coupon"
                                type="text"
                                class="input input-bordered w-full max-w-xs"
                                placeholder="Code eingeben"
                            />
                        </div>
                        
                        <!-- Payment Details -->
                        <div>
                            <label for="card-element" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Kartendetails
                            </label>
                            <div 
                                id="card-element" 
                                class="p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                            >
                                <!-- Stripe Elements will be inserted here -->
                            </div>
                            <div v-if="cardError" class="text-red-500 text-sm mt-1">{{ cardError }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                Ihre Zahlung wird sicher über Stripe verarbeitet.
                            </div>
                        </div>
                        
                        <!-- Flash Messages -->
                        <div v-if="page.props.flash.error" class="bg-red-100 dark:bg-red-900 p-4 rounded-md">
                            <p class="text-sm text-red-700 dark:text-red-300">{{ page.props.flash.error }}</p>
                        </div>
                        
                        <!-- Form Actions -->
                        <div class="flex justify-end space-x-3">
                            <Button as="a" :href="route('subscription.index')" variant="outline">
                                Abbrechen
                            </Button>
                            <Button type="submit" :disabled="processing || !stripe">
                                <span v-if="processing" class="flex items-center">
                                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Verarbeitung...
                                </span>
                                <span v-else>Jetzt abonnieren ({{ formatPrice(plan.price) }}/{{ plan.interval === 'month' ? 'Monat' : 'Jahr' }})</span>
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template> 