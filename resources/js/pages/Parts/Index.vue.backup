<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import axios from 'axios';
import { useToast } from '@/composables/useToast';
import PartFilterBar from './components/PartFilterBar.vue';
import PriceComparisonModal from './components/PriceComparisonModal.vue';
import HsnTsnSearchModal from './components/HsnTsnSearchModal.vue';
import EmptyState from '@/components/EmptyState.vue';
import Pagination from '@/components/Pagination.vue';
import PartsList from './components/PartsList.vue';

defineOptions({
  layout: AppSidebarLayout,
});

interface Part {
  id: number;
  name: string;
  part_number: string | null;
  manufacturer: string | null;
  category: string | null;
  description: string | null;
  cost: number | null;
  quantity: number;
  created_at: string;
  updated_at: string;
  [key: string]: string | number | null;
}

interface PriceSearchResult {
  provider: string;
  price: number;
  title: string;
  url: string;
  availability: string;
  shipping_cost: number;
  rating: number;
  delivery_time: string;
  image_url?: string;  // Added field for product images
}

// Add interface for HSN/TSN search results
interface VehicleInfo {
  hsn: string;
  tsn: string;
  make: string;
  model: string;
  type: string;
  year_from: number | null;
  year_to: number | null;
  engine: string;
  engine_code: string;
}

interface CompatiblePart {
  name: string;
  manufacturer: string;
  part_number: string;
  price: number;
  url: string;
  image_url?: string;  // Added field for product images
}

interface CompatibleCategory {
  category: string;
  parts: CompatiblePart[];
}

interface HsnTsnSearchResult {
  success: boolean;
  vehicle_info: VehicleInfo;
  compatible_parts: CompatibleCategory[];
  error?: string;
}

const props = defineProps<{
  parts: Array<Part>;
  categories: Array<string>;
}>();

const { showToast } = useToast();

// Filter state
const filterState = reactive({
  searchQuery: '',
  selectedCategory: '',
  sortField: 'name',
  sortDirection: 'asc',
  currentPage: 1,
  itemsPerPage: 10
});

// Modal state
const modalState = reactive({
  priceModal: {
    isOpen: false,
    isLoading: false,
    selectedPart: null as Part | null,
    results: [] as PriceSearchResult[],
    error: ''
  },
  hsnTsnModal: {
    isOpen: false,
    isLoading: false,
    hsn: '',
    tsn: '',
    results: null as HsnTsnSearchResult | null,
    error: ''
  }
});

// Computed properties
const filteredParts = computed(() => {
  let result = [...props.parts];

  // Suche
  if (filterState.searchQuery) {
    const query = filterState.searchQuery.toLowerCase();
    result = result.filter(part =>
      part.name.toLowerCase().includes(query) ||
      (part.part_number && part.part_number.toLowerCase().includes(query)) ||
      (part.manufacturer && part.manufacturer.toLowerCase().includes(query)) ||
      (part.description && part.description.toLowerCase().includes(query))
    );
  }

  // Kategoriefilter
  if (filterState.selectedCategory) {
    result = result.filter(part => part.category === filterState.selectedCategory);
  }

  // Sortierung
  result.sort((a, b) => {
    const aValue = a[filterState.sortField];
    const bValue = b[filterState.sortField];

    // Handling für null-Werte
    if (aValue === null && bValue === null) return 0;
    if (aValue === null) return 1;
    if (bValue === null) return -1;

    // String-Vergleich
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return filterState.sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // Numerischer Vergleich
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return filterState.sortDirection === 'asc'
        ? aValue - bValue
        : bValue - aValue;
    }

    return 0;
  });

  return result;
});

// Paginierte Teile
const paginatedParts = computed(() => {
  const start = (filterState.currentPage - 1) * filterState.itemsPerPage;
  const end = start + filterState.itemsPerPage;
  return filteredParts.value.slice(start, end);
});

// Gesamtzahl der Seiten
const totalPages = computed(() => {
  return Math.ceil(filteredParts.value.length / filterState.itemsPerPage);
});

// Format utilities
const formatCurrency = (price: number | null) => {
  if (price === null) return '-';

  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(price);
};

// Event handlers
const changePage = (page: number) => {
  filterState.currentPage = page;
};

const changeSort = (field: string) => {
  if (filterState.sortField === field) {
    // Richtung umkehren, wenn das gleiche Feld ausgewählt wird
    filterState.sortDirection = filterState.sortDirection === 'asc' ? 'desc' : 'asc';
  } else {
    // Neues Sortierfeld mit aufsteigender Richtung
    filterState.sortField = field;
    filterState.sortDirection = 'asc';
  }
};

const deletePart = (partId: number) => {
  if (confirm('Sind Sie sicher, dass Sie dieses Teil löschen möchten?')) {
    router.delete(route('parts.destroy', partId), {
      onSuccess: () => {
        showToast({
          message: 'Teil erfolgreich gelöscht',
          type: 'success'
        });
      }
    });
  }
};

// Price comparison handlers
const openPriceSearch = (part: Part) => {
  modalState.priceModal.selectedPart = part;
  modalState.priceModal.results = [];
  modalState.priceModal.error = '';
  modalState.priceModal.isOpen = true;
};

const searchBestPrice = async () => {
  if (!modalState.priceModal.selectedPart || !modalState.priceModal.selectedPart.name) {
    modalState.priceModal.error = 'Teil fehlt oder hat keinen Namen';
    return;
  }

  modalState.priceModal.isLoading = true;
  modalState.priceModal.error = '';

  try {
    const response = await axios.post(route('parts.find-best-price'), {
      name: modalState.priceModal.selectedPart.name,
      part_number: modalState.priceModal.selectedPart.part_number || modalState.priceModal.selectedPart.name,
      manufacturer: modalState.priceModal.selectedPart.manufacturer || ''
    });

    if (response.data.success) {
      modalState.priceModal.results = response.data.results;
      
      if (modalState.priceModal.results.length === 0) {
        modalState.priceModal.error = 'Keine Preisdaten gefunden';
      }
    } else {
      modalState.priceModal.error = response.data.error || 'Fehler beim Abrufen der Preise';
    }
  } catch (error) {
    modalState.priceModal.error = 'Ein Fehler ist aufgetreten beim Laden der Preisdaten';
    console.error('Preissuchfehler:', error);
  } finally {
    modalState.priceModal.isLoading = false;
  }
};

const closePriceModal = () => {
  modalState.priceModal.isOpen = false;
  modalState.priceModal.selectedPart = null;
};

// HSN/TSN handlers
const openHsnTsnSearch = () => {
  modalState.hsnTsnModal.results = null;
  modalState.hsnTsnModal.error = '';
  modalState.hsnTsnModal.isOpen = true;
};

const searchByHsnTsn = async () => {
  // Validiere HSN (4 Ziffern) und TSN (3 Zeichen - Buchstaben oder Ziffern)
  if (!modalState.hsnTsnModal.hsn || modalState.hsnTsnModal.hsn.length !== 4 || !/^\d{4}$/.test(modalState.hsnTsnModal.hsn)) {
    modalState.hsnTsnModal.error = 'HSN muss aus 4 Ziffern bestehen';
    return;
  }

  if (!modalState.hsnTsnModal.tsn || modalState.hsnTsnModal.tsn.length !== 3) {
    modalState.hsnTsnModal.error = 'TSN muss aus genau 3 Zeichen bestehen';
    return;
  }

  modalState.hsnTsnModal.isLoading = true;
  modalState.hsnTsnModal.error = '';

  try {
    const response = await axios.post(route('parts.find-by-hsn-tsn'), {
      hsn: modalState.hsnTsnModal.hsn,
      tsn: modalState.hsnTsnModal.tsn
    });

    if (response.data.success) {
      modalState.hsnTsnModal.results = response.data;
    } else {
      modalState.hsnTsnModal.error = response.data.error || 'Fehler bei der Suche nach Teilen';
    }
  } catch (error) {
    modalState.hsnTsnModal.error = 'Ein Fehler ist aufgetreten bei der Suche';
    console.error('HSN/TSN-Suchfehler:', error);
  } finally {
    modalState.hsnTsnModal.isLoading = false;
  }
};

const closeHsnTsnModal = () => {
  modalState.hsnTsnModal.isOpen = false;
};

// Calculate total price (product + shipping)
const calculateTotalPrice = (price: number, shippingCost: number) => {
  return formatCurrency(price + shippingCost);
};

// Page init
onMounted(() => {
  document.title = 'Ersatzteile | Fahrzeugakte';
});
</script>

<template>
  <div class="px-6 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold tracking-tight flex items-center">
          Ersatzteile
          <span class="ml-2 text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full font-medium">BETA</span>
        </h1>
        <p class="text-muted-foreground mt-1">Verwalten Sie Ihre Ersatzteile und finden Sie die besten Preise</p>
      </div>
      <div class="flex space-x-2">
        <Link :href="route('parts.create')" class="btn btn-primary">
          Neues Teil hinzufügen
        </Link>
      </div>
    </div>

    <!-- Filter Bar -->
    <PartFilterBar
      :categories="categories"
      v-model:searchQuery="filterState.searchQuery"
      v-model:selectedCategory="filterState.selectedCategory"
      @openHsnTsnSearch="openHsnTsnSearch"
    />

    <!-- Parts List -->
    <PartsList
      v-if="filteredParts.length > 0"
      :parts="paginatedParts"
      :sortField="filterState.sortField"
      :sortDirection="filterState.sortDirection"
      @changeSort="changeSort"
      @openPriceSearch="openPriceSearch"
      @deletePart="deletePart"
    />
    <div v-else>
      <EmptyState
        title="Keine Ersatzteile gefunden"
        description="Es wurden keine Ersatzteile gefunden, die den Suchkriterien entsprechen. Passen Sie Ihre Filterkriterien an oder fügen Sie neue Teile hinzu."
        icon="search"
        :actions="{
          primary: {
            label: 'Neues Teil hinzufügen',
            to: route('parts.create')
          }
        }"
      />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="totalPages > 1"
      :currentPage="filterState.currentPage"
      :totalPages="totalPages"
      :totalItems="filteredParts.length"
      :from="(filterState.currentPage - 1) * filterState.itemsPerPage + 1"
      :to="Math.min(filterState.currentPage * filterState.itemsPerPage, filteredParts.length)"
      @change="changePage"
    />

    <!-- Modals -->
    <PriceComparisonModal
      :isOpen="modalState.priceModal.isOpen"
      :isLoading="modalState.priceModal.isLoading"
      :selectedPart="modalState.priceModal.selectedPart"
      :results="modalState.priceModal.results"
      :error="modalState.priceModal.error"
      @close="closePriceModal"
      @search="searchBestPrice"
    />

    <HsnTsnSearchModal
      :isOpen="modalState.hsnTsnModal.isOpen"
      :isLoading="modalState.hsnTsnModal.isLoading"
      v-model:hsn="modalState.hsnTsnModal.hsn"
      v-model:tsn="modalState.hsnTsnModal.tsn"
      :results="modalState.hsnTsnModal.results"
      :error="modalState.hsnTsnModal.error"
      @close="closeHsnTsnModal"
      @search="searchByHsnTsn"
    />
  </div>
</template>
