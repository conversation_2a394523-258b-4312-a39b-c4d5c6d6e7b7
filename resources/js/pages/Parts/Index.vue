<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import axios from 'axios';
import { debounce } from 'lodash';
import PartCard from './components/PartCard.vue';
import PartDetailsModal from './components/PartDetailsModal.vue';
import VehicleCompatibilityModal from './components/VehicleCompatibilityModal.vue';
import QrScanner from './components/QrScanner.vue';

defineOptions({
  layout: AppSidebarLayout,
});

interface Part {
  id: number;
  name: string;
  part_number: string | null;
  manufacturer: string | null;
  category: string | null;
  description: string | null;
  cost: number | null;
  quantity: number;
  created_at: string;
  updated_at: string;
  compatible_vehicles?: Array<{
    id: number;
    make: string;
    model: string;
    year: number | null;
  }>;
  purchase_links?: Array<{
    store: string;
    url: string;
    price: number | null;
  }>;
  [key: string]: any;
}

interface PriceComparison {
  store: string;
  price: number;
  url: string;
  rating?: number;
  logo?: string;
}

const props = defineProps<{
  parts: Array<Part>;
  categories: Array<string>;
  manufacturers: Array<string>;
  vehicles: Array<{
    id: number;
    make: string;
    model: string;
    year: number | null;
  }>;
}>();

// Zustandsvariablen
const searchQuery = ref('');
const selectedCategory = ref('');
const selectedManufacturer = ref('');
const viewMode = ref<'grid' | 'list'>('grid');
const selectedVehicle = ref<number | null>(null);
const isLoading = ref(false);
const showQrScanner = ref(false);
const showFiltersMobile = ref(false);

// Sortierung
const sortField = ref('name');
const sortDirection = ref('asc');

// Paginierung
const itemsPerPage = 12;
const currentPage = ref(1);

// Ausgewähltes Teil für Detailansicht
const selectedPart = ref<Part | null>(null);
const showPartDetails = ref(false);

// Fahrzeugkompatibilität
const showCompatibilityModal = ref(false);
const partForCompatibility = ref<Part | null>(null);

// Preisvergleich
const isLoadingPrices = ref(false);
const priceComparisons = ref<Record<number, Array<PriceComparison>>>({});

// Filtert und sortiert die Teile 
const filteredParts = computed(() => {
  let result = [...props.parts];

  // Suche
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(part =>
      part.name.toLowerCase().includes(query) ||
      (part.part_number && part.part_number.toLowerCase().includes(query)) ||
      (part.manufacturer && part.manufacturer.toLowerCase().includes(query)) ||
      (part.description && part.description.toLowerCase().includes(query))
    );
  }

  // Kategoriefilter
  if (selectedCategory.value) {
    result = result.filter(part => part.category === selectedCategory.value);
  }

  // Herstellerfilter
  if (selectedManufacturer.value) {
    result = result.filter(part => part.manufacturer === selectedManufacturer.value);
  }

  // Fahrzeugkompatibilität filter
  if (selectedVehicle.value) {
    result = result.filter(part => 
      part.compatible_vehicles && 
      part.compatible_vehicles.some(vehicle => vehicle.id === selectedVehicle.value)
    );
  }

  // Sortierung
  result.sort((a, b) => {
    const aValue = a[sortField.value];
    const bValue = b[sortField.value];

    // Handling für null-Werte
    if (aValue === null && bValue === null) return 0;
    if (aValue === null) return 1;
    if (bValue === null) return -1;

    // String-Vergleich
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection.value === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // Numerischer Vergleich
    return sortDirection.value === 'asc'
      ? aValue - bValue
      : bValue - aValue;
  });

  return result;
});

// Paginierte Teile
const paginatedParts = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return filteredParts.value.slice(start, end);
});

// Gesamtzahl der Seiten
const totalPages = computed(() => {
  return Math.ceil(filteredParts.value.length / itemsPerPage);
});

// Seite ändern
const changePage = (page: number) => {
  currentPage.value = page;
};

// Sortierfeld ändern
const changeSort = (field: string) => {
  if (sortField.value === field) {
    // Richtung umkehren, wenn das gleiche Feld ausgewählt wird
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // Neues Sortierfeld mit aufsteigender Richtung
    sortField.value = field;
    sortDirection.value = 'asc';
  }
};

// Formatiert den Preis als Währung
const formatCurrency = (price: number | null) => {
  if (price === null) return '-';

  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(price);
};

// Teil löschen
const deletePart = (partId: number) => {
  if (confirm('Sind Sie sicher, dass Sie dieses Teil löschen möchten?')) {
    router.delete(route('parts.destroy', partId));
  }
};

// Teil anzeigen
const viewPartDetails = (part: Part) => {
  selectedPart.value = part;
  showPartDetails.value = true;
  // Preise laden, wenn noch nicht vorhanden
  if (!priceComparisons.value[part.id]) {
    fetchPriceComparisons(part);
  }
};

// Kompatibilitätsmodal öffnen
const openCompatibilityModal = (part: Part) => {
  partForCompatibility.value = part;
  showCompatibilityModal.value = true;
};

// Preisvergleich laden
const fetchPriceComparisons = async (part: Part) => {
  if (!part.part_number || isLoadingPrices.value) return;
  
  isLoadingPrices.value = true;
  try {
    // Echte API-Anfrage senden
    const response = await axios.get(route('api.parts.price-comparison'), {
      params: {
        part_number: part.part_number,
        manufacturer: part.manufacturer || undefined,
        name: part.name
      }
    });
    
    if (response.data.success && response.data.results) {
      priceComparisons.value[part.id] = response.data.results;
    } else {
      console.error('Keine Preisvergleichsdaten erhalten:', response.data.message || 'Unbekannter Fehler');
    }
  } catch (error) {
    console.error('Fehler beim Laden der Preisvergleiche:', error);
  } finally {
    isLoadingPrices.value = false;
  }
};

// QR-Code gescannt
const onQrCodeScanned = (partNumber: string) => {
  searchQuery.value = partNumber;
  showQrScanner.value = false;
};

// Debounced Suche
const debouncedSearch = debounce(() => {
  currentPage.value = 1; // Zurück zur ersten Seite bei Suchanfragen
}, 300);

// Überwache Änderungen an der Suchanfrage
watch(searchQuery, () => {
  debouncedSearch();
});

// Reset aller Filter
const resetFilters = () => {
  searchQuery.value = '';
  selectedCategory.value = '';
  selectedManufacturer.value = '';
  selectedVehicle.value = null;
  sortField.value = 'name';
  sortDirection.value = 'asc';
  currentPage.value = 1;
};
</script>

<template>
  <div class="bg-gray-50 min-h-screen pb-12">
    <!-- Hauptheader mit Hintergrundbild -->
    <div class="relative mb-8">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-indigo-800/90 z-0"></div>
      <div class="w-full h-64 bg-[url('/images/parts-header.jpg')] bg-cover bg-center"></div>
      
      <div class="container mx-auto px-4 relative z-10">
        <div class="absolute bottom-8 left-0 right-0 px-6 md:px-8">
          <div class="flex flex-wrap items-center justify-between">
            <div>
              <h1 class="text-3xl md:text-4xl font-bold text-white drop-shadow-lg flex items-center">
                Ersatzteile-Katalog
                <span class="ml-2 text-xs px-2 py-1 bg-yellow-400 text-yellow-900 rounded-full font-medium">PRO</span>
              </h1>
              <p class="text-white/80 mt-2 max-w-2xl">
                Verwalten Sie Ihre Ersatzteile, finden Sie Kompatibilitäten und vergleichen Sie Preise, alles an einem Ort.
              </p>
            </div>
            
            <div class="mt-4 md:mt-0 flex space-x-3">
              <button @click="showQrScanner = true" class="btn bg-white/20 backdrop-blur-md border-0 text-white hover:bg-white/30">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v-4m6 6h2M4 12H2m10-3h2M4 9h2" />
                </svg>
                QR scannen
              </button>
              
              <Link :href="route('parts.create')" class="btn bg-white text-blue-700 hover:bg-blue-50 border-0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Neues Teil
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container mx-auto px-4">
      <!-- Suchleiste und Filter -->
      <div class="bg-white rounded-2xl shadow-md p-6 mb-8 border border-gray-100">
        <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
          <!-- Suchfeld mit Icon -->
          <div class="relative flex-grow max-w-2xl">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Suche nach Teilenummer, Name oder Beschreibung..."
              class="w-full pl-11 pr-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
            />
            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          
          <!-- Ansichtsumschaltung und Filter-Toggle (Mobile) -->
          <div class="flex items-center gap-3">
            <button @click="showFiltersMobile = !showFiltersMobile" class="md:hidden btn btn-outline btn-sm">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              Filter
            </button>
            
            <div class="flex bg-gray-100 rounded-lg p-1">
              <button 
                @click="viewMode = 'grid'" 
                class="p-2 rounded-md transition-colors" 
                :class="viewMode === 'grid' ? 'bg-white shadow-sm' : 'text-gray-600'"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zm-10 10a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button 
                @click="viewMode = 'list'" 
                class="p-2 rounded-md transition-colors" 
                :class="viewMode === 'list' ? 'bg-white shadow-sm' : 'text-gray-600'"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Erweiterte Filter (Desktop immer sichtbar, Mobile umschaltbar) -->
        <div :class="{'hidden md:flex': !showFiltersMobile, 'flex': showFiltersMobile}" class="flex-col md:flex-row gap-4 mt-4">
          <!-- Kategorie Filter -->
          <div class="flex-1">
            <label class="block text-sm font-medium text-gray-700 mb-1.5">Kategorie</label>
            <select
              v-model="selectedCategory"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
            >
              <option value="">Alle Kategorien</option>
              <option v-for="category in categories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
          </div>
          
          <!-- Hersteller Filter -->
          <div class="flex-1">
            <label class="block text-sm font-medium text-gray-700 mb-1.5">Hersteller</label>
            <select
              v-model="selectedManufacturer"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
            >
              <option value="">Alle Hersteller</option>
              <option v-for="manufacturer in manufacturers" :key="manufacturer" :value="manufacturer">
                {{ manufacturer }}
              </option>
            </select>
          </div>
          
          <!-- Fahrzeug-Kompatibilität Filter -->
          <div class="flex-1">
            <label class="block text-sm font-medium text-gray-700 mb-1.5">Kompatibel mit Fahrzeug</label>
            <select
              v-model="selectedVehicle"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
            >
              <option :value="null">Alle Fahrzeuge</option>
              <option v-for="vehicle in vehicles" :key="vehicle.id" :value="vehicle.id">
                {{ vehicle.make }} {{ vehicle.model }} {{ vehicle.year ? `(${vehicle.year})` : '' }}
              </option>
            </select>
          </div>
          
          <!-- Filter zurücksetzen Button -->
          <div class="flex items-end">
            <button 
              @click="resetFilters" 
              class="px-4 py-2.5 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Filter zurücksetzen
            </button>
          </div>
        </div>
      </div>
      
      <!-- Status und Anzeigebereich -->
      <div class="mb-4 flex items-center justify-between">
        <p class="text-gray-600">
          {{ filteredParts.length }} Teil{{ filteredParts.length !== 1 ? 'e' : '' }} gefunden
        </p>
        
        <!-- Sortier-Dropdown -->
        <div class="flex items-center">
          <span class="text-sm text-gray-500 mr-2">Sortieren nach:</span>
          <select
            v-model="sortField"
            @change="sortDirection = 'asc'"
            class="px-3 py-1.5 rounded-lg border border-gray-300 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
          >
            <option value="name">Name</option>
            <option value="manufacturer">Hersteller</option>
            <option value="category">Kategorie</option>
            <option value="cost">Preis</option>
            <option value="quantity">Bestand</option>
          </select>
          
          <!-- Sortierrichtung-Toggle -->
          <button 
            @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'"
            class="ml-2 p-1.5 rounded-lg border border-gray-300 hover:bg-gray-50"
          >
            <svg v-if="sortDirection === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Keine Teile gefunden -->
      <div v-if="filteredParts.length === 0" class="bg-white rounded-2xl shadow-sm p-8 text-center border border-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-xl font-bold text-gray-700 mb-2">Keine Teile gefunden</h3>
        <p class="text-gray-500 mb-6 max-w-md mx-auto">Die aktuelle Filterauswahl ergab keine Treffer. Bitte passen Sie Ihre Suchkriterien an oder fügen Sie neue Teile hinzu.</p>
        <div class="flex justify-center gap-4">
          <button @click="resetFilters" class="btn btn-outline">Filter zurücksetzen</button>
          <Link :href="route('parts.create')" class="btn btn-primary">
            Neues Teil hinzufügen
          </Link>
        </div>
      </div>
      
      <!-- Teile im Grid-Modus -->
      <div v-else-if="viewMode === 'grid'" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <PartCard 
          v-for="part in paginatedParts" 
          :key="part.id" 
          :part="part"
          :format-currency="formatCurrency"
          @view="viewPartDetails"
          @edit="(part) => router.visit(route('parts.edit', part.id))"
          @delete="deletePart"
          @compatibility="openCompatibilityModal"
        />
      </div>
      
      <!-- Teile im Listenmodus -->
      <div v-else class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-8">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50 border-b border-gray-200">
              <tr>
                <th
                  @click="changeSort('name')"
                  class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Name
                  <span v-if="sortField === 'name'" class="ml-1">
                    {{ sortDirection === 'asc' ? '↑' : '↓' }}
                  </span>
                </th>
                <th
                  @click="changeSort('part_number')"
                  class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Teilenummer
                  <span v-if="sortField === 'part_number'" class="ml-1">
                    {{ sortDirection === 'asc' ? '↑' : '↓' }}
                  </span>
                </th>
                <th
                  @click="changeSort('manufacturer')"
                  class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Hersteller
                  <span v-if="sortField === 'manufacturer'" class="ml-1">
                    {{ sortDirection === 'asc' ? '↑' : '↓' }}
                  </span>
                </th>
                <th
                  @click="changeSort('category')"
                  class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Kategorie
                  <span v-if="sortField === 'category'" class="ml-1">
                    {{ sortDirection === 'asc' ? '↑' : '↓' }}
                  </span>
                </th>
                <th
                  @click="changeSort('quantity')"
                  class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Bestand
                  <span v-if="sortField === 'quantity'" class="ml-1">
                    {{ sortDirection === 'asc' ? '↑' : '↓' }}
                  </span>
                </th>
                <th
                  @click="changeSort('cost')"
                  class="cursor-pointer px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Preis
                  <span v-if="sortField === 'cost'" class="ml-1">
                    {{ sortDirection === 'asc' ? '↑' : '↓' }}
                  </span>
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Aktionen
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="part in paginatedParts" :key="part.id" class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="font-medium text-gray-900">{{ part.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{{ part.part_number || '-' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{{ part.manufacturer || '-' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span v-if="part.category" class="px-2 py-1 text-xs font-medium rounded-full" :class="{
                    'bg-blue-100 text-blue-800': part.category === 'Motor',
                    'bg-green-100 text-green-800': part.category === 'Fahrwerk',
                    'bg-yellow-100 text-yellow-800': part.category === 'Elektrik',
                    'bg-purple-100 text-purple-800': part.category === 'Karosserie',
                    'bg-gray-100 text-gray-800': !['Motor', 'Fahrwerk', 'Elektrik', 'Karosserie'].includes(part.category)
                  }">
                    {{ part.category }}
                  </span>
                  <span v-else>-</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="part.quantity > 0 ? 'text-green-600' : 'text-red-600'" class="font-medium">
                    {{ part.quantity }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatCurrency(part.cost) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end gap-2">
                    <button @click="viewPartDetails(part)" class="text-blue-600 hover:text-blue-900">
                      Details
                    </button>
                    <Link :href="route('parts.edit', part.id)" class="text-indigo-600 hover:text-indigo-900">
                      Bearbeiten
                    </Link>
                    <button @click="deletePart(part.id)" class="text-red-600 hover:text-red-900">
                      Löschen
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Paginierung -->
      <div v-if="totalPages > 1" class="flex justify-center my-8">
        <div class="join">
          <button 
            v-for="page in totalPages" 
            :key="page" 
            @click="changePage(page)" 
            class="join-item btn"
            :class="page === currentPage ? 'btn-active' : ''"
          >
            {{ page }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- QR-Scanner Modal -->
    <QrScanner 
      v-if="showQrScanner" 
      @close="showQrScanner = false"
      @scanned="onQrCodeScanned"
    />
    
    <!-- Teil Details Modal -->
    <PartDetailsModal
      v-if="showPartDetails"
      :part="selectedPart"
      :price-comparisons="selectedPart ? priceComparisons[selectedPart.id] || [] : []"
      :is-loading-prices="isLoadingPrices"
      :format-currency="formatCurrency"
      @close="showPartDetails = false"
      @edit="(part) => router.visit(route('parts.edit', part.id))"
      @delete="deletePart"
      @compatibility="openCompatibilityModal"
      @load-prices="fetchPriceComparisons"
    />
    
    <!-- Fahrzeug-Kompatibilität Modal -->
    <VehicleCompatibilityModal
      v-if="showCompatibilityModal"
      :part="partForCompatibility"
      :vehicles="vehicles"
      @close="showCompatibilityModal = false"
      @save="(compatibilities) => router.post(route('parts.update-compatibility', partForCompatibility.id), { compatible_vehicles: compatibilities })"
    />
  </div>
</template>
