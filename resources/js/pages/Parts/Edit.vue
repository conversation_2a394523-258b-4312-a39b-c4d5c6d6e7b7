<script setup lang="ts">
import { Link, router, useForm } from '@inertiajs/vue3';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  part: {
    id: number;
    name: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    category: string | null;
    quantity: number;
    cost: number | null;
    purchase_url: string | null;
  };
}>();

const form = useForm({
  name: props.part.name,
  part_number: props.part.part_number,
  manufacturer: props.part.manufacturer,
  description: props.part.description,
  category: props.part.category,
  quantity: props.part.quantity,
  cost: props.part.cost,
  purchase_url: props.part.purchase_url,
  _method: 'PUT'
});

const categories = [
  { value: 'engine', label: 'Motor' },
  { value: 'transmission', label: 'Getriebe' },
  { value: 'suspension', label: 'Fahrwerk' },
  { value: 'brake', label: '<PERSON>remsen' },
  { value: 'electrical', label: 'Elektrik' },
  { value: 'body', label: 'Karosserie' },
  { value: 'interior', label: 'Innenraum' },
  { value: 'accessories', label: 'Zubehör' },
  { value: 'fluids', label: 'Betriebsstoffe' },
  { value: 'other', label: 'Sonstiges' }
];

const submit = () => {
  form.post(route('parts.update', props.part.id), {
    preserveScroll: true
  });
};
</script>

<template>
  <div class="p-6 md:p-8 bg-gray-50">
    <div class="flex justify-between items-center mb-8">
      <div class="flex items-center gap-3">
        <Link :href="route('parts.index')" class="btn btn-circle btn-outline btn-sm border-gray-300 hover:bg-gray-100 hover:border-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg>
        </Link>
        <h1 class="text-2xl font-bold text-gray-800">Ersatzteil bearbeiten</h1>
      </div>
    </div>

    <form @submit.prevent="submit">
      <!-- Grundinformationen -->
      <div class="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
        <div class="mb-6 border-b border-gray-100 pb-4">
          <div class="flex items-center">
            <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
            <h2 class="text-xl font-semibold text-gray-800">Teilinformationen</h2>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Name <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.name"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
              required
            />
            <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Teilenummer
            </label>
            <input
              v-model="form.part_number"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.part_number" class="text-red-500 text-sm mt-1">{{ form.errors.part_number }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Hersteller
            </label>
            <input
              v-model="form.manufacturer"
              type="text"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.manufacturer" class="text-red-500 text-sm mt-1">{{ form.errors.manufacturer }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Kategorie
            </label>
            <select
              v-model="form.category"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            >
              <option value="">-- Kategorie auswählen --</option>
              <option v-for="category in categories" :key="category.value" :value="category.value">
                {{ category.label }}
              </option>
            </select>
            <div v-if="form.errors.category" class="text-red-500 text-sm mt-1">{{ form.errors.category }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Menge
            </label>
            <input
              v-model="form.quantity"
              type="number"
              min="0"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.quantity" class="text-red-500 text-sm mt-1">{{ form.errors.quantity }}</div>
          </div>

          <div class="form-control">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Preis (€)
            </label>
            <input
              v-model="form.cost"
              type="number"
              step="0.01"
              min="0"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.cost" class="text-red-500 text-sm mt-1">{{ form.errors.cost }}</div>
          </div>

          <div class="form-control md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Kauf-URL
            </label>
            <input
              v-model="form.purchase_url"
              type="url"
              placeholder="https://"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            />
            <div v-if="form.errors.purchase_url" class="text-red-500 text-sm mt-1">{{ form.errors.purchase_url }}</div>
          </div>

          <div class="form-control md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Beschreibung
            </label>
            <textarea
              v-model="form.description"
              rows="4"
              class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
            ></textarea>
            <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
          </div>
        </div>
      </div>

      <div class="flex justify-end gap-4">
        <Link
          :href="route('parts.index')"
          class="px-6 py-2.5 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50 transition-colors duration-200"
        >
          Abbrechen
        </Link>
        <button
          type="submit"
          class="px-6 py-2.5 rounded-lg bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors duration-200"
          :disabled="form.processing"
        >
          <span v-if="form.processing">Wird gespeichert...</span>
          <span v-else>Speichern</span>
        </button>
      </div>
    </form>
  </div>
</template>
