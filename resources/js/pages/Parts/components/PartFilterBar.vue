<script setup lang="ts">
defineProps<{
  categories: string[]
}>();

const emit = defineEmits<{
  (e: 'update:search', value: string): void
  (e: 'update:category', value: string): void
  (e: 'hsn-tsn-search'): void
}>();

defineOptions({
  inheritAttrs: false,
});
</script>

<template>
  <div class="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="form-control">
        <label class="block text-sm font-medium text-gray-700 mb-2">Suche</label>
        <div class="relative">
          <input
            :value="$attrs.search"
            @input="emit('update:search', ($event.target as HTMLInputElement).value)"
            type="text"
            placeholder="Suche nach Name, Teilenummer oder Hersteller..."
            class="w-full pl-10 pr-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
          />
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
          </div>
        </div>
      </div>

      <div class="form-control">
        <label class="block text-sm font-medium text-gray-700 mb-2">Kategorie</label>
        <select
          :value="$attrs.category"
          @change="emit('update:category', ($event.target as HTMLSelectElement).value)"
          class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200"
        >
          <option value="">Alle Kategorien</option>
          <option v-for="category in categories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>
      </div>

      <div class="form-control flex items-end">
        <button
          @click="emit('hsn-tsn-search')"
          class="w-full px-4 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
          </svg>
          Suche nach HSN/TSN
        </button>
      </div>
    </div>
  </div>
</template> 