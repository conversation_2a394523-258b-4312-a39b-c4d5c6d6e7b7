<script setup lang="ts">
interface VehicleInfo {
  hsn: string;
  tsn: string;
  make: string;
  model: string;
  type: string;
  year_from: number | null;
  year_to: number | null;
  engine: string;
  engine_code: string;
}

interface CompatiblePart {
  name: string;
  manufacturer: string;
  part_number: string;
  price: number;
  url: string;
  image_url?: string;
}

interface CompatibleCategory {
  category: string;
  parts: CompatiblePart[];
}

interface HsnTsnSearchResult {
  success: boolean;
  vehicle_info: VehicleInfo;
  compatible_parts: CompatibleCategory[];
  error?: string;
}

defineProps<{
  isOpen: boolean;
  isLoading: boolean;
  hsn: string;
  tsn: string;
  results: HsnTsnSearchResult | null;
  error: string;
  formatCurrency: (price: number | null) => string;
}>();

const emit = defineEmits<{
  (e: 'update:hsn', value: string): void
  (e: 'update:tsn', value: string): void
  (e: 'close'): void;
  (e: 'search'): void;
}>();
</script>

<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="bg-green-600 text-white p-4 rounded-t-xl flex justify-between items-center">
        <h3 class="text-xl font-semibold">
          Teilesuche nach HSN/TSN
        </h3>
        <button @click="emit('close')" class="text-white hover:text-green-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Eingabefelder -->
        <div class="mb-6">
          <p class="text-gray-600 mb-4">Geben Sie die HSN (Herstellerschlüsselnummer) und TSN (Typ-Schlüsselnummer) Ihres Fahrzeugs ein, um passende Teile zu finden.</p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">HSN (4 Ziffern)</label>
              <input
                :value="hsn"
                @input="emit('update:hsn', ($event.target as HTMLInputElement).value)"
                type="text"
                maxlength="4"
                placeholder="z.B. 0600"
                class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none transition-all duration-200"
              />
              <p class="text-xs text-gray-500 mt-1">Herstellerschlüsselnummer, zu finden in Feld 2.1 der Zulassungsbescheinigung Teil I</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">TSN (3 Zeichen)</label>
              <input
                :value="tsn"
                @input="emit('update:tsn', ($event.target as HTMLInputElement).value)"
                type="text"
                maxlength="3"
                placeholder="z.B. 301 oder AAA"
                class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none transition-all duration-200"
              />
              <p class="text-xs text-gray-500 mt-1">Typ-Schlüsselnummer, zu finden in Feld 2.2 der Zulassungsbescheinigung Teil I (3 Ziffern oder Buchstaben)</p>
            </div>
          </div>

          <button
            @click="emit('search')"
            class="w-full py-3 px-6 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="flex items-center justify-center">
              <span class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
              Suche läuft...
            </span>
            <span v-else>Passende Teile suchen</span>
          </button>
        </div>

        <!-- Fehler -->
        <div v-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <span>{{ error }}</span>
          </div>
        </div>

        <!-- Erfolgreiche Suche: Fahrzeuginformationen -->
        <div v-if="results && results.vehicle_info" class="mb-6">
          <h4 class="text-lg font-medium text-gray-800 mb-3">Ihr Fahrzeug</h4>

          <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p class="text-gray-700 mb-1"><span class="font-medium">HSN/TSN:</span> {{ results.vehicle_info.hsn }} / {{ results.vehicle_info.tsn }}</p>
                <p class="text-gray-700 mb-1"><span class="font-medium">Hersteller:</span> {{ results.vehicle_info.make }}</p>
                <p class="text-gray-700 mb-1"><span class="font-medium">Modell:</span> {{ results.vehicle_info.model }}</p>
                <p class="text-gray-700"><span class="font-medium">Typ:</span> {{ results.vehicle_info.type }}</p>
              </div>
              <div>
                <p class="text-gray-700 mb-1"><span class="font-medium">Baujahr:</span> {{ results.vehicle_info.year_from }}{{ results.vehicle_info.year_to ? ' - ' + results.vehicle_info.year_to : ' bis heute' }}</p>
                <p class="text-gray-700 mb-1"><span class="font-medium">Motor:</span> {{ results.vehicle_info.engine }}</p>
                <p class="text-gray-700"><span class="font-medium">Motorcode:</span> {{ results.vehicle_info.engine_code }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="bg-gray-50 p-4 rounded-b-xl border-t border-gray-200">
        <button @click="emit('close')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100">
          Schließen
        </button>
      </div>
    </div>
  </div>
</template> 