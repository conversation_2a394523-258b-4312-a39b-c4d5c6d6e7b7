<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number | null;
}

const props = defineProps<{
  part: {
    id: number;
    name: string;
    compatible_vehicles?: Array<Vehicle>;
  } | null;
  vehicles: Array<Vehicle>;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'save', compatibilities: Array<number>): void;
}>();

// Aktuell ausgewählte Fahrzeuge
const selectedVehicleIds = ref<number[]>([]);

// Suchfilter und Gruppenfilter
const searchQuery = ref('');
const selectedMake = ref<string | null>(null);

// Fahrzeugmarken für Filter
const makes = computed(() => {
  const uniqueMakes = new Set<string>();
  props.vehicles.forEach(vehicle => uniqueMakes.add(vehicle.make));
  return Array.from(uniqueMakes).sort();
});

// Fahrzeuge nach Suche und Markenfilter filtern
const filteredVehicles = computed(() => {
  let result = [...props.vehicles];
  
  // Nach Marke filtern
  if (selectedMake.value) {
    result = result.filter(vehicle => vehicle.make === selectedMake.value);
  }
  
  // Nach Suchbegriff filtern
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(vehicle => 
      vehicle.make.toLowerCase().includes(query) ||
      vehicle.model.toLowerCase().includes(query) ||
      (vehicle.year && vehicle.year.toString().includes(query))
    );
  }
  
  // Nach Marke und Modell gruppieren/sortieren
  return result.sort((a, b) => {
    if (a.make !== b.make) return a.make.localeCompare(b.make);
    if (a.model !== b.model) return a.model.localeCompare(b.model);
    if (a.year !== b.year) {
      if (a.year === null) return 1;
      if (b.year === null) return -1;
      return b.year - a.year; // Neueste Modelle zuerst
    }
    return 0;
  });
});

// Prüfen, ob ein Fahrzeug ausgewählt ist
const isVehicleSelected = (vehicleId: number): boolean => {
  return selectedVehicleIds.value.includes(vehicleId);
};

// Fahrzeugauswahl umschalten
const toggleVehicle = (vehicleId: number): void => {
  if (isVehicleSelected(vehicleId)) {
    selectedVehicleIds.value = selectedVehicleIds.value.filter(id => id !== vehicleId);
  } else {
    selectedVehicleIds.value.push(vehicleId);
  }
};

// Alle Fahrzeuge der aktuellen Filterung auswählen/abwählen
const toggleAll = (select: boolean): void => {
  if (select) {
    // Alle Fahrzeug-IDs aus der gefilterten Liste hinzufügen
    const newIds = filteredVehicles.value.map(v => v.id);
    // Mit Set Duplikate vermeiden
    selectedVehicleIds.value = [...new Set([...selectedVehicleIds.value, ...newIds])];
  } else {
    // Nur die IDs der gefilterten Fahrzeuge entfernen
    const filteredIds = new Set(filteredVehicles.value.map(v => v.id));
    selectedVehicleIds.value = selectedVehicleIds.value.filter(id => !filteredIds.has(id));
  }
};

// Änderungen speichern
const saveChanges = (): void => {
  emit('save', selectedVehicleIds.value);
  emit('close');
};

// Kompatibilitäten beim Laden initialisieren
onMounted(() => {
  if (props.part && props.part.compatible_vehicles) {
    selectedVehicleIds.value = props.part.compatible_vehicles.map(v => v.id);
  }
});
</script>

<template>
  <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 p-4" @click.self="emit('close')">
    <div v-if="part" class="bg-white rounded-2xl shadow-xl max-w-3xl w-full max-h-[80vh] overflow-hidden flex flex-col">
      <!-- Header -->
      <div class="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4 flex justify-between items-center">
        <h2 class="text-xl font-bold text-white">
          Fahrzeugkompatibilität für {{ part.name }}
        </h2>
        <button @click="emit('close')" class="text-white hover:text-gray-200 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <!-- Filter und Suche -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex flex-col md:flex-row gap-3">
          <div class="flex-grow">
            <label class="block text-sm font-medium text-gray-700 mb-1">Suche</label>
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Suche nach Marke, Modell oder Jahr..."
                class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
              />
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Marke</label>
            <select
              v-model="selectedMake"
              class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
            >
              <option :value="null">Alle Marken</option>
              <option v-for="make in makes" :key="make" :value="make">
                {{ make }}
              </option>
            </select>
          </div>
        </div>
        
        <div class="mt-3 flex items-center justify-between">
          <div>
            <span class="text-sm text-gray-600">
              {{ filteredVehicles.length }} Fahrzeuge gefunden
            </span>
          </div>
          
          <div class="flex gap-2">
            <button 
              @click="toggleAll(true)" 
              class="px-3 py-1 rounded-lg text-xs font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors"
            >
              Alle auswählen
            </button>
            <button 
              @click="toggleAll(false)" 
              class="px-3 py-1 rounded-lg text-xs font-medium bg-gray-50 text-gray-700 hover:bg-gray-100 transition-colors"
            >
              Auswahl aufheben
            </button>
          </div>
        </div>
      </div>
      
      <!-- Fahrzeugliste -->
      <div class="overflow-y-auto flex-grow p-4">
        <div v-if="filteredVehicles.length === 0" class="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-gray-600 mb-1">Keine Fahrzeuge gefunden</p>
          <p class="text-gray-500 text-sm">Bitte passen Sie Ihre Suchkriterien an</p>
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div 
            v-for="vehicle in filteredVehicles" 
            :key="vehicle.id" 
            class="border rounded-lg overflow-hidden" 
            :class="isVehicleSelected(vehicle.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white hover:bg-gray-50'"
            @click="toggleVehicle(vehicle.id)"
          >
            <div class="flex items-center p-3 cursor-pointer">
              <!-- Checkbox -->
              <div class="mr-3">
                <div 
                  class="w-5 h-5 rounded border flex items-center justify-center" 
                  :class="isVehicleSelected(vehicle.id) ? 'bg-blue-600 border-blue-600' : 'border-gray-300'"
                >
                  <svg 
                    v-if="isVehicleSelected(vehicle.id)" 
                    xmlns="http://www.w3.org/2000/svg" 
                    class="h-3.5 w-3.5 text-white" 
                    viewBox="0 0 20 20" 
                    fill="currentColor"
                  >
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
              
              <!-- Fahrzeuginfo -->
              <div class="flex-grow">
                <div class="font-medium text-gray-800">
                  {{ vehicle.make }} {{ vehicle.model }}
                </div>
                <div v-if="vehicle.year" class="text-sm text-gray-500">
                  Baujahr {{ vehicle.year }}
                </div>
              </div>
              
              <!-- Marken-Badge -->
              <div class="ml-3">
                <span 
                  class="text-xs px-2 py-1 rounded-full font-medium" 
                  :class="{
                    'bg-red-100 text-red-800': vehicle.make === 'BMW',
                    'bg-blue-100 text-blue-800': vehicle.make === 'Mercedes',
                    'bg-gray-100 text-gray-800': vehicle.make === 'Audi',
                    'bg-yellow-100 text-yellow-800': vehicle.make === 'Volkswagen',
                    'bg-green-100 text-green-800': vehicle.make === 'Opel',
                    'bg-purple-100 text-purple-800': !['BMW', 'Mercedes', 'Audi', 'Volkswagen', 'Opel'].includes(vehicle.make)
                  }"
                >
                  {{ vehicle.make }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer mit Aktionsschaltflächen -->
      <div class="border-t border-gray-200 p-4 bg-gray-50 flex justify-between">
        <div>
          <span class="text-gray-600 text-sm">
            {{ selectedVehicleIds.length }} Fahrzeuge ausgewählt
          </span>
        </div>
        <div class="flex space-x-3">
          <button @click="emit('close')" class="btn btn-ghost">
            Abbrechen
          </button>
          <button @click="saveChanges" class="btn btn-primary">
            Kompatibilität speichern
          </button>
        </div>
      </div>
    </div>
  </div>
</template> 