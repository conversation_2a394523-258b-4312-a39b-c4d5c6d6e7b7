<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  part: {
    id: number;
    name: string;
    part_number: string | null;
    manufacturer: string | null;
    category: string | null;
    description: string | null;
    cost: number | null;
    quantity: number;
    compatible_vehicles?: Array<{
      id: number;
      make: string;
      model: string;
      year: number | null;
    }>;
    purchase_links?: Array<{
      store: string;
      url: string;
      price: number | null;
    }>;
    created_at: string;
    updated_at: string;
  } | null;
  priceComparisons: Array<{
    store: string;
    price: number;
    url: string;
    rating?: number;
    logo?: string;
  }>;
  isLoadingPrices: boolean;
  formatCurrency: (price: number | null) => string;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'edit', part: NonNullable<typeof props.part>): void;
  (e: 'delete', partId: number): void;
  (e: 'compatibility', part: NonNullable<typeof props.part>): void;
  (e: 'load-prices', part: NonNullable<typeof props.part>): void;
}>();

// Überprüfen, ob wir einen Preisvorteil durch den Preisvergleich haben
const bestPriceDifference = computed(() => {
  if (!props.part || !props.part.cost || props.priceComparisons.length === 0) return null;
  
  const bestPrice = Math.min(...props.priceComparisons.map(comp => comp.price));
  const difference = props.part.cost - bestPrice;
  const percentageDifference = (difference / props.part.cost) * 100;
  
  return {
    amount: difference,
    percentage: percentageDifference,
    hasBetterPrice: difference > 0
  };
});

// Formatiert das Datum in lesbares Format
const formatDate = (dateString: string): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};
</script>

<template>
  <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 p-4" @click.self="emit('close')">
    <div v-if="part" class="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
      <!-- Header -->
      <div class="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4 flex justify-between items-center">
        <h2 class="text-xl font-bold text-white">{{ part.name }}</h2>
        <button @click="emit('close')" class="text-white hover:text-gray-200 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <!-- Body -->
      <div class="overflow-y-auto flex-grow p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Linke Spalte: Hauptdetails -->
          <div>
            <div class="space-y-4">
              <!-- Basis-Informationen -->
              <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Informationen</h3>
                
                <div class="space-y-2.5">
                  <div v-if="part.part_number" class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">Teilenummer:</span>
                    <span class="font-mono text-gray-800">{{ part.part_number }}</span>
                  </div>
                  
                  <div v-if="part.manufacturer" class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">Hersteller:</span>
                    <span class="text-gray-800">{{ part.manufacturer }}</span>
                  </div>
                  
                  <div v-if="part.category" class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">Kategorie:</span>
                    <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                      {{ part.category }}
                    </span>
                  </div>
                  
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">Bestand:</span>
                    <span :class="part.quantity > 0 ? 'text-green-600' : 'text-red-600'" class="font-medium">
                      {{ part.quantity }} Stück
                    </span>
                  </div>
                  
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">Preis:</span>
                    <span class="text-blue-700 font-bold">{{ formatCurrency(part.cost) }}</span>
                  </div>
                </div>
              </div>
              
              <!-- Beschreibung -->
              <div v-if="part.description" class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Beschreibung</h3>
                <p class="text-gray-700 whitespace-pre-line">{{ part.description }}</p>
              </div>
              
              <!-- Kompatible Fahrzeuge -->
              <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                <div class="flex justify-between items-center mb-3">
                  <h3 class="text-lg font-semibold text-gray-800">Kompatible Fahrzeuge</h3>
                  <button 
                    @click="emit('compatibility', part)" 
                    class="text-xs px-2 py-1 rounded bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors font-medium"
                  >
                    Bearbeiten
                  </button>
                </div>
                
                <div v-if="part.compatible_vehicles && part.compatible_vehicles.length > 0" class="space-y-2">
                  <div 
                    v-for="vehicle in part.compatible_vehicles" 
                    :key="vehicle.id" 
                    class="bg-white rounded-lg p-2 border border-gray-200 flex items-center justify-between"
                  >
                    <div>
                      <span class="font-medium text-gray-800">{{ vehicle.make }} {{ vehicle.model }}</span>
                      <span v-if="vehicle.year" class="text-gray-500 text-sm ml-1">({{ vehicle.year }})</span>
                    </div>
                  </div>
                </div>
                <p v-else class="text-gray-500 text-sm italic">Keine kompatiblen Fahrzeuge hinterlegt</p>
              </div>
              
              <!-- Metadaten -->
              <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Metadaten</h3>
                
                <div class="space-y-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">Erstellt am:</span>
                    <span class="text-gray-700">{{ formatDate(part.created_at) }}</span>
                  </div>
                  
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500 w-32">Aktualisiert am:</span>
                    <span class="text-gray-700">{{ formatDate(part.updated_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Rechte Spalte: Preisvergleich und mehr -->
          <div>
            <!-- Preisvergleich -->
            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200 mb-6">
              <div class="flex justify-between items-center mb-3">
                <h3 class="text-lg font-semibold text-gray-800">Preisvergleich</h3>
                <button 
                  v-if="priceComparisons.length === 0 && !isLoadingPrices" 
                  @click="emit('load-prices', part)" 
                  class="text-xs px-2 py-1 rounded bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors font-medium"
                >
                  Preise laden
                </button>
              </div>
              
              <!-- Loading-Zustand -->
              <div v-if="isLoadingPrices" class="py-4 flex flex-col items-center justify-center">
                <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-8 w-8 mb-3 animate-spin border-t-blue-500"></div>
                <p class="text-gray-600 text-sm">Preise werden geladen...</p>
              </div>
              
              <!-- Keine Preise -->
              <div v-else-if="priceComparisons.length === 0" class="py-4 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p class="text-gray-500 text-sm">Keine Preisvergleiche verfügbar</p>
                <p class="text-gray-400 text-xs mt-1">Klicken Sie auf "Preise laden", um aktuelle Preise zu vergleichen</p>
              </div>
              
              <!-- Preise anzeigen -->
              <div v-else>
                <!-- Sparmöglichkeit anzeigen -->
                <div v-if="bestPriceDifference && bestPriceDifference.hasBetterPrice" class="mb-3 bg-green-50 border border-green-200 rounded-lg p-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  <div>
                    <span class="text-green-700 font-semibold">Sparpotential: {{ formatCurrency(bestPriceDifference.amount) }}</span>
                    <span class="text-green-700 text-sm"> ({{ bestPriceDifference.percentage.toFixed(1) }}%)</span>
                  </div>
                </div>
                
                <!-- Preisliste -->
                <div class="space-y-2.5">
                  <div 
                    v-for="(price, index) in priceComparisons" 
                    :key="index" 
                    class="bg-white rounded-lg p-3 border border-gray-200 flex items-center justify-between"
                    :class="part.cost && price.price < part.cost ? 'border-green-300 bg-green-50' : ''"
                  >
                    <div class="flex items-center">
                      <div v-if="price.logo" class="w-8 h-8 mr-3 flex-shrink-0">
                        <img :src="price.logo" :alt="price.store" class="w-full h-full object-contain">
                      </div>
                      <div v-else class="w-8 h-8 mr-3 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-gray-600 text-xs font-bold">{{ price.store.substring(0, 2).toUpperCase() }}</span>
                      </div>
                      
                      <div>
                        <div class="font-medium text-gray-800">{{ price.store }}</div>
                        <div v-if="price.rating" class="flex items-center text-yellow-500 text-xs">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span class="ml-0.5">{{ price.rating.toFixed(1) }}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="flex items-center">
                      <span 
                        class="font-bold" 
                        :class="part.cost && price.price < part.cost ? 'text-green-700' : 'text-gray-800'"
                      >
                        {{ formatCurrency(price.price) }}
                      </span>
                      <a 
                        :href="price.url" 
                        target="_blank" 
                        class="ml-3 p-1.5 rounded-full hover:bg-gray-100 transition-colors text-blue-600"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
                
                <p class="text-gray-400 text-xs mt-3 text-center">Preise zuletzt aktualisiert am {{ formatDate(new Date().toISOString()) }}</p>
              </div>
            </div>
            
            <!-- QR-Code für Teilenummer -->
            <div v-if="part.part_number" class="bg-gray-50 rounded-xl p-4 border border-gray-200 text-center mb-6">
              <h3 class="text-lg font-semibold text-gray-800 mb-3">Teilenummer QR-Code</h3>
              <div class="flex justify-center mb-3">
                <img 
                  :src="`https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(part.part_number)}`" 
                  :alt="part.part_number" 
                  class="w-32 h-32 border border-gray-300 p-2 bg-white rounded-lg"
                />
              </div>
              <p class="text-sm text-gray-600">Scannen Sie diesen Code, um die Teilenummer schnell zu erfassen</p>
            </div>
            
            <!-- Dokumentation/Anleitungen -->
            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
              <h3 class="text-lg font-semibold text-gray-800 mb-3">Dokumente & Anleitungen</h3>
              
              <!-- Placeholder für künftige Dokumentation -->
              <div class="text-center py-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p class="text-gray-500 text-sm">Noch keine Dokumente vorhanden</p>
                <button class="text-blue-600 text-sm font-medium mt-2 hover:underline">
                  + Dokument hinzufügen
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer mit Aktionsschaltflächen -->
      <div class="border-t border-gray-200 p-4 bg-gray-50 flex justify-between">
        <div>
          <button @click="emit('delete', part.id)" class="btn btn-error">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Löschen
          </button>
        </div>
        <div class="flex space-x-3">
          <button @click="emit('close')" class="btn btn-ghost">
            Schließen
          </button>
          <button @click="emit('edit', part)" class="btn btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            Bearbeiten
          </button>
        </div>
      </div>
    </div>
  </div>
</template> 