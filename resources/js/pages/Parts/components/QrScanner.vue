<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';

// Definieren der Emits für die Komponente
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'scanned', data: string): void;
}>();

// Status für Scanner-Verfügbarkeit und Fehler
const isScanning = ref(false);
const error = ref<string | null>(null);
const videoElement = ref<HTMLVideoElement | null>(null);
let videoStream: MediaStream | null = null;

// Status für manuelle Eingabe
const manualEntry = ref(false);
const manualPartNumber = ref('');

// Scanner initialisieren
const initScanner = async () => {
  isScanning.value = true;
  error.value = null;
  
  try {
    // Benutzeroberfläche für Kamerazugriff anfordern
    const stream = await navigator.mediaDevices.getUserMedia({ 
      video: { facingMode: 'environment' } 
    });
    
    videoStream = stream;
    
    if (videoElement.value) {
      videoElement.value.srcObject = stream;
      
      // Da wir keinen tatsächlichen QR-Scanner in diesem Demo-Code implementieren, 
      // simulieren wir die Erkennung nach einigen Sekunden
      setTimeout(() => {
        const mockPartNumber = 'BMW-' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
        emit('scanned', mockPartNumber);
      }, 3000);
    }
  } catch (err) {
    console.error('Fehler beim Zugriff auf die Kamera:', err);
    error.value = 'Kamerazugriff fehlgeschlagen. Bitte erlauben Sie den Zugriff auf Ihre Kamera oder geben Sie die Teilenummer manuell ein.';
    isScanning.value = false;
  }
};

// Stream stoppen
const stopScanner = () => {
  if (videoStream) {
    videoStream.getTracks().forEach(track => track.stop());
    videoStream = null;
  }
  
  if (videoElement.value) {
    videoElement.value.srcObject = null;
  }
  
  isScanning.value = false;
};

// Manuelle Eingabe der Teilenummer
const submitManualEntry = () => {
  if (manualPartNumber.value.trim()) {
    emit('scanned', manualPartNumber.value.trim());
  }
};

// Starte Scanner beim Laden der Komponente
onMounted(() => {
  initScanner();
});

// Stoppe Scanner beim Entladen der Komponente
onBeforeUnmount(() => {
  stopScanner();
});
</script>

<template>
  <div class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70 p-4">
    <div class="bg-white rounded-2xl shadow-xl max-w-lg w-full overflow-hidden">
      <!-- Header -->
      <div class="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4 flex justify-between items-center">
        <h2 class="text-xl font-bold text-white">
          {{ manualEntry ? 'Teilenummer eingeben' : 'QR-Code scannen' }}
        </h2>
        <button @click="emit('close')" class="text-white hover:text-gray-200 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <!-- Scanner/Manueller Eingabebereich -->
      <div v-if="!manualEntry" class="p-6">
        <div class="relative mb-4">
          <!-- Video-Element für Kamerastream -->
          <div class="w-full h-64 bg-gray-900 rounded-xl overflow-hidden relative">
            <video 
              ref="videoElement" 
              class="w-full h-full object-cover" 
              autoplay 
              playsinline
            ></video>
            
            <!-- Overlay mit Scanbereich -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-48 h-48 border-2 border-white rounded-lg opacity-70"></div>
            </div>
            
            <!-- Animierter Scanstreifen -->
            <div v-if="isScanning" class="absolute left-0 right-0 h-0.5 bg-green-500 animate-scan"></div>
          </div>
          
          <!-- Fehlerhinweis -->
          <div v-if="error" class="mt-3 text-red-600 text-sm">
            {{ error }}
          </div>
          
          <!-- Hinweis -->
          <p v-else-if="isScanning" class="mt-3 text-gray-600 text-sm text-center">
            Positionieren Sie den QR-Code der Teilenummer im markierten Bereich.
          </p>
        </div>
        
        <!-- Toggle zu manueller Eingabe -->
        <div class="text-center">
          <button @click="manualEntry = true" class="text-blue-600 text-sm font-medium hover:underline">
            Teilenummer manuell eingeben
          </button>
        </div>
      </div>
      
      <!-- Manuelle Eingabe -->
      <div v-else class="p-6">
        <div class="mb-4">
          <label for="part-number" class="block text-sm font-medium text-gray-700 mb-1">Teilenummer</label>
          <input
            id="part-number"
            v-model="manualPartNumber"
            type="text"
            placeholder="z.B. BMW-123456"
            class="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
          />
        </div>
        
        <!-- Hinweis -->
        <p class="text-gray-600 text-sm mb-4">
          Geben Sie die Teilenummer ein, die Sie suchen oder hinzufügen möchten.
        </p>
        
        <!-- Toggle zurück zu Scanner -->
        <div class="text-center">
          <button @click="manualEntry = false; initScanner()" class="text-blue-600 text-sm font-medium hover:underline">
            Zurück zum QR-Scanner
          </button>
        </div>
      </div>
      
      <!-- Footer mit Aktionsschaltflächen -->
      <div class="border-t border-gray-200 p-4 bg-gray-50 flex justify-between">
        <button @click="emit('close')" class="btn btn-ghost">
          Abbrechen
        </button>
        <button 
          v-if="manualEntry" 
          @click="submitManualEntry" 
          :disabled="!manualPartNumber.trim()" 
          class="btn btn-primary"
          :class="{ 'opacity-50 cursor-not-allowed': !manualPartNumber.trim() }"
        >
          Bestätigen
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes scan {
  0% {
    top: 10%;
  }
  50% {
    top: 90%;
  }
  100% {
    top: 10%;
  }
}

.animate-scan {
  animation: scan 2s linear infinite;
}
</style> 