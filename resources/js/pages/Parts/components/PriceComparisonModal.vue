<script setup lang="ts">
interface Part {
  id: number;
  name: string;
  part_number: string | null;
  manufacturer: string | null;
  category: string | null;
  description: string | null;
  cost: number | null;
  quantity: number;
  created_at: string;
  updated_at: string;
  [key: string]: string | number | null;
}

interface PriceSearchResult {
  provider: string;
  price: number;
  title: string;
  url: string;
  availability: string;
  shipping_cost: number;
  rating: number;
  delivery_time: string;
  image_url?: string;
}

defineProps<{
  isOpen: boolean;
  isLoading: boolean;
  selectedPart: Part | null;
  results: PriceSearchResult[];
  error: string;
  formatCurrency: (price: number | null) => string;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'search'): void;
}>();

// Helper function for calculating total price
const calculateTotalPrice = (price: number, shippingCost: number) => {
  return price + shippingCost;
};
</script>

<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="bg-blue-600 text-white p-4 rounded-t-xl flex justify-between items-center">
        <h3 class="text-xl font-semibold">
          Preisvergleich für {{ selectedPart?.name }} {{ selectedPart?.part_number ? `(${selectedPart.part_number})` : '' }}
        </h3>
        <button @click="emit('close')" class="text-white hover:text-blue-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Suchinitiierung -->
        <div v-if="!isLoading && results.length === 0 && !error" class="text-center py-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-blue-500 mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"/>
            <line x1="16" y1="7" x2="16" y2="13"/>
            <line x1="13" y1="10" x2="19" y2="10"/>
            <line x1="22" y1="2" x2="11" y2="13"/>
            <path d="M17 2h5v5"/>
          </svg>
          <h4 class="text-lg font-medium text-gray-800 mb-3">Online-Preise vergleichen</h4>
          <p class="text-gray-600 mb-6">Vergleichen Sie die Preise für dieses Ersatzteil bei verschiedenen Online-Händlern.</p>
          <button
            @click="emit('search')"
            class="py-3 px-6 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
          >
            Preise vergleichen
          </button>
        </div>

        <!-- Ladezustand -->
        <div v-if="isLoading" class="py-10 text-center">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700 mb-4"></div>
          <h3 class="text-lg font-medium text-gray-800">Suche nach den besten Preisen...</h3>
          <p class="text-gray-500 mt-2">Wir durchsuchen verschiedene Anbieter nach Ersatzteilen</p>
        </div>

        <!-- Fehler -->
        <div v-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <span>{{ error }}</span>
          </div>
        </div>

        <!-- Ergebnisse -->
        <div v-if="results.length > 0" class="space-y-6">
          <h3 class="text-lg font-medium text-gray-800 mb-2">Gefundene Angebote ({{ results.length }})</h3>

          <!-- Bestes Angebot -->
          <div v-if="results.length > 0" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex items-center gap-2 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <h4 class="text-lg font-semibold text-green-800">Bestes Angebot</h4>
            </div>

            <div class="flex flex-col md:flex-row md:items-start gap-4">
              <div class="flex-grow">
                <p class="font-medium text-gray-900 mb-1">{{ results[0].title }}</p>
                <p class="text-gray-600 mb-2">Anbieter: {{ results[0].provider }}</p>

                <div class="flex items-center gap-3 mb-2">
                  <span class="px-2 py-1 bg-green-100 text-green-800 rounded-md text-sm">{{ results[0].availability }}</span>
                </div>

                <p class="text-sm text-gray-600">Lieferzeit: {{ results[0].delivery_time }}</p>
              </div>

              <div class="flex flex-col items-end">
                <div class="text-right mb-3">
                  <p class="text-sm text-gray-500">Teilpreis: {{ formatCurrency(results[0].price) }}</p>
                  <p class="text-sm text-gray-500">Versand: {{ formatCurrency(results[0].shipping_cost) }}</p>
                  <p class="text-lg font-bold text-green-700 mt-1">
                    Gesamt: {{ formatCurrency(calculateTotalPrice(results[0].price, results[0].shipping_cost)) }}
                  </p>
                </div>

                <a :href="results[0].url" target="_blank" rel="noopener noreferrer"
                  class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-center"
                >
                  Jetzt kaufen
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="bg-gray-50 p-4 rounded-b-xl border-t border-gray-200 flex justify-between">
        <button @click="emit('close')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100">
          Schließen
        </button>

        <button
          v-if="results.length > 0 && !isLoading"
          @click="emit('search')"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Suche aktualisieren
        </button>
      </div>
    </div>
  </div>
</template> 