<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps<{
  part: {
    id: number;
    name: string;
    part_number: string | null;
    manufacturer: string | null;
    category: string | null;
    description: string | null;
    cost: number | null;
    quantity: number;
    compatible_vehicles?: Array<{
      id: number;
      make: string;
      model: string;
      year: number | null;
    }>;
  };
  formatCurrency: (price: number | null) => string;
}>();

const emit = defineEmits<{
  (e: 'view', part: typeof props.part): void;
  (e: 'edit', part: typeof props.part): void;
  (e: 'delete', partId: number): void;
  (e: 'compatibility', part: typeof props.part): void;
}>();

// Kategorie-Badge Styling
const categoryClass = computed(() => {
  if (!props.part.category) return 'bg-gray-100 text-gray-800';
  
  switch (props.part.category) {
    case 'Motor': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'Fahrwerk': return 'bg-green-100 text-green-800 border-green-200';
    case 'Elektrik': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'Bremsen': return 'bg-red-100 text-red-800 border-red-200';
    case 'Karosserie': return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'Innenraum': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    case 'Beleuchtung': return 'bg-orange-100 text-orange-800 border-orange-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
});

// Bestand Styling
const stockClass = computed(() => {
  if (props.part.quantity <= 0) return 'text-red-600';
  if (props.part.quantity < 5) return 'text-yellow-600';
  return 'text-green-600';
});
</script>

<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all overflow-hidden flex flex-col h-full">
    <!-- Oberer Teil mit Bild (falls vorhanden) oder Fallback-Darstellung -->
    <div class="h-40 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center relative">
      <!-- Kompatible Fahrzeuge Badge -->
      <div 
        v-if="part.compatible_vehicles && part.compatible_vehicles.length > 0" 
        class="absolute top-3 right-3 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium border border-green-200"
      >
        {{ part.compatible_vehicles.length }} kompatibel
      </div>
      
      <!-- Kategorie Badge -->
      <div v-if="part.category" class="absolute top-3 left-3">
        <span class="px-2.5 py-1 rounded-full text-xs font-medium border" :class="categoryClass">
          {{ part.category }}
        </span>
      </div>
      
      <!-- Manufacturer Icon or Placeholder -->
      <div class="text-center p-4">
        <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-gray-300 flex items-center justify-center text-white">
          <svg v-if="part.category === 'Motor'" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <svg v-else-if="part.category === 'Elektrik'" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <svg v-else-if="part.category === 'Fahrwerk'" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
          </svg>
        </div>
        <p v-if="part.manufacturer" class="text-sm text-gray-600 font-medium">{{ part.manufacturer }}</p>
      </div>
    </div>
    
    <!-- Teileinformationen -->
    <div class="p-4 flex-grow">
      <h3 class="font-bold text-gray-800 text-lg mb-1 line-clamp-1">{{ part.name }}</h3>
      
      <div v-if="part.part_number" class="flex items-center text-sm text-gray-500 mb-3">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
        <span class="font-mono">{{ part.part_number }}</span>
      </div>
      
      <div v-if="part.description" class="text-sm text-gray-600 mb-3 line-clamp-2">
        {{ part.description }}
      </div>
      
      <div class="flex items-center justify-between mb-1">
        <span class="text-sm font-medium text-gray-500">Bestand:</span>
        <span class="font-bold" :class="stockClass">{{ part.quantity }}</span>
      </div>
      
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-gray-500">Preis:</span>
        <span class="font-bold text-blue-700">{{ formatCurrency(part.cost) }}</span>
      </div>
    </div>
    
    <!-- Aktionsleiste -->
    <div class="bg-gray-50 p-3 border-t border-gray-200 flex justify-between">
      <button @click="emit('view', part)" class="btn btn-sm btn-primary">
        Details
      </button>
      
      <div class="flex gap-2">
        <button @click="emit('compatibility', part)" class="btn btn-sm btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
          </svg>
        </button>
        
        <button @click="emit('edit', part)" class="btn btn-sm btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
          </svg>
        </button>
        
        <button @click="emit('delete', part.id)" class="btn btn-sm btn-outline btn-error">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template> 