<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import { ref, computed } from 'vue';
import {
  <PERSON>, Wrench, CalendarCheck, AlertTriangle,
  ChevronRight, Plus, TrendingUp, Euro
} from 'lucide-vue-next';

defineOptions({
  layout: AppSidebarLayout,
});

const props = defineProps<{
  vehicles: Array<{
    id: number;
    make: string;
    model: string;
    year: number | null;
    license_plate: string | null;
    mileage: number;
    image: string | null;
    is_active: boolean;
  }>;
  totalVehicles: number;
  activeVehicles: number;
  recentMaintenanceLogs: Array<{
    id: number;
    title: string;
    date: string;
    type: string;
    cost: number | null;
    vehicle: {
      id: number;
      make: string;
      model: string;
    }
  }>;
  upcomingServices: Array<{
    id: number;
    title: string;
    due_date: string;
    priority: string;
    vehicle: {
      id: number;
      make: string;
      model: string;
    }
  }>;
  overdueServices: Array<{
    id: number;
    title: string;
    due_date: string;
    priority: string;
    vehicle: {
      id: number;
      make: string;
      model: string;
    }
  }>;
  vehicleCosts: Record<number, number>;
  totalCosts: number;
}>();

// Calculate total logs
const totalMaintenanceLogs = computed(() =>
  props.recentMaintenanceLogs?.length || 0
);

// Helper Functions
const getPriorityClass = (priority: string) => {
  switch (priority) {
    case 'high': return 'bg-red-100 text-red-700 border-red-300';
    case 'medium': return 'bg-amber-100 text-amber-700 border-amber-300';
    case 'low': return 'bg-blue-100 text-blue-700 border-blue-300';
    default: return 'bg-gray-100 text-gray-700 border-gray-300';
  }
};

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'high': return 'text-red-500';
    case 'medium': return 'text-amber-500';
    case 'low': return 'text-blue-500';
    default: return 'text-gray-500';
  }
};

const getMaintenanceTypeClass = (type: string) => {
  switch (type) {
    case 'repair': return 'bg-red-100 text-red-700 border-red-300';
    case 'service': return 'bg-blue-100 text-blue-700 border-blue-300';
    case 'modification': return 'bg-purple-100 text-purple-700 border-purple-300';
    case 'inspection': return 'bg-amber-100 text-amber-700 border-amber-300';
    default: return 'bg-gray-100 text-gray-700 border-gray-300';
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
  }).format(value);
};

// Vehicle image handling
const getVehicleImage = (vehicle: {
  image: string | null;
  make: string;
  model: string;
}) => {
  if (vehicle.image) {
    // If it's a full URL, return as is
    if (vehicle.image.startsWith('http://') || vehicle.image.startsWith('https://')) {
      return vehicle.image;
    }

    // Handle case where 'storage/' might already be in the path
    if (vehicle.image.startsWith('storage/')) {
      return `/${vehicle.image}`;
    }

    // Standard case - add storage prefix
    return `/storage/${vehicle.image}`;
  }

  // Fallback to placeholder image
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(vehicle.make + ' ' + vehicle.model)}&background=random&color=fff&size=256`;
};

// Color generation for charts
const getRandomColor = (index: number) => {
  const colors = [
    'rgba(59, 130, 246, 0.6)', // blue
    'rgba(99, 102, 241, 0.6)', // indigo
    'rgba(139, 92, 246, 0.6)', // violet
    'rgba(244, 114, 182, 0.6)', // pink
    'rgba(239, 68, 68, 0.6)',   // red
    'rgba(249, 115, 22, 0.6)',  // orange
    'rgba(234, 179, 8, 0.6)',   // yellow
    'rgba(16, 185, 129, 0.6)',  // emerald
    'rgba(20, 184, 166, 0.6)',  // teal
    'rgba(6, 182, 212, 0.6)',   // cyan
  ];
  return colors[index % colors.length];
};
</script>

<template>
  <div class="p-4 sm:p-6 space-y-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-3xl font-bold tracking-tight text-primary">
        Fahrzeug Dashboard
      </h1>
      <Link :href="route('vehicles.create')" class="btn btn-primary gap-2">
        <Plus class="h-5 w-5" />
        <span>Neues Fahrzeug</span>
      </Link>
    </div>

    <!-- Stats Overview Section -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
      <!-- Vehicles Stat Card -->
      <div class="card bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 shadow-sm hover:shadow-md transition-all duration-300">
        <div class="card-body p-5">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h2 class="text-lg font-medium text-blue-800">Fahrzeuge</h2>
              <div class="mt-1 flex items-baseline">
                <p class="text-3xl font-semibold text-blue-700">{{ totalVehicles }}</p>
                <p class="ml-2 text-sm text-blue-600">
                  <span class="font-medium">{{ activeVehicles }}</span> aktiv
                </p>
              </div>
            </div>
            <div class="bg-blue-200 p-3 rounded-full">
              <Car class="h-7 w-7 text-blue-700" />
            </div>
          </div>
          <Link :href="route('vehicles.index')" class="btn btn-sm btn-ghost text-blue-700 justify-end gap-1 mt-4 hover:bg-blue-200/50">
            <span>Alle anzeigen</span>
            <ChevronRight class="h-4 w-4" />
          </Link>
        </div>
      </div>

      <!-- Maintenance Logs Stat Card -->
      <div class="card bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 shadow-sm hover:shadow-md transition-all duration-300">
        <div class="card-body p-5">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h2 class="text-lg font-medium text-purple-800">Wartungen</h2>
              <div class="mt-1 flex items-baseline">
                <p class="text-3xl font-semibold text-purple-700">{{ totalMaintenanceLogs }}</p>
                <p class="ml-2 text-sm text-purple-600">
                  <span class="font-medium">Einträge</span>
                </p>
              </div>
            </div>
            <div class="bg-purple-200 p-3 rounded-full">
              <Wrench class="h-7 w-7 text-purple-700" />
            </div>
          </div>
          <Link v-if="vehicles.length > 0" :href="route('vehicles.show', vehicles[0].id)" class="btn btn-sm btn-ghost text-purple-700 justify-end gap-1 mt-4 hover:bg-purple-200/50">
            <span>Anzeigen</span>
            <ChevronRight class="h-4 w-4" />
          </Link>
        </div>
      </div>

      <!-- Upcoming Services Stat Card -->
      <div class="card bg-gradient-to-br from-green-50 to-green-100 border border-green-200 shadow-sm hover:shadow-md transition-all duration-300">
        <div class="card-body p-5">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h2 class="text-lg font-medium text-green-800">Anstehend</h2>
              <div class="mt-1 flex items-baseline">
                <p class="text-3xl font-semibold text-green-700">{{ upcomingServices.length }}</p>
                <p class="ml-2 text-sm text-green-600">
                  <span class="font-medium">Services</span>
                </p>
              </div>
            </div>
            <div class="bg-green-200 p-3 rounded-full">
              <CalendarCheck class="h-7 w-7 text-green-700" />
            </div>
          </div>
          <Link :href="route('calendar')" class="btn btn-sm btn-ghost text-green-700 justify-end gap-1 mt-4 hover:bg-green-200/50">
            <span>Zum Kalender</span>
            <ChevronRight class="h-4 w-4" />
          </Link>
        </div>
      </div>

      <!-- Overdue Services Stat Card -->
      <div class="card bg-gradient-to-br from-red-50 to-red-100 border border-red-200 shadow-sm hover:shadow-md transition-all duration-300">
        <div class="card-body p-5">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h2 class="text-lg font-medium text-red-800">Überfällig</h2>
              <div class="mt-1 flex items-baseline">
                <p class="text-3xl font-semibold text-red-700">{{ overdueServices.length }}</p>
                <p class="ml-2 text-sm text-red-600">
                  <span class="font-medium">Services</span>
                </p>
              </div>
            </div>
            <div class="bg-red-200 p-3 rounded-full">
              <AlertTriangle class="h-7 w-7 text-red-700" />
            </div>
          </div>
          <Link :href="route('calendar')" class="btn btn-sm btn-ghost text-red-700 justify-end gap-1 mt-4 hover:bg-red-200/50">
            <span>Sofort ansehen</span>
            <ChevronRight class="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Vehicle Gallery Section -->
      <div class="card bg-base-100 shadow-xl col-span-1 lg:col-span-2">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h2 class="card-title text-xl font-bold">Meine Fahrzeuge</h2>
            <Link :href="route('vehicles.index')" class="btn btn-sm btn-ghost">
              Alle anzeigen
              <ChevronRight class="h-4 w-4 ml-1" />
            </Link>
          </div>

          <div v-if="!vehicles.length" class="alert alert-info shadow-sm">
            <div class="flex items-center gap-3">
              <Car class="h-5 w-5" />
              <span>Sie haben noch keine Fahrzeuge angelegt. Fügen Sie jetzt Ihr erstes Fahrzeug hinzu!</span>
            </div>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            <div v-for="vehicle in vehicles" :key="vehicle.id"
                class="card bg-base-200 hover:bg-base-300 transition-colors duration-200 shadow-sm hover:shadow">
              <figure class="relative h-40 overflow-hidden">
                <img :src="getVehicleImage(vehicle)" :alt="`${vehicle.make} ${vehicle.model}`"
                    class="w-full h-full object-cover" />
                <div class="absolute top-2 right-2">
                  <div class="badge badge-lg" :class="vehicle.is_active ? 'badge-success' : 'badge-ghost'">
                    {{ vehicle.is_active ? 'Aktiv' : 'Inaktiv' }}
                  </div>
                </div>
              </figure>
              <div class="card-body p-4">
                <h2 class="card-title text-lg">{{ vehicle.make }} {{ vehicle.model }}</h2>
                <div class="flex flex-wrap gap-2 text-sm text-base-content/80">
                  <span v-if="vehicle.year">{{ vehicle.year }}</span>
                  <span v-if="vehicle.license_plate" class="badge badge-outline">{{ vehicle.license_plate }}</span>
                </div>
                <div class="flex items-center justify-between mt-2">
                  <div class="text-base font-medium">
                    {{ vehicle.mileage.toLocaleString() }} km
                  </div>
                  <div class="text-base font-semibold text-primary">
                    {{ formatCurrency(vehicleCosts[vehicle.id] || 0) }}
                  </div>
                </div>
                <div class="card-actions justify-end mt-2">
                  <Link :href="route('vehicles.show', vehicle.id)" class="btn btn-sm btn-primary">
                    Details
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Service Notifications -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h2 class="card-title text-xl font-bold text-red-600">Überfällige Services</h2>
          </div>

          <div v-if="overdueServices.length === 0" class="alert alert-success shadow-sm mb-6">
            <div class="flex items-center gap-3">
              <CalendarCheck class="h-5 w-5" />
              <span>Sehr gut! Keine überfälligen Services.</span>
            </div>
          </div>

          <div v-else class="space-y-3 mb-6">
            <div v-for="service in overdueServices.slice(0, 3)" :key="service.id"
                class="p-3 rounded-lg border-l-4 border-red-500 bg-red-50 flex flex-col">
              <div class="font-medium">{{ service.title }}</div>
              <div class="text-sm text-gray-600">{{ service.vehicle.make }} {{ service.vehicle.model }}</div>
              <div class="flex justify-between items-center mt-2">
                <div class="text-xs px-2 py-1 rounded border" :class="getPriorityClass(service.priority)">
                  {{ service.priority === 'high' ? 'Hohe Priorität' : service.priority === 'medium' ? 'Mittlere Priorität' : 'Niedrige Priorität' }}
                </div>
                <div class="text-sm font-medium text-red-600">Fällig am: {{ formatDate(service.due_date) }}</div>
              </div>
            </div>
            <Link v-if="overdueServices.length > 3" :href="route('calendar')" class="btn btn-sm btn-outline btn-error w-full">
              {{ overdueServices.length - 3 }} weitere anzeigen
            </Link>
          </div>

          <div class="flex justify-between items-center mb-4">
            <h2 class="card-title text-xl font-bold text-green-600">Anstehende Services</h2>
          </div>

          <div v-if="upcomingServices.length === 0" class="alert alert-info shadow-sm">
            <div class="flex items-center gap-3">
              <CalendarCheck class="h-5 w-5" />
              <span>Keine anstehenden Services.</span>
            </div>
          </div>

          <div v-else class="space-y-3">
            <div v-for="service in upcomingServices.slice(0, 3)" :key="service.id"
                class="p-3 rounded-lg border-l-4 border-green-500 bg-green-50 flex flex-col">
              <div class="font-medium">{{ service.title }}</div>
              <div class="text-sm text-gray-600">{{ service.vehicle.make }} {{ service.vehicle.model }}</div>
              <div class="flex justify-between items-center mt-2">
                <div class="text-xs px-2 py-1 rounded border" :class="getPriorityClass(service.priority)">
                  {{ service.priority === 'high' ? 'Hohe Priorität' : service.priority === 'medium' ? 'Mittlere Priorität' : 'Niedrige Priorität' }}
                </div>
                <div class="text-sm font-medium text-green-600">Fällig am: {{ formatDate(service.due_date) }}</div>
              </div>
            </div>
            <Link v-if="upcomingServices.length > 3" :href="route('calendar')" class="btn btn-sm btn-outline btn-success w-full">
              {{ upcomingServices.length - 3 }} weitere anzeigen
            </Link>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Maintenance Section -->
    <div v-if="recentMaintenanceLogs.length > 0" class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <div class="flex justify-between items-center mb-4">
          <h2 class="card-title text-xl font-bold">Letzte Wartungen & Reparaturen</h2>
        </div>

        <div class="overflow-x-auto">
          <table class="table table-zebra">
            <thead>
              <tr>
                <th>Vorgang</th>
                <th>Fahrzeug</th>
                <th>Datum</th>
                <th>Typ</th>
                <th>Kosten</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="log in recentMaintenanceLogs" :key="log.id" class="hover">
                <td class="font-medium">{{ log.title }}</td>
                <td>{{ log.vehicle.make }} {{ log.vehicle.model }}</td>
                <td>{{ formatDate(log.date) }}</td>
                <td>
                  <span class="px-2 py-1 text-xs rounded border" :class="getMaintenanceTypeClass(log.type)">
                    {{ log.type === 'repair' ? 'Reparatur' :
                       log.type === 'service' ? 'Wartung' :
                       log.type === 'modification' ? 'Modifikation' :
                       log.type === 'inspection' ? 'Inspektion' : log.type }}
                  </span>
                </td>
                <td class="font-semibold">{{ log.cost ? formatCurrency(log.cost) : '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Total Costs Overview -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <div class="flex justify-between items-center mb-4">
          <h2 class="card-title text-xl font-bold">Kostenübersicht</h2>
        </div>

        <div class="flex flex-col lg:flex-row gap-6 items-center justify-center">
          <div class="stats shadow stats-vertical lg:stats-horizontal">
            <div class="stat bg-primary/10">
              <div class="stat-figure text-primary">
                <Euro class="h-8 w-8" />
              </div>
              <div class="stat-title">Gesamtkosten</div>
              <div class="stat-value text-primary">{{ formatCurrency(totalCosts) }}</div>
              <div class="stat-desc">Alle Fahrzeuge</div>
            </div>

            <div class="stat" v-for="(cost, vehicleId) in vehicleCosts" :key="vehicleId">
              <div class="stat-figure text-secondary">
                <Car class="h-8 w-8" />
              </div>
              <div class="stat-title">
                {{ vehicles.find(v => v.id == vehicleId)?.make }}
                {{ vehicles.find(v => v.id == vehicleId)?.model }}
              </div>
              <div class="stat-value text-secondary">{{ formatCurrency(cost) }}</div>
              <div class="stat-desc">
                <div class="badge badge-outline badge-sm">
                  {{ vehicles.find(v => v.id == vehicleId)?.license_plate || '-' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
