<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import { Building2, Users, TrendingUp, Award, Target, Wrench, Check } from 'lucide-vue-next';

// Timeline Daten
const milestones = [
  {
    year: '2021',
    title: 'Die Idee entsteht',
    description: 'Die Idee für Fahrzeugakte.app entstand aus der persönlichen Herausforderung, Wartungs- und Reparaturnachweise für mehrere Fahrzeuge zu organisieren.'
  },
  {
    year: '2022',
    title: 'Entwicklung beginnt',
    description: 'Das Entwicklungsteam wurde zusammengestellt und begann mit der Konzeption und Entwicklung der digitalen Plattform.'
  },
  {
    year: '2023',
    title: 'Beta-Phase',
    description: 'Die erste Beta-Version von Fahrzeugakte.app wurde mit ausgewählten Testnutzern gestartet, um wertvolles Feedback für die Weiterentwicklung zu sammeln.'
  },
  {
    year: '2024',
    title: 'Öffentlicher Launch',
    description: 'Die vollständige Plattform wurde für die Öffentlichkeit zugänglich gemacht, mit dem Ziel, Fahrzeugbesitzern eine umfassende digitale Lösung anzubieten.'
  }
];

// Team-Daten
const team = [
  {
    name: 'Theodor van Goltz',
    position: 'Gründer & CEO',
    bio: 'Als passionierter Fahrzeugliebhaber und Technik-Enthusiast hat Max Fahrzeugakte.app gegründet, um Fahrzeugbesitzern eine moderne Lösung zur Verwaltung ihrer Fahrzeughistorie zu bieten.',
    image: '/images/team/placeholder.jpg'
  },
  {
    name: 'Anna Schmidt',
    position: 'Technische Leitung',
    bio: 'Anna bringt über 10 Jahre Erfahrung in der Softwareentwicklung mit und leitet die technische Umsetzung unserer innovativen Fahrzeugverwaltungsplattform.',
    image: '/images/team/placeholder.jpg'
  },
  {
    name: 'Thomas Weber',
    position: 'Automobil-Experte',
    bio: 'Mit seiner 15-jährigen Erfahrung als KFZ-Meister sorgt Thomas dafür, dass Fahrzeugakte.app alle relevanten technischen Aspekte und Wartungsanforderungen unterschiedlicher Fahrzeugtypen berücksichtigt.',
    image: '/images/team/placeholder.jpg'
  }
];

// Werte-Daten
const values = [
  {
    icon: Users,
    title: 'Kundenorientierung',
    description: 'Wir stellen die Bedürfnisse unserer Nutzer in den Mittelpunkt und entwickeln unsere Plattform kontinuierlich nach ihrem Feedback weiter.'
  },
  {
    icon: Award,
    title: 'Qualität',
    description: 'Wir setzen hohe Standards für unsere Plattform, um zuverlässige und präzise Informationen für die Fahrzeugverwaltung zu gewährleisten.'
  },
  {
    icon: Target,
    title: 'Innovation',
    description: 'Durch innovative Technologien und Funktionen machen wir die Fahrzeugverwaltung effizienter und zukunftsorientiert.'
  },
  {
    icon: TrendingUp,
    title: 'Nachhaltigkeit',
    description: 'Durch bessere Wartungsplanung und -dokumentation tragen wir zu einer längeren Lebensdauer von Fahrzeugen und somit zum nachhaltigen Umgang mit Ressourcen bei.'
  }
];
</script>

<template>
  <div class="min-h-screen bg-gradient-to-b from-[#f8fafc] to-[#f1f5f9] dark:from-[#0f172a] dark:to-[#020617]">
    <!-- Header (wiederverwendet von Welcome.vue) -->
    <header class="fixed top-0 left-0 right-0 w-full backdrop-blur-md bg-white/70 dark:bg-gray-900/60 z-50 border-b border-gray-200/30 dark:border-gray-800/30">
      <div class="container mx-auto px-6 py-4">
        <nav class="flex justify-between items-center">
          <div class="flex items-center gap-3">
            <div class="relative group">
              <div class="absolute -inset-2 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
              <Link href="/">
                <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-12 w-auto relative z-10 transform group-hover:scale-105 transition-transform duration-300" />
              </Link>
            </div>
            <div class="ml-1">
              <Link href="/" class="text-xl font-semibold text-gray-900 dark:text-white tracking-tight">Fahrzeugakte<span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">.app</span></Link>
            </div>
          </div>

          <div class="hidden md:flex items-center space-x-10">
            <Link href="/#features" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">Features</Link>
            <Link href="/#benefits" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">Vorteile</Link>
            <Link href="/#faq" class="text-sm font-medium text-gray-700 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors">FAQ</Link>
          </div>

          <div class="flex items-center gap-3">
            <Link
              href="/register"
              class="px-5 py-2.5 text-sm font-medium bg-gradient-to-r from-primary to-blue-600 hover:bg-primary/90 text-white rounded-full shadow-sm hover:shadow-lg hover:shadow-primary/20 hover:scale-105 transition-all duration-300"
            >
              Registrieren
            </Link>
          </div>
        </nav>
      </div>
    </header>

    <!-- Spacing for fixed header -->
    <div class="h-20"></div>

    <!-- Hero Section -->
    <section class="py-24 md:py-32 relative overflow-hidden">
      <!-- Background elements -->
      <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.15),transparent_70%)] dark:bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.15),transparent_70%)]"></div>
      <div class="absolute -top-24 -left-24 w-96 h-96 bg-primary/20 rounded-full blur-3xl animate-blob"></div>
      <div class="absolute -bottom-32 left-1/2 w-96 h-96 bg-indigo-500/20 rounded-full blur-3xl animate-blob animation-delay-4000"></div>
      
      <div class="container mx-auto px-6">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">Über Fahrzeugakte<span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">.app</span></h1>
          <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            Wir revolutionieren die Art, wie Fahrzeugbesitzer ihre Fahrzeughistorie verwalten und dokumentieren. Unsere Plattform kombiniert innovative Technologie mit Benutzerfreundlichkeit, um Ihnen die bestmögliche Erfahrung zu bieten.
          </p>
          <div class="flex flex-wrap justify-center gap-4">
            <a href="#mission" class="px-6 py-3 text-sm font-medium bg-white hover:bg-gray-50 text-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white rounded-full shadow-sm hover:shadow-md transition-all duration-300">
              Unsere Mission
            </a>
            <a href="#team" class="px-6 py-3 text-sm font-medium bg-white hover:bg-gray-50 text-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white rounded-full shadow-sm hover:shadow-md transition-all duration-300">
              Unser Team
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Mission Section -->
    <section id="mission" class="py-20 bg-white dark:bg-gray-900">
      <div class="container mx-auto px-6">
        <div class="flex flex-col lg:flex-row items-center gap-12">
          <div class="lg:w-1/2">
            <div class="relative">
              <div class="absolute -inset-2 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-3xl blur-xl"></div>
              <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden">
                <img 
                  src="/images/fahrzeug_manager_00003.png" 
                  alt="Fahrzeugakte Dashboard" 
                  class="w-full h-auto"
                  @error="$event.target.src = '/images/fahrzeugakte-logo.png'; $event.target.style = 'padding:4rem;opacity:0.5;'"
                />
              </div>
            </div>
          </div>
          <div class="lg:w-1/2">
            <div class="mb-6">
              <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-primary to-blue-500 text-white mb-4 shadow-sm">
                Unsere Vision
              </div>
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Wir machen Fahrzeugverwaltung einfach</h2>
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                Bei Fahrzeugakte.app glauben wir daran, dass eine umfassende und gut organisierte Fahrzeugdokumentation nicht nur den Wert eines Fahrzeugs steigert, sondern auch zu einer besseren Wartung und längeren Lebensdauer beiträgt.
              </p>
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                Unsere Mission ist es, Fahrzeugbesitzern ein intuitives und leistungsfähiges Werkzeug an die Hand zu geben, mit dem sie ihre gesamte Fahrzeughistorie lückenlos dokumentieren können.
              </p>
              <p class="text-gray-600 dark:text-gray-300">
                Inspiriert vom Konzept der Do-It-Auto-Philosophie, ermöglichen wir es jedem Fahrzeugbesitzer, selbst die Kontrolle über die Wartung und Dokumentation seines Fahrzeugs zu übernehmen.
              </p>
            </div>

            <div class="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-gray-200/50 dark:border-gray-700/50">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Unsere Kernwerte</h3>
              <ul class="space-y-3">
                <li class="flex items-start" v-for="(value, index) in values" :key="index">
                  <div class="flex-shrink-0 w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mt-1">
                    <component :is="value.icon" class="h-3 w-3 text-primary" />
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ value.title }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ value.description }}</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Timeline Section -->
    <section class="py-20 bg-gray-50 dark:bg-gray-800/50 relative overflow-hidden">
      <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_30%_30%,rgba(var(--primary-rgb),0.1),transparent_70%)]"></div>
      
      <div class="container mx-auto px-6 relative">
        <div class="max-w-3xl mx-auto text-center mb-12">
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-primary to-blue-500 text-white mb-4 shadow-sm">
            Unsere Geschichte
          </div>
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Wie alles begann</h2>
          <p class="text-gray-600 dark:text-gray-300">
            Von der ersten Idee bis zur heutigen Plattform - unsere Reise ist geprägt von der stetigen Verbesserung und dem Streben nach innovativen Lösungen für Fahrzeugbesitzer.
          </p>
        </div>

        <div class="relative">
          <!-- Timeline center line -->
          <div class="hidden md:block absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-gradient-to-b from-primary/30 via-blue-500/30 to-indigo-500/30"></div>

          <!-- Timeline items -->
          <div class="space-y-12">
            <div 
              v-for="(milestone, index) in milestones" 
              :key="index"
              class="relative md:flex md:justify-between md:items-center"
              :class="index % 2 === 0 ? 'md:text-right' : ''"
            >
              <!-- Content for left side (even index) -->
              <div 
                class="md:w-5/12 mb-8 md:mb-0"
                :class="index % 2 === 0 ? 'md:pr-12' : 'md:order-last md:pl-12 md:text-left'"
              >
                <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-200/50 dark:border-gray-700/50">
                  <span class="text-sm font-bold text-primary block mb-2">{{ milestone.year }}</span>
                  <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ milestone.title }}</h3>
                  <p class="text-gray-600 dark:text-gray-300">{{ milestone.description }}</p>
                </div>
              </div>

              <!-- Center dot -->
              <div class="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-4 h-4 rounded-full bg-gradient-to-r from-primary to-blue-500 border-4 border-white dark:border-gray-900"></div>

              <!-- Content for right side or mobile (odd index) -->
              <div
                class="md:w-5/12"
                :class="index % 2 === 0 ? 'md:order-first md:opacity-0' : 'md:pl-12'"
              >
                <div class="md:hidden bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-200/50 dark:border-gray-700/50">
                  <span class="text-sm font-bold text-primary block mb-2">{{ milestone.year }}</span>
                  <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ milestone.title }}</h3>
                  <p class="text-gray-600 dark:text-gray-300">{{ milestone.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section id="team" class="py-20 bg-white dark:bg-gray-900">
      <div class="container mx-auto px-6">
        <div class="max-w-3xl mx-auto text-center mb-16">
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-primary to-blue-500 text-white mb-4 shadow-sm">
            Unser Team
          </div>
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Die Menschen hinter Fahrzeugakte.app</h2>
          <p class="text-gray-600 dark:text-gray-300">
            Unser Team besteht aus leidenschaftlichen Experten aus den Bereichen Automobil, Technologie und Kundenerfahrung, die gemeinsam daran arbeiten, Ihnen das beste Fahrzeugmanagement-Erlebnis zu bieten.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(member, index) in team" :key="index" class="group">
            <div class="relative overflow-hidden rounded-2xl bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all duration-300 h-full flex flex-col">
              <div class="absolute -inset-1 bg-gradient-to-r from-primary/10 via-blue-500/10 to-indigo-500/10 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              
              <div class="aspect-w-3 aspect-h-4 relative">
                <img 
                  :src="member.image" 
                  :alt="member.name"
                  class="w-full h-full object-cover"
                  @error="$event.target.src = 'https://placehold.co/600x400?text=' + encodeURIComponent(member.name); $event.target.style = 'padding:0;'"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"></div>
                <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 class="text-xl font-bold">{{ member.name }}</h3>
                  <p class="text-sm opacity-90">{{ member.position }}</p>
                </div>
              </div>
              
              <div class="p-6 flex-grow">
                <p class="text-gray-600 dark:text-gray-300">{{ member.bio }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-primary to-indigo-600 text-white relative overflow-hidden">
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_10%_50%,rgba(255,255,255,0.1),transparent_50%)]"></div>
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_80%_90%,rgba(255,255,255,0.1),transparent_50%)]"></div>
      
      <div class="container mx-auto px-6 relative">
        <div class="max-w-2xl mx-auto text-center">
          <h2 class="text-3xl font-bold mb-6">Bereit, Ihre Fahrzeugdaten zu digitalisieren?</h2>
          <p class="text-lg opacity-90 mb-8">
            Melden Sie sich heute kostenlos an und entdecken Sie, wie Fahrzeugakte.app Ihnen helfen kann, Ihre Fahrzeughistorie lückenlos zu dokumentieren.
          </p>
          <Link
            href="/register"
            class="relative inline-flex items-center justify-center group"
          >
            <div class="absolute -inset-0.5 bg-white/30 rounded-full blur-sm group-hover:bg-white/40 transition duration-300"></div>
            <span class="relative inline-flex items-center justify-center px-8 py-4 font-medium bg-white hover:bg-gray-50 text-primary rounded-full shadow-xl transition-all duration-300 transform group-hover:-translate-y-1">
              Kostenlos starten
            </span>
          </Link>
        </div>
      </div>
    </section>

    <!-- Footer (wiederverwendet von Welcome.vue) -->
    <footer class="bg-gray-900 text-white py-16 relative overflow-hidden z-20">
      <!-- Background pattern -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.03]"></div>
      
      <div class="container mx-auto px-6 relative z-10">
        <div class="flex flex-col md:flex-row justify-between mb-12">
          <div class="flex items-center gap-4 mb-8 md:mb-0">
            <div class="relative group">
              <div class="absolute -inset-2 bg-gradient-to-r from-primary/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
              <img src="/images/fahrzeugakte-logo.png" alt="Fahrzeugakte.app Logo" class="h-16 w-auto relative z-10 transform group-hover:scale-105 transition-transform duration-300" />
            </div>
            <div>
              <span class="text-2xl font-bold">Fahrzeugakte<span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">.app</span></span>
              <p class="text-sm text-gray-400 mt-1">Die digitale Fahrzeugakte der Zukunft</p>
            </div>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-2 gap-x-16 gap-y-8 mb-8 md:mb-0 relative z-10">
            <div class="flex flex-col space-y-3">
              <p class="text-sm font-medium text-gray-300 uppercase tracking-wider mb-2">Rechtliches</p>
              <a href="/legal/datenschutz" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Datenschutz</a>
              <a href="/legal/impressum" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Impressum</a>
              <a href="/legal/agb" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">AGB</a>
            </div>
            <div class="flex flex-col space-y-3">
              <p class="text-sm font-medium text-gray-300 uppercase tracking-wider mb-2">Unternehmen</p>
              <a href="/about" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Über uns</a>
              <a href="/contact" class="text-sm text-gray-400 hover:text-white transition-colors cursor-pointer block py-1 pointer-events-auto">Kontakt</a>
            </div>
          </div>
        </div>
        
        <div class="pt-8 border-t border-gray-800">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-sm text-gray-500">&copy; 2024 Fahrzeugakte.app - Alle Rechte vorbehalten</p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style>
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.animate-blob {
  animation: blob 15s ease-in-out infinite;
}

@keyframes blob {
  0% {
    transform: scale(1) translate(0px, 0px);
  }
  33% {
    transform: scale(1.1) translate(30px, -50px);
  }
  66% {
    transform: scale(0.9) translate(-20px, 20px);
  }
  100% {
    transform: scale(1) translate(0px, 0px);
  }
}

.animation-delay-4000 {
  animation-delay: 4s;
}

:root {
  --primary-rgb: 79, 70, 229; /* Indigo-600 in RGB format */
}

/* Aspect ratio for team images */
.aspect-w-3 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 3;
}

.aspect-h-4 {
  --tw-aspect-h: 4;
}

.aspect-w-3 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style> 