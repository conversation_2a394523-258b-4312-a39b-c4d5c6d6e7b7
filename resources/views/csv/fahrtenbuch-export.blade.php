<?php echo "<PERSON><PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON>;Startort;<PERSON>ielort;Zweck;Art;KM <PERSON>n;KM <PERSON>;Strecke (km);Notizen\n"; ?>

<?php foreach($vehiclesData as $vehicleId => $data): ?>
<?php foreach($data['trips'] as $trip): ?>
<?php 
    $type = $trip->type === 'business' ? 'Geschäftlich' : ($trip->type === 'private' ? 'Privat' : 'Arbeitsweg');
    echo $data['vehicle']->make . ' ' . $data['vehicle']->model . ';' . 
         $data['vehicle']->license_plate . ';' . 
         $trip->date->format('d.m.Y') . ';' . 
         $trip->start_location . ';' . 
         $trip->end_location . ';' . 
         $trip->purpose . ';' . 
         $type . ';' . 
         $trip->start_odometer . ';' . 
         $trip->end_odometer . ';' . 
         $trip->distance . ';' . 
         $trip->notes . "\n";
?>
<?php endfo<PERSON>ch; ?>
<?php endforeach; ?> 