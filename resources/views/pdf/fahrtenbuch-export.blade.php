<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fahrtenbuch</title>
    <style>
        body {
            font-family: 'Helvetica', 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        
        h1 {
            font-size: 22px;
            margin: 0 0 5px;
        }
        
        h2 {
            font-size: 16px;
            margin: 0 0 10px;
            font-weight: normal;
        }
        
        .info-box {
            background-color: #f7f7f7;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th {
            background-color: #f2f2f2;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 11px;
        }
        
        td {
            border: 1px solid #ddd;
            padding: 8px;
            font-size: 10px;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .summary {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 10px;
            color: #777;
        }
        
        .type-business {
            background-color: #e3f2fd;
        }
        
        .type-private {
            background-color: #ece9ff;
        }
        
        .type-commute {
            background-color: #e8f5e9;
        }
        
        .page-break {
            page-break-after: always;
        }
        
        .subtitle {
            font-size: 14px;
            color: #666;
            margin: 5px 0 15px;
        }
        
        .badge {
            display: inline-block;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 9px;
            text-transform: uppercase;
        }
        
        .badge-business {
            background-color: #e3f2fd;
            color: #0d47a1;
        }
        
        .badge-private {
            background-color: #4f46e5;
            color: #ffffff;
        }
        
        .badge-commute {
            background-color: #e8f5e9;
            color: #1b5e20;
        }
        
        .tax-notice {
            margin-top: 30px;
            padding: 10px;
            border: 1px solid #ffcc80;
            background-color: #fff8e1;
            font-size: 11px;
        }
        
        .tax-notice-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .vehicle-section {
            margin-bottom: 40px;
        }
        
        .vehicle-header {
            background-color: #f0f0f0;
            padding: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #3b82f6;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Fahrtenbuch</h1>
        <div class="subtitle">
            Zeitraum: {{ $startDate->format('d.m.Y') }} - {{ $endDate->format('d.m.Y') }}
        </div>
    </div>
    
    @foreach($vehiclesData as $vehicleId => $data)
    <div class="vehicle-section">
        <div class="vehicle-header">
            <h2>{{ $data['vehicle']->make }} {{ $data['vehicle']->model }} ({{ $data['vehicle']->license_plate }})</h2>
        </div>
        
        <div class="info-box">
            <div class="info-row">
                <div class="info-label">Fahrzeug:</div>
                <div>{{ $data['vehicle']->make }} {{ $data['vehicle']->model }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Kennzeichen:</div>
                <div>{{ $data['vehicle']->license_plate }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Fahrgestellnummer:</div>
                <div>{{ $data['vehicle']->vin }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Fahrzeughalter:</div>
                <div>{{ $data['vehicle']->user->name }}</div>
            </div>
        </div>
        
        <h3>Fahrtenübersicht</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Datum</th>
                    <th>Startort</th>
                    <th>Zielort</th>
                    <th>Zweck</th>
                    <th>Art</th>
                    <th>KM Stand Beginn</th>
                    <th>KM Stand Ende</th>
                    <th>Strecke (km)</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['trips'] as $trip)
                    <tr class="type-{{ $trip->type }}">
                        <td>{{ $trip->date->format('d.m.Y') }}</td>
                        <td>{{ $trip->start_location }}</td>
                        <td>{{ $trip->end_location }}</td>
                        <td>{{ $trip->purpose }}</td>
                        <td>
                            <span class="badge badge-{{ $trip->type }}">
                                @if($trip->type === 'business')
                                    Geschäftlich
                                @elseif($trip->type === 'private')
                                    Privat
                                @else
                                    Arbeitsweg
                                @endif
                            </span>
                        </td>
                        <td>{{ number_format($trip->start_odometer, 0, ',', '.') }}</td>
                        <td>{{ number_format($trip->end_odometer, 0, ',', '.') }}</td>
                        <td>{{ number_format($trip->distance, 0, ',', '.') }}</td>
                    </tr>
                    @if(!empty($trip->notes))
                        <tr class="type-{{ $trip->type }}">
                            <td colspan="8" style="padding-top: 0; font-style: italic; border-top: none;">
                                Notizen: {{ $trip->notes }}
                            </td>
                        </tr>
                    @endif
                @endforeach
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="7" style="text-align: right; font-weight: bold;">Gesamtkilometer:</td>
                    <td style="font-weight: bold;">{{ number_format($data['statistics']['totalKm'], 0, ',', '.') }}</td>
                </tr>
            </tfoot>
        </table>
        
        <div class="summary">
            <h3>Zusammenfassung</h3>
            
            <table>
                <thead>
                    <tr>
                        <th>Art der Fahrten</th>
                        <th>Anzahl</th>
                        <th>Kilometer</th>
                        <th>Prozent</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="type-business">
                        <td>Geschäftlich</td>
                        <td>{{ $data['statistics']['businessTrips'] }}</td>
                        <td>{{ number_format($data['statistics']['businessKm'], 0, ',', '.') }} km</td>
                        <td>{{ number_format($data['statistics']['businessPercent'], 1, ',', '.') }}%</td>
                    </tr>
                    <tr class="type-private">
                        <td>Privat</td>
                        <td>{{ $data['statistics']['privateTrips'] }}</td>
                        <td>{{ number_format($data['statistics']['privateKm'], 0, ',', '.') }} km</td>
                        <td>{{ number_format($data['statistics']['privatePercent'], 1, ',', '.') }}%</td>
                    </tr>
                    <tr class="type-commute">
                        <td>Arbeitsweg</td>
                        <td>{{ $data['statistics']['commuteTrips'] }}</td>
                        <td>{{ number_format($data['statistics']['commuteKm'], 0, ',', '.') }} km</td>
                        <td>{{ number_format($data['statistics']['commutePercent'], 1, ',', '.') }}%</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">Gesamt</td>
                        <td style="font-weight: bold;">{{ $data['statistics']['totalTrips'] }}</td>
                        <td style="font-weight: bold;">{{ number_format($data['statistics']['totalKm'], 0, ',', '.') }} km</td>
                        <td style="font-weight: bold;">100,0%</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        @if(!$loop->last)
            <div class="page-break"></div>
        @endif
    </div>
    @endforeach
    
    <div class="tax-notice">
        <div class="tax-notice-title">Hinweis zur steuerlichen Anerkennung</div>
        <p>
            Dieses Fahrtenbuch enthält alle nach deutschem Steuerrecht erforderlichen Angaben:
        </p>
        <ul>
            <li>Datum und Kilometerstand zu Beginn und Ende jeder einzelnen Fahrt</li>
            <li>Reiseziel, Reiseroute, Reisezweck und aufgesuchte Geschäftspartner</li>
            <li>Nachweis der beruflichen Veranlassung für jede einzelne Fahrt</li>
            <li>Nachvollziehbare Gliederung und Vollständigkeit der Aufzeichnungen</li>
        </ul>
        <p>
            Die Aufzeichnungen wurden zeitnah, lückenlos und fortlaufend in geschlossener Form geführt.
        </p>
    </div>
    
    <div class="footer">
        <p>Erstellt am {{ now()->format('d.m.Y') }} mit fahrzeugakte.app</p>
    </div>
</body>
</html> 