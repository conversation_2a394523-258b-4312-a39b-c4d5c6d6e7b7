<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fahrzeugbericht - {{ $vehicle->make }} {{ $vehicle->model }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 20px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        h1 {
            color: #2563eb;
            margin: 0 0 5px 0;
            font-size: 24px;
        }
        .subtitle {
            color: #666;
            margin: 0;
            font-size: 14px;
        }
        .vehicle-info {
            margin-bottom: 30px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        h2 {
            color: #2563eb;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f7ff;
            text-align: left;
            padding: 10px;
        }
        td {
            padding: 10px;
        }
        .label {
            font-weight: bold;
            color: #555;
            width: 40%;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .badge-success {
            background-color: #ddf5e7;
            color: #0f5132;
        }
        .badge-warning {
            background-color: #fff8e6;
            color: #996500;
        }
        .badge-error {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        .summary-box {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .cost-summary {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
        }
        .page-break {
            page-break-after: always;
        }
        @page {
            margin: 25mm 25mm 25mm 25mm;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Fahrzeugbericht</h1>
        <p class="subtitle">Erstellt am {{ date('d.m.Y') }}</p>
    </div>

    <div class="section">
        <h2>Fahrzeugdaten</h2>
        <div class="info-grid">
            <table>
                <tr>
                    <td class="label">Marke</td>
                    <td>{{ $vehicle->make }}</td>
                </tr>
                <tr>
                    <td class="label">Modell</td>
                    <td>{{ $vehicle->model }}</td>
                </tr>
                <tr>
                    <td class="label">Kennzeichen</td>
                    <td>{{ $vehicle->license_plate ?? '-' }}</td>
                </tr>
                <tr>
                    <td class="label">Baujahr</td>
                    <td>{{ $vehicle->year ?? '-' }}</td>
                </tr>
                <tr>
                    <td class="label">Kilometerstand</td>
                    <td>{{ number_format($vehicle->mileage, 0, ',', '.') }} km</td>
                </tr>
                <tr>
                    <td class="label">Status</td>
                    <td>
                        @if($vehicle->is_active)
                            <span class="badge badge-success">Aktiv</span>
                        @else
                            <span class="badge badge-error">Inaktiv</span>
                        @endif
                    </td>
                </tr>
            </table>

            <table>
                <tr>
                    <td class="label">VIN</td>
                    <td>{{ $vehicle->vin ?? '-' }}</td>
                </tr>
                <tr>
                    <td class="label">HSN/TSN</td>
                    <td>{{ ($vehicle->hsn ?? '-') . ' / ' . ($vehicle->tsn ?? '-') }}</td>
                </tr>
                <tr>
                    <td class="label">Kraftstoff</td>
                    <td>
                        @if($vehicle->fuel_type == 'petrol')
                            Benzin
                        @elseif($vehicle->fuel_type == 'diesel')
                            Diesel
                        @elseif($vehicle->fuel_type == 'electric')
                            Elektrisch
                        @elseif($vehicle->fuel_type == 'hybrid')
                            Hybrid
                        @elseif($vehicle->fuel_type == 'lpg')
                            Autogas (LPG)
                        @elseif($vehicle->fuel_type == 'cng')
                            Erdgas (CNG)
                        @else
                            {{ $vehicle->fuel_type ?? '-' }}
                        @endif
                    </td>
                </tr>
                <tr>
                    <td class="label">Leistung</td>
                    <td>{{ $vehicle->power ? $vehicle->power . ' kW' : '-' }}</td>
                </tr>
                <tr>
                    <td class="label">Getriebe</td>
                    <td>{{ $vehicle->transmission ?? '-' }}</td>
                </tr>
                <tr>
                    <td class="label">Farbe</td>
                    <td>{{ $vehicle->color ?? '-' }}</td>
                </tr>
            </table>
        </div>

        @if($vehicle->notes)
        <h3>Notizen</h3>
        <p>{{ $vehicle->notes }}</p>
        @endif
    </div>

    <div class="section">
        <h2>Kostenzusammenfassung</h2>
        <div class="summary-box">
            <p>Gesamtkosten für Wartung und Reparaturen:</p>
            <p class="cost-summary">{{ number_format($totalCosts, 2, ',', '.') }} €</p>
        </div>
    </div>

    <div class="section">
        <h2>Wartungshistorie</h2>
        @if(count($vehicle->maintenanceLogs) > 0)
            <table>
                <thead>
                    <tr>
                        <th>Datum</th>
                        <th>Beschreibung</th>
                        <th>Typ</th>
                        <th>Kilometerstand</th>
                        <th>Kosten</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($vehicle->maintenanceLogs as $log)
                    <tr>
                        <td>{{ \Carbon\Carbon::parse($log->date)->format('d.m.Y') }}</td>
                        <td>{{ $log->title }}</td>
                        <td>
                            @switch($log->type)
                                @case('service')
                                    Wartung
                                    @break
                                @case('repair')
                                    Reparatur
                                    @break
                                @case('modification')
                                    Umbau
                                    @break
                                @case('inspection')
                                    TÜV/HU
                                    @break
                                @case('purchase')
                                    Kauf
                                    @break
                                @case('sale')
                                    Verkauf
                                    @break
                                @default
                                    {{ $log->type }}
                            @endswitch
                        </td>
                        <td>{{ $log->mileage ? number_format($log->mileage, 0, ',', '.') . ' km' : '-' }}</td>
                        <td>{{ $log->cost ? number_format($log->cost, 2, ',', '.') . ' €' : '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <p>Keine Wartungseinträge vorhanden.</p>
        @endif
    </div>

    <div class="section">
        <h2>Anstehende Servicerinnerungen</h2>
        @if(count($upcomingServices) > 0)
            <table>
                <thead>
                    <tr>
                        <th>Titel</th>
                        <th>Fällig am</th>
                        <th>Priorität</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($upcomingServices as $reminder)
                    <tr>
                        <td>{{ $reminder->title }}</td>
                        <td>
                            @if($reminder->due_date)
                                {{ \Carbon\Carbon::parse($reminder->due_date)->format('d.m.Y') }}
                            @elseif($reminder->due_mileage)
                                Bei {{ number_format($reminder->due_mileage, 0, ',', '.') }} km
                            @else
                                -
                            @endif
                        </td>
                        <td>
                            @switch($reminder->priority)
                                @case('high')
                                    <span class="badge badge-error">Hoch</span>
                                    @break
                                @case('medium')
                                    <span class="badge badge-warning">Mittel</span>
                                    @break
                                @case('low')
                                    <span class="badge">Niedrig</span>
                                    @break
                                @default
                                    {{ $reminder->priority }}
                            @endswitch
                        </td>
                        <td>
                            @switch($reminder->status)
                                @case('pending')
                                    <span class="badge">Ausstehend</span>
                                    @break
                                @case('completed')
                                    <span class="badge badge-success">Abgeschlossen</span>
                                    @break
                                @case('overdue')
                                    <span class="badge badge-error">Überfällig</span>
                                    @break
                                @default
                                    {{ $reminder->status }}
                            @endswitch
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <p>Keine anstehenden Servicerinnerungen vorhanden.</p>
        @endif
    </div>

    <div class="footer">
        <p>Dieser Bericht wurde automatisch erstellt. Alle Daten entsprechen dem Stand vom {{ date('d.m.Y H:i') }} Uhr.</p>
    </div>
</body>
</html>
