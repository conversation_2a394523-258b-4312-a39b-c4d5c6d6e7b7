<div>
    {{-- Knowing others is intelligence; knowing yourself is true wisdom. --}}
    <div class="px-4 py-6 lg:px-8">
        @if (session()->has('message'))
            <div class="alert alert-success mb-6">
                {{ session('message') }}
            </div>
        @endif

        @if ($errorMessage)
            <div class="alert alert-error mb-6">
                {{ $errorMessage }}
            </div>
        @endif

        <div class="space-y-8">
            @if (!$subscription)
                <!-- Subscription Plans -->
                <div>
                    <h2 class="text-xl font-semibold mb-4">Choose a Subscription Plan</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @foreach ($plans as $plan)
                            <div class="card bg-base-200 shadow-xl hover:shadow-2xl transition-all">
                                <div class="card-body">
                                    <h3 class="card-title text-xl">{{ $plan['name'] }}</h3>
                                    <div class="mt-2 mb-4">
                                        <span class="text-2xl font-bold">€{{ number_format($plan['price'], 2) }}</span>
                                        <span class="text-gray-500">/ {{ $plan['interval'] }}</span>
                                    </div>
                                    <div class="divider"></div>
                                    <ul class="space-y-2">
                                        @foreach ($plan['features'] as $feature)
                                            <li class="flex items-start">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                </svg>
                                                <span>{{ $feature }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                    <div class="card-actions mt-4">
                                        <button 
                                            wire:click="$set('selectedPlan', '{{ $plan['id'] }}')"
                                            class="btn btn-primary btn-block">
                                            Select Plan
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                @if ($selectedPlan)
                    <!-- Payment Form -->
                    <div class="card bg-base-200 shadow-xl p-6 mt-8">
                        <h2 class="text-xl font-semibold mb-4">Payment Details</h2>
                        <div x-data="{}" x-init="
                            const stripe = Stripe('{{ config('cashier.key') }}');
                            const elements = stripe.elements();
                            const cardElement = elements.create('card');
                            cardElement.mount('#card-element');
                            
                            // Handle form submission
                            document.getElementById('payment-form').addEventListener('submit', async (e) => {
                                e.preventDefault();
                                const { paymentMethod, error } = await stripe.createPaymentMethod('card', cardElement);
                                
                                if (error) {
                                    document.getElementById('card-errors').textContent = error.message;
                                } else {
                                    @this.set('paymentMethod', paymentMethod.id);
                                    @this.subscribe();
                                }
                            });
                        ">
                            <form id="payment-form" class="space-y-4">
                                <div>
                                    <label for="card-element" class="block text-sm font-medium mb-2">
                                        Credit or debit card
                                    </label>
                                    <div id="card-element" class="p-3 border rounded-md bg-base-100">
                                        <!-- Stripe Elements will be inserted here -->
                                    </div>
                                    <div id="card-errors" class="text-error text-sm mt-1"></div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    Subscribe Now
                                </button>
                            </form>
                        </div>
                    </div>
                @endif
            @else
                <!-- Active Subscription Info -->
                <div class="card bg-base-200 shadow-xl p-6">
                    <h2 class="text-xl font-semibold mb-4">Your Subscription</h2>
                    <div class="space-y-4">
                        <div>
                            <span class="font-medium">Plan:</span> 
                            <span class="badge badge-primary">
                                {{ $subscription->stripe_price ?? 'Unknown' }}
                            </span>
                        </div>
                        <div>
                            <span class="font-medium">Status:</span> 
                            <span class="badge {{ $subscription->active() ? 'badge-success' : 'badge-warning' }}">
                                {{ ucfirst($subscription->stripe_status) }}
                            </span>
                        </div>
                        @if ($subscription->onGracePeriod())
                            <div>
                                <span class="font-medium">Ends at:</span> 
                                {{ $subscription->ends_at->format('F j, Y') }}
                            </div>
                        @endif
                        @if ($subscription->cancelled())
                            <button wire:click="resumeSubscription" class="btn btn-success mt-4">
                                Resume Subscription
                            </button>
                        @else
                            <button wire:click="cancelSubscription" class="btn btn-error mt-4">
                                Cancel Subscription
                            </button>
                        @endif
                    </div>
                </div>

                <!-- Payment Methods -->
                @if ($paymentMethod)
                    <div class="card bg-base-200 shadow-xl p-6">
                        <h2 class="text-xl font-semibold mb-4">Payment Method</h2>
                        <div class="flex items-center space-x-4">
                            @if ($paymentMethod->card)
                                <div>
                                    <span class="text-xl">
                                        @switch($paymentMethod->card->brand)
                                            @case('visa')
                                                <i class="fa-brands fa-cc-visa"></i>
                                                @break
                                            @case('mastercard')
                                                <i class="fa-brands fa-cc-mastercard"></i>
                                                @break
                                            @case('amex')
                                                <i class="fa-brands fa-cc-amex"></i>
                                                @break
                                            @default
                                                <i class="fa-regular fa-credit-card"></i>
                                        @endswitch
                                    </span>
                                </div>
                                <div>
                                    <p>{{ ucfirst($paymentMethod->card->brand) }} ending in {{ $paymentMethod->card->last4 }}</p>
                                    <p class="text-gray-500 text-sm">Expires {{ $paymentMethod->card->exp_month }}/{{ $paymentMethod->card->exp_year }}</p>
                                </div>
                            @else
                                <p>Unknown payment method</p>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Invoices -->
                @if (count($invoices) > 0)
                    <div class="card bg-base-200 shadow-xl p-6">
                        <h2 class="text-xl font-semibold mb-4">Billing History</h2>
                        <div class="overflow-x-auto">
                            <table class="table table-zebra w-full">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($invoices as $invoice)
                                        <tr>
                                            <td>{{ $invoice->date()->format('F j, Y') }}</td>
                                            <td>{{ $invoice->total() }}</td>
                                            <td>
                                                <span class="badge {{ $invoice->paid ? 'badge-success' : 'badge-warning' }}">
                                                    {{ $invoice->paid ? 'Paid' : 'Unpaid' }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('cashier.invoice.download', $invoice->id) }}" class="btn btn-sm">
                                                    Download
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
            @endif
        </div>
    </div>

    {{-- Include Stripe.js --}}
    @push('scripts')
        <script src="https://js.stripe.com/v3/"></script>
    @endpush
</div>
