#!/bin/bash

# Create SelectTrigger.vue
cat > resources/js/components/ui/select/SelectTrigger.vue << 'EOL'
<template>
  <button @click="toggle">
    <slot />
  </button>
</template>

<script setup lang="ts">
import { inject } from 'vue';

defineOptions({
  name: 'SelectTrigger',
});

const isOpen = inject('isOpen', false);
const setIsOpen = inject('setIsOpen', (value: boolean) => {});

const toggle = () => {
  setIsOpen(!isOpen.value);
};
</script>
EOL

# Create SelectValue.vue
cat > resources/js/components/ui/select/SelectValue.vue << 'EOL'
<template>
  <span>
    <slot />
  </span>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SelectValue',
});
</script>
EOL

# Create SelectContent.vue
cat > resources/js/components/ui/select/SelectContent.vue << 'EOL'
<template>
  <div v-if="isOpen" class="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { inject } from 'vue';

defineOptions({
  name: 'SelectContent',
});

const isOpen = inject('isOpen', false);
</script>
EOL

# Create SelectItem.vue
cat > resources/js/components/ui/select/SelectItem.vue << 'EOL'
<template>
  <div
    @click="select"
    class="relative cursor-default select-none py-2 pl-10 pr-4 text-gray-900 hover:bg-gray-100"
    :class="{ 'font-medium': isSelected }"
  >
    <span class="block truncate">
      <slot />
    </span>
    <span v-if="isSelected" class="absolute inset-y-0 left-0 flex items-center pl-3 text-indigo-600">
      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
    </span>
  </div>
</template>

<script setup lang="ts">
import { inject, computed } from 'vue';

defineOptions({
  name: 'SelectItem',
});

const props = defineProps<{
  value: any;
}>();

const selectedValue = inject('selectedValue', null);
const updateValue = inject('updateValue', (value: any) => {});
const setIsOpen = inject('setIsOpen', (value: boolean) => {});

const isSelected = computed(() => selectedValue.value === props.value);

const select = () => {
  updateValue(props.value);
  setIsOpen(false);
};
</script>
EOL 