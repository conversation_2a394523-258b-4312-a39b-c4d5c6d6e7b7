# Laravel 12 + Vue.js Projektregeln

## 1. Allgemeine Projektstruktur

### Verzeichnisstruktur
- Halte dich an die Standard-Laravel-Verzeichnisstruktur
- Vue-Komponenten gehören in `resources/js/components`
- Vue-Seiten/Views gehören in `resources/js/pages`
- TypeScript-Interfaces und -Types in `resources/js/types`
- Composables in `resources/js/composables`

### Namenskonventionen
- **PHP-Klassen**: PascalCase (z.B. `UserController.php`)
- **PHP-Methoden**: camelCase (z.B. `getUserDetails()`)
- **Vue-Komponenten**: PascalCase (z.B. `UserProfile.vue`)
- **Blade Templates**: kebab-case (z.B. `user-profile.blade.php`)
- **JavaScript/Vue-Dateien**: PascalCase für Komponenten, camelCase für Utilities
- **TypeScript-Interfaces**: PascalCase mit "I"-Präfix (z.B. `IUserData`)
- **TypeScript-Types**: PascalCase (z.B. `UserRole`)
- **CSS/SCSS**: kebab-case (z.B. `user-profile.scss`)
- **Datenbank**: snake_case für Tabellen und Spalten (z.B. `user_profiles`, `first_name`)

## 2. Laravel-spezifische Regeln

### Controller
- Verwende Resource-Controller für CRUD-Operationen
- Jeder Controller sollte nur eine Ressource verwalten (Single Responsibility)
- Begrenze Controller-Methoden auf maximal 20 Zeilen
- Verwende FormRequests für Validierung
- Nutze Controller-Middleware für Berechtigungsprüfungen
- Verwende Invokable Controller für einzelne Aktionen

### Models
- Alle Beziehungen (Relations) dokumentieren
- Definiere `$fillable` oder `$guarded` für jedes Model
- Verwende Accessoren und Mutatoren für Datenformatierung
- Stelle Business-Logik als Model-Methoden bereit
- Nutze Scopes für häufig verwendete Abfragen
- Implementiere Factories für alle Models für Tests

### Migrations
- Aussagekräftige Namen (z.B. `create_users_table`, `add_role_to_users`)
- Definiere Fremdschlüssel und Indizes explizit
- Definiere eine `down()`-Methode für jede Migration
- Verwende Enum-Klassen für Status-Felder
- Nutze UUID statt Auto-Increment für IDs, wenn sinnvoll

### Routes
- Gruppiere Routen nach Funktionalität oder Modul
- Verwende benannte Routen
- Route-Model-Binding für Ressourcen-Routes
- Nutze Route-Caching in Produktionsumgebungen
- Verwende Route-Middleware für Berechtigungen

### Services & Repositories
- Implementiere komplexe Business-Logik in Service-Klassen
- Nutze Repositories für komplexe Datenbankoperationen
- Verwende Dependency Injection
- Implementiere Interfaces für Services und Repositories
- Nutze Laravel's Service Container für Dependency Injection

## 3. Vue.js-spezifische Regeln

### Komponenten
- Verwende Single-File-Components (.vue)
- Präfix für UI-Komponenten (z.B. `BaseButton.vue`, `BaseCard.vue`)
- Kopple Komponenten lose (geringe Abhängigkeiten)
- Maximiere die Wiederverwendbarkeit von Komponenten
- Nutze TypeScript für Komponenten-Props und Events
- Verwende `<script setup>` Syntax für einfachere Komponenten

### State Management
- Pinia für globales State Management
- Store-Module nach Funktionalität organisieren
- Keine direkten API-Aufrufe in Komponenten, nutze Actions
- Verwende Composables für wiederverwendbare Logik
- Nutze TypeScript für typsichere Stores

### API-Kommunikation
- Zentralisiere API-Calls in dedizierten Service-Dateien
- Verwende Axios für HTTP-Requests
- Definiere eine Base-URL und Interceptors für Requests/Responses
- Implementiere Request-Caching für häufige Anfragen
- Nutze TypeScript für API-Response-Typen

## 4. Code-Qualität

### Testing
- Feature-Tests für alle API-Endpunkte
- Unit-Tests für komplexe Business-Logik
- Vue-Komponenten mit Vitest testen
- CI/CD Pipeline mit automatisierten Tests
- Verwende Pest für PHP-Tests
- Implementiere End-to-End Tests mit Cypress für kritische Workflows

### Code-Style
- PSR-12 für PHP-Code
- ESLint und Prettier für JavaScript/Vue/TypeScript
- Nutze `.editorconfig` für konsistente Formatierung
- Husky für Pre-commit Hooks zur Codequalitätssicherung
- Verwende TypeScript für bessere Typsicherheit

### Dokumentation
- PHPDoc für Klassen und Methoden
- JSDoc für JavaScript-Funktionen
- README.md mit Projektbeschreibung und Setup-Anleitung
- Dokumentiere alle ENV-Variablen in `.env.example`
- Erstelle API-Dokumentation mit OpenAPI/Swagger

## 5. Performance & Sicherheit

### Performance
- Verwende Eager Loading für Eloquent-Beziehungen
- Optimiere Frontend-Assets mit Vite
- Implementiere Caching für langsame Operationen
- Verwende Queues für zeitintensive Tasks
- Nutze Laravel Octane für verbesserte Performance
- Implementiere Lazy Loading für Vue-Komponenten

### Sicherheit
- Validiere alle Benutzereingaben
- Verwende Laravel Sanctum für API-Authentifizierung
- XSS-Schutz durch korrektes Escaping
- CSRF-Schutz für alle Formulare
- Implementiere Rate Limiting für APIs
- Verwende Content Security Policy (CSP) Headers
- Aktiviere HTTP Strict Transport Security (HSTS)

## 6. Versionskontrolle & Zusammenarbeit

### Git-Workflow
- Feature-Branch-Workflow
- Pull-Requests für Code-Reviews
- Keine direkte Commits auf `main` oder `develop`
- Semantic Versioning für Releases
- Nutze GitHub Actions oder GitLab CI für automatisierte Tests

### Commit-Nachrichten
- Strukturierte Commit-Nachrichten: `type(scope): message`
- Typen: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`
- Halte Commits klein und fokussiert
- Verwende Conventional Commits für automatisierte Changelogs

## 7. Deployment & Umgebungen

### Umgebungen
- Definiere separate Umgebungen: `local`, `dev`, `staging`, `production`
- Umgebungsspezifische Konfigurationen in `.env`-Dateien
- CI/CD für automatisierte Deployments
- Verwende Docker für konsistente Entwicklungs- und Produktionsumgebungen

### Build-Prozess
- Vite für das Frontend-Asset-Building
- Optimiere Assets für Produktion (`npm run build`)
- Verwendet Semantic Versioning für Release-Markierungen
- Implementiere Progressive Web App (PWA) Features, wenn sinnvoll
- Nutze Build-Zeit-Optimierungen wie Tree-Shaking

## 8. Fehlerbehandlung & Logging

### Fehlerbehandlung
- Einheitliches Format für API-Fehlerantworten
- Benutzerdefinierte Exception-Handler
- Vue-Error-Boundaries für Frontend-Fehler
- Implementiere Retry-Mechanismen für kritische API-Calls
- Nutze Sentry oder ähnliche Tools für Fehlerüberwachung

### Logging
- Strukturiertes Logging mit Kontext
- Verwende Log-Levels angemessen
- Fehler in Produktionsumgebung überwachen
- Implementiere Request-ID für bessere Nachverfolgbarkeit
- Nutze Elasticsearch/Kibana oder ähnliche Tools für Log-Analyse

## 9. Dependencies & Packages

### PHP-Packages
- Dokumentiere jedes Package im README
- Halte Abhängigkeiten aktuell (regelmäßige Updates)
- Bevorzuge offizielle Laravel-Packages
- Führe Sicherheitsaudits für Abhängigkeiten durch
- Verwende Composer Scripts für häufige Aufgaben

### NPM-Packages
- Minimiere die Anzahl externer Abhängigkeiten
- Prüfe Packages auf Aktualität und Sicherheit
- Dokumentiere Package-Zweck im `package.json`
- Verwende npm audit für Sicherheitsüberprüfungen
- Nutze Lockfiles für konsistente Installationen

## 10. Barrierefreiheit & Internationalisierung

### Barrierefreiheit (A11y)
- Implementiere WCAG 2.1 AA Standards
- Verwende semantisches HTML
- Stelle ausreichenden Farbkontrast sicher
- Implementiere Keyboard-Navigation
- Teste mit Screen-Readern

### Internationalisierung (i18n)
- Verwende Laravel's Lokalisierungsfunktionen
- Extrahiere alle UI-Texte in Sprachdateien
- Unterstütze RTL-Sprachen, wenn erforderlich
- Implementiere Pluralisierung korrekt
- Verwende Vue-i18n für Frontend-Übersetzungen
