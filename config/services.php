<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    // Konfiguration für DAT Group API (HSN/TSN-Daten)
    'dat' => [
        'api_key' => env('DAT_API_KEY'),
        'base_url' => env('DAT_API_URL', 'https://api.dat.de/v1'),
    ],

    // Konfiguration für KBA API (deutsche Fahrzeugdaten)
    'kba' => [
        'username' => env('KBA_API_USERNAME'),
        'password' => env('KBA_API_PASSWORD'),
        'api_key' => env('KBA_API_KEY'),
        'base_url' => env('KBA_API_URL', 'https://www.regcheck.org.uk/api/reg.asmx'),
    ],

];
