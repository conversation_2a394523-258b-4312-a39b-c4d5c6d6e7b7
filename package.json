{"private": true, "type": "module", "scripts": {"build": "vite build", "build:prod": "vite build --mode production", "build:force": "vite build --emptyOutDir", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@iconify/tailwind": "^1.2.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.13.5", "@vue/eslint-config-typescript": "^14.3.0", "daisyui": "^5.0.9", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.9", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@inertiajs/vue3": "^2.0.0-beta.3", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^12.0.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "laravel-vite-plugin": "^1.0", "lucide": "^0.468.0", "lucide-vue-next": "^0.468.0", "radix-vue": "^1.9.11", "reka-ui": "^2.2.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^6.2.0", "vue": "^3.5.13", "vue-toastification": "^2.0.0-rc.5", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}