<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\VehicleDataController;
use App\Http\Controllers\QuickAccessEntryController;
use App\Http\Controllers\VehicleInspectionController;
use App\Http\Controllers\API\PriceComparisonController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Fahrzeugdaten-APIs
Route::prefix('vehicle-data')->group(function () {
    // HSN/TSN-Lookup
    Route::get('/', [VehicleDataController::class, 'getVehicleByHsnTsn'])
        ->middleware('throttle:60,1'); // Rate-Limiting: 60 Anfragen pro Minute

    // VIN-Decoder
    Route::get('/vin', [VehicleDataController::class, 'getVehicleByVin'])
        ->middleware('throttle:60,1');
});

// Quick Access Entries API Routes
// Route::apiResource('vehicles.quick-access-entries', QuickAccessEntryController::class);

// Quick Access Entries API routes - temporarily without auth for testing
Route::get('vehicles/{vehicle}/quick-access-entries', [QuickAccessEntryController::class, 'index']);
Route::post('vehicles/{vehicle}/quick-access-entries', [QuickAccessEntryController::class, 'store']);
Route::get('vehicles/{vehicle}/quick-access-entries/{quickAccessEntryId}', [QuickAccessEntryController::class, 'show']);
Route::put('vehicles/{vehicle}/quick-access-entries/{quickAccessEntryId}', [QuickAccessEntryController::class, 'update']);
Route::delete('vehicles/{vehicle}/quick-access-entries/{quickAccessEntryId}', [QuickAccessEntryController::class, 'destroy']);

// Vehicle inspection routes
Route::get('vehicles/{vehicle}/latest-inspection', [VehicleInspectionController::class, 'getLatestInspection']);

// Public debugger route for quick access entries
Route::get('debug/vehicles/{vehicleId}/quick-access-entries', [QuickAccessEntryController::class, 'getEntriesForVehicle']);

// Preisvergleich-Endpunkt
Route::get('/parts/price-comparison', [PriceComparisonController::class, 'getComparison'])
    ->name('api.parts.price-comparison');

// Simple test route
Route::get('test', function () {
    return response()->json(['message' => 'API is working']);
});
