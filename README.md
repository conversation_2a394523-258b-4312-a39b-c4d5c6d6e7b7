# DSGVO-konformes Cookie-Consent für Fahrzeugakte.app

Diese Implementierung enthält ein DSGVO-konformes Cookie-Consent-Banner mit vollständiger Funktionalität zur Einholung, Speicherung und Verwaltung von Cookie-Einwilligungen.

## Features

- Vollständig DSGVO-konformes Cookie-Banner
- Unterstützung für verschiedene Cookie-Kategorien (notwendig, funktional, analytisch, Marketing)
- Detaillierte Einstellungsmöglichkeiten für Benutzer
- Persistente Speicherung der Einstellungen im Local Storage
- Composable für einfachen Zugriff auf Cookie-Einstellungen in anderen Komponenten
- Dark Mode / Light Mode Support
- Responsive Design
- Schwebender Button zum erneuten Öffnen der Einstellungen

## Verwendung

### 1. Grundlegende Integration

Die Komponente wird automatisch in der App initialisiert und im DOM außerhalb der Vue-Anwendungswurzel gemountet, sodass sie überall verfügbar ist.

### 2. Zugriff auf Cookie-Einstellungen in anderen Komponenten

Verwenden Sie den `useCookieConsent`-Composable, um auf die Cookie-Einstellungen zuzugreifen:

```vue
<script setup>
import { useCookieConsent } from '@/composables/useCookieConsent';

const { isCategoryEnabled, reopenCookieSettings } = useCookieConsent();

// Prüfen, ob eine bestimmte Kategorie aktiviert ist
const canUseAnalytics = isCategoryEnabled('analytics');

// Einstellungen erneut öffnen
const openSettings = () => {
  reopenCookieSettings();
};
</script>
```

### 3. Conditional Loading von Scripts

Um Scripts nur dann zu laden, wenn die entsprechende Einwilligung vorliegt:

```vue
<script setup>
import { onMounted } from 'vue';
import { useCookieConsent } from '@/composables/useCookieConsent';

const { runIfCategoryEnabled } = useCookieConsent();

onMounted(() => {
  // Lädt Analytics nur, wenn die Einwilligung vorliegt
  runIfCategoryEnabled('analytics', () => {
    // Google Analytics oder andere Tracking-Skripte laden
    const script = document.createElement('script');
    script.src = 'https://www.googletagmanager.com/gtag/js?id=UA-XXXXXXXX-X';
    document.head.appendChild(script);
    
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'UA-XXXXXXXX-X');
  });
});
</script>
```

## Anpassung

### Cookie-Kategorien anpassen

Die verfügbaren Cookie-Kategorien können in der Datei `resources/js/components/CookieConsent.vue` angepasst werden:

```javascript
const cookieCategories = ref<CookieCategory[]>([
  {
    id: 'necessary',
    name: 'Notwendig',
    description: 'Diese Cookies sind für die Grundfunktionen der Website erforderlich...',
    required: true, // Kann nicht deaktiviert werden
    enabled: true,
  },
  // Weitere Kategorien hinzufügen...
]);
```

### Styling anpassen

Die Komponente verwendet TailwindCSS und passt sich automatisch an das Light/Dark-Theme der Anwendung an. Spezifische Anpassungen können direkt in der Komponente vorgenommen werden.

## Wichtige Funktionen

- `isCategoryEnabled(categoryId)`: Prüft, ob eine bestimmte Cookie-Kategorie aktiviert ist
- `reopenCookieSettings()`: Öffnet das Cookie-Banner erneut
- `runIfCategoryEnabled(categoryId, callback)`: Führt eine Funktion nur aus, wenn die Einwilligung für eine bestimmte Kategorie vorliegt

## Event-System

Die Komponente verwendet benutzerdefinierte Events zur Kommunikation:

- `cookie-consent-updated`: Wird ausgelöst, wenn sich die Cookie-Einstellungen ändern
- `reopen-cookie-settings`: Wird verwendet, um das Cookie-Banner erneut zu öffnen

## Datenschutz-Hinweis

Diese Implementierung erfüllt die grundlegenden Anforderungen der DSGVO für Cookie-Consent. Bitte stellen Sie sicher, dass der Text und die Kategorien den spezifischen Anforderungen Ihrer Website und den geltenden Gesetzen entsprechen. 